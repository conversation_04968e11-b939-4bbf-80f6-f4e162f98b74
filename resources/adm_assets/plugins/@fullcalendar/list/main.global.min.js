/*!
FullCalendar v5.10.1
Docs & License: https://fullcalendar.io/
(c) 2021 <PERSON>
*/
var FullCalendarList=function(e,t){"use strict";var n=function(e,t){return(n=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n])})(e,t)};function a(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Class extends value "+String(t)+" is not a constructor or null");function a(){this.constructor=e}n(e,t),e.prototype=null===t?Object.create(t):(a.prototype=t.prototype,new a)}var r=function(){return(r=Object.assign||function(e){for(var t,n=1,a=arguments.length;n<a;n++)for(var r in t=arguments[n])Object.prototype.hasOwnProperty.call(t,r)&&(e[r]=t[r]);return e}).apply(this,arguments)},i=function(e){function n(){var n=null!==e&&e.apply(this,arguments)||this;return n.state={textId:t.getUniqueDomId()},n}return a(n,e),n.prototype.render=function(){var e=this.context,n=e.theme,a=e.dateEnv,i=e.options,s=e.viewApi,l=this.props,d=l.cellId,c=l.dayDate,u=l.todayRange,m=this.state.textId,v=t.getDateMeta(c,u),y=i.listDayFormat?a.format(c,i.listDayFormat):"",f=i.listDaySideFormat?a.format(c,i.listDaySideFormat):"",g=r({date:a.toDate(c),view:s,textId:m,text:y,sideText:f,navLinkAttrs:t.buildNavLinkAttrs(this.context,c),sideNavLinkAttrs:t.buildNavLinkAttrs(this.context,c,"day",!1)},v),p=["fc-list-day"].concat(t.getDayClassNames(v,n));return t.createElement(t.RenderHook,{hookProps:g,classNames:i.dayHeaderClassNames,content:i.dayHeaderContent,defaultContent:o,didMount:i.dayHeaderDidMount,willUnmount:i.dayHeaderWillUnmount},(function(e,a,r,i){return t.createElement("tr",{ref:e,className:p.concat(a).join(" "),"data-date":t.formatDayString(c)},t.createElement("th",{scope:"colgroup",colSpan:3,id:d,"aria-labelledby":m},t.createElement("div",{className:"fc-list-day-cushion "+n.getClass("tableCellShaded"),ref:r},i)))}))},n}(t.BaseComponent);function o(e){return t.createElement(t.Fragment,null,e.text&&t.createElement("a",r({id:e.textId,className:"fc-list-day-text"},e.navLinkAttrs),e.text),e.sideText&&t.createElement("a",r({"aria-hidden":!0,className:"fc-list-day-side-text"},e.sideNavLinkAttrs),e.sideText))}var s=t.createFormatter({hour:"numeric",minute:"2-digit",meridiem:"short"}),l=function(e){function n(){return null!==e&&e.apply(this,arguments)||this}return a(n,e),n.prototype.render=function(){var e=this.props,n=this.context,a=e.seg,i=e.timeHeaderId,o=e.eventHeaderId,l=e.dateHeaderId,c=n.options.eventTimeFormat||s;return t.createElement(t.EventRoot,{seg:a,timeText:"",disableDragging:!0,disableResizing:!0,defaultContent:function(){return function(e,n){var a=t.getSegAnchorAttrs(e,n);return t.createElement("a",r({},a),e.eventRange.def.title)}(a,n)},isPast:e.isPast,isFuture:e.isFuture,isToday:e.isToday,isSelected:e.isSelected,isDragging:e.isDragging,isResizing:e.isResizing,isDateSelecting:e.isDateSelecting},(function(e,r,s,u,m){return t.createElement("tr",{className:["fc-list-event",m.event.url?"fc-event-forced-url":""].concat(r).join(" "),ref:e},function(e,n,a,r,i){var o=a.options;if(!1!==o.displayEventTime){var s=e.eventRange.def,l=e.eventRange.instance,c=!1,u=void 0;if(s.allDay?c=!0:t.isMultiDayRange(e.eventRange.range)?e.isStart?u=t.buildSegTimeText(e,n,a,null,null,l.range.start,e.end):e.isEnd?u=t.buildSegTimeText(e,n,a,null,null,e.start,l.range.end):c=!0:u=t.buildSegTimeText(e,n,a),c){var m={text:a.options.allDayText,view:a.viewApi};return t.createElement(t.RenderHook,{hookProps:m,classNames:o.allDayClassNames,content:o.allDayContent,defaultContent:d,didMount:o.allDayDidMount,willUnmount:o.allDayWillUnmount},(function(e,n,a,o){return t.createElement("td",{ref:e,headers:r+" "+i,className:["fc-list-event-time"].concat(n).join(" ")},o)}))}return t.createElement("td",{className:"fc-list-event-time"},u)}return null}(a,c,n,i,l),t.createElement("td",{"aria-hidden":!0,className:"fc-list-event-graphic"},t.createElement("span",{className:"fc-list-event-dot",style:{borderColor:m.borderColor||m.backgroundColor}})),t.createElement("td",{ref:s,headers:o+" "+l,className:"fc-list-event-title"},u))}))},n}(t.BaseComponent);function d(e){return e.text}var c=function(e){function n(){var n=null!==e&&e.apply(this,arguments)||this;return n.computeDateVars=t.memoize(m),n.eventStoreToSegs=t.memoize(n._eventStoreToSegs),n.state={timeHeaderId:t.getUniqueDomId(),eventHeaderId:t.getUniqueDomId(),dateHeaderIdRoot:t.getUniqueDomId()},n.setRootEl=function(e){e?n.context.registerInteractiveComponent(n,{el:e}):n.context.unregisterInteractiveComponent(n)},n}return a(n,e),n.prototype.render=function(){var e=this,n=this.props,a=this.context,r=["fc-list",a.theme.getClass("table"),!1!==a.options.stickyHeaderDates?"fc-list-sticky":""],i=this.computeDateVars(n.dateProfile),o=i.dayDates,s=i.dayRanges,l=this.eventStoreToSegs(n.eventStore,n.eventUiBases,s);return t.createElement(t.ViewRoot,{viewSpec:a.viewSpec,elRef:this.setRootEl},(function(a,i){return t.createElement("div",{ref:a,className:r.concat(i).join(" ")},t.createElement(t.Scroller,{liquid:!n.isHeightAuto,overflowX:n.isHeightAuto?"visible":"hidden",overflowY:n.isHeightAuto?"visible":"auto"},l.length>0?e.renderSegList(l,o):e.renderEmptyMessage()))}))},n.prototype.renderEmptyMessage=function(){var e=this.context,n=e.options,a=e.viewApi,r={text:n.noEventsText,view:a};return t.createElement(t.RenderHook,{hookProps:r,classNames:n.noEventsClassNames,content:n.noEventsContent,defaultContent:u,didMount:n.noEventsDidMount,willUnmount:n.noEventsWillUnmount},(function(e,n,a,r){return t.createElement("div",{className:["fc-list-empty"].concat(n).join(" "),ref:e},t.createElement("div",{className:"fc-list-empty-cushion",ref:a},r))}))},n.prototype.renderSegList=function(e,n){var a=this.context,o=a.theme,s=a.options,d=this.state,c=d.timeHeaderId,u=d.eventHeaderId,m=d.dateHeaderIdRoot,v=function(e){var t,n,a=[];for(t=0;t<e.length;t+=1)(a[(n=e[t]).dayIndex]||(a[n.dayIndex]=[])).push(n);return a}(e);return t.createElement(t.NowTimer,{unit:"day"},(function(e,a){for(var d=[],y=0;y<v.length;y+=1){var f=v[y];if(f){var g=t.formatDayString(n[y]),p=m+"-"+g;d.push(t.createElement(i,{key:g,cellId:p,dayDate:n[y],todayRange:a}));for(var h=0,E=f=t.sortEventSegs(f,s.eventOrder);h<E.length;h++){var D=E[h];d.push(t.createElement(l,r({key:g+":"+D.eventRange.instance.instanceId,seg:D,isDragging:!1,isResizing:!1,isDateSelecting:!1,isSelected:!1,timeHeaderId:c,eventHeaderId:u,dateHeaderId:p},t.getSegMeta(D,a,e))))}}}return t.createElement("table",{className:"fc-list-table "+o.getClass("table")},t.createElement("thead",null,t.createElement("tr",null,t.createElement("th",{scope:"col",id:c},s.timeHint),t.createElement("th",{scope:"col","aria-hidden":!0}),t.createElement("th",{scope:"col",id:u},s.eventHint))),t.createElement("tbody",null,d))}))},n.prototype._eventStoreToSegs=function(e,n,a){return this.eventRangesToSegs(t.sliceEventStore(e,n,this.props.dateProfile.activeRange,this.context.options.nextDayThreshold).fg,a)},n.prototype.eventRangesToSegs=function(e,t){for(var n=[],a=0,r=e;a<r.length;a++){var i=r[a];n.push.apply(n,this.eventRangeToSegs(i,t))}return n},n.prototype.eventRangeToSegs=function(e,n){var a,r,i,o=this.context.dateEnv,s=this.context.options.nextDayThreshold,l=e.range,d=e.def.allDay,c=[];for(a=0;a<n.length;a+=1)if((r=t.intersectRanges(l,n[a]))&&(i={component:this,eventRange:e,start:r.start,end:r.end,isStart:e.isStart&&r.start.valueOf()===l.start.valueOf(),isEnd:e.isEnd&&r.end.valueOf()===l.end.valueOf(),dayIndex:a},c.push(i),!i.isEnd&&!d&&a+1<n.length&&l.end<o.add(n[a+1].start,s))){i.end=l.end,i.isEnd=!0;break}return c},n}(t.DateComponent);function u(e){return e.text}function m(e){for(var n=t.startOfDay(e.renderRange.start),a=e.renderRange.end,r=[],i=[];n<a;)r.push(n),i.push({start:n,end:t.addDays(n,1)}),n=t.addDays(n,1);return{dayDates:r,dayRanges:i}}var v={listDayFormat:y,listDaySideFormat:y,noEventsClassNames:t.identity,noEventsContent:t.identity,noEventsDidMount:t.identity,noEventsWillUnmount:t.identity};function y(e){return!1===e?null:t.createFormatter(e)}var f=t.createPlugin({optionRefiners:v,views:{list:{component:c,buttonTextKey:"list",listDayFormat:{month:"long",day:"numeric",year:"numeric"}},listDay:{type:"list",duration:{days:1},listDayFormat:{weekday:"long"}},listWeek:{type:"list",duration:{weeks:1},listDayFormat:{weekday:"long"},listDaySideFormat:{month:"long",day:"numeric",year:"numeric"}},listMonth:{type:"list",duration:{month:1},listDaySideFormat:{weekday:"long"}},listYear:{type:"list",duration:{year:1},listDaySideFormat:{weekday:"long"}}}});return t.globalPlugins.push(f),e.ListView=c,e.default=f,Object.defineProperty(e,"__esModule",{value:!0}),e}({},FullCalendar);