{"version": 3, "file": "main.js", "sources": ["src/Calendar.tsx"], "sourcesContent": ["import {\n  CalendarOptions, Action, CalendarContent, render, createElement, DelayedRunner, CssDimValue, applyStyleProp,\n  CalendarApi, CalendarRoot, isArraysEqual, CalendarDataManager, CalendarData,\n  CustomContentRenderContext, flushToDom, unmountComponentAtNode,\n} from '@fullcalendar/common'\n\nexport class Calendar extends CalendarApi {\n  currentData: CalendarData\n  renderRunner: DelayedRunner\n  el: HTMLElement\n  isRendering = false\n  isRendered = false\n  currentClassNames: string[] = []\n  customContentRenderId = 0 // will affect custom generated classNames?\n\n  get view() { return this.currentData.viewApi } // for public API\n\n  constructor(el: HTMLElement, optionOverrides: CalendarOptions = {}) {\n    super()\n\n    this.el = el\n    this.renderRunner = new DelayedRunner(this.handleRenderRequest)\n\n    new CalendarDataManager({ // eslint-disable-line no-new\n      optionOverrides,\n      calendarApi: this,\n      onAction: this.handleAction,\n      onData: this.handleData,\n    })\n  }\n\n  handleAction = (action: Action) => {\n    // actions we know we want to render immediately\n    switch (action.type) {\n      case 'SET_EVENT_DRAG':\n      case 'SET_EVENT_RESIZE':\n        this.renderRunner.tryDrain()\n    }\n  }\n\n  handleData = (data: CalendarData) => {\n    this.currentData = data\n    this.renderRunner.request(data.calendarOptions.rerenderDelay)\n  }\n\n  handleRenderRequest = () => {\n    if (this.isRendering) {\n      this.isRendered = true\n      let { currentData } = this\n\n      render(\n        <CalendarRoot options={currentData.calendarOptions} theme={currentData.theme} emitter={currentData.emitter}>\n          {(classNames, height, isHeightAuto, forPrint) => {\n            this.setClassNames(classNames)\n            this.setHeight(height)\n\n            return (\n              <CustomContentRenderContext.Provider value={this.customContentRenderId}>\n                <CalendarContent\n                  isHeightAuto={isHeightAuto}\n                  forPrint={forPrint}\n                  {...currentData}\n                />\n              </CustomContentRenderContext.Provider>\n            )\n          }}\n        </CalendarRoot>,\n        this.el,\n      )\n    } else if (this.isRendered) {\n      this.isRendered = false\n      unmountComponentAtNode(this.el)\n      this.setClassNames([])\n      this.setHeight('')\n    }\n\n    flushToDom()\n  }\n\n  render() {\n    let wasRendering = this.isRendering\n\n    if (!wasRendering) {\n      this.isRendering = true\n    } else {\n      this.customContentRenderId += 1\n    }\n\n    this.renderRunner.request()\n\n    if (wasRendering) {\n      this.updateSize()\n    }\n  }\n\n  destroy() {\n    if (this.isRendering) {\n      this.isRendering = false\n      this.renderRunner.request()\n    }\n  }\n\n  updateSize() {\n    super.updateSize()\n    flushToDom()\n  }\n\n  batchRendering(func) {\n    this.renderRunner.pause('batchRendering')\n    func()\n    this.renderRunner.resume('batchRendering')\n  }\n\n  pauseRendering() { // available to plugins\n    this.renderRunner.pause('pauseRendering')\n  }\n\n  resumeRendering() { // available to plugins\n    this.renderRunner.resume('pauseRendering', true)\n  }\n\n  resetOptions(optionOverrides, append?) {\n    this.currentDataManager.resetOptions(optionOverrides, append)\n  }\n\n  setClassNames(classNames: string[]) {\n    if (!isArraysEqual(classNames, this.currentClassNames)) {\n      let { classList } = this.el\n\n      for (let className of this.currentClassNames) {\n        classList.remove(className)\n      }\n\n      for (let className of classNames) {\n        classList.add(className)\n      }\n\n      this.currentClassNames = classNames\n    }\n  }\n\n  setHeight(height: CssDimValue) {\n    applyStyleProp(this.el, 'height', height)\n  }\n}\n"], "names": [], "mappings": ";;;;;;;;;;;IAM8B,4BAAW;IAWvC,kBAAY,EAAe,EAAE,eAAqC;QAArC,gCAAA,EAAA,oBAAqC;QAAlE,YACE,iBAAO,SAWR;QAnBD,iBAAW,GAAG,KAAK,CAAA;QACnB,gBAAU,GAAG,KAAK,CAAA;QAClB,uBAAiB,GAAa,EAAE,CAAA;QAChC,2BAAqB,GAAG,CAAC,CAAA;QAkBzB,kBAAY,GAAG,UAAC,MAAc;;YAE5B,QAAQ,MAAM,CAAC,IAAI;gBACjB,KAAK,gBAAgB,CAAC;gBACtB,KAAK,kBAAkB;oBACrB,KAAI,CAAC,YAAY,CAAC,QAAQ,EAAE,CAAA;aAC/B;SACF,CAAA;QAED,gBAAU,GAAG,UAAC,IAAkB;YAC9B,KAAI,CAAC,WAAW,GAAG,IAAI,CAAA;YACvB,KAAI,CAAC,YAAY,CAAC,OAAO,CAAC,IAAI,CAAC,eAAe,CAAC,aAAa,CAAC,CAAA;SAC9D,CAAA;QAED,yBAAmB,GAAG;YACpB,IAAI,KAAI,CAAC,WAAW,EAAE;gBACpB,KAAI,CAAC,UAAU,GAAG,IAAI,CAAA;gBAChB,IAAA,aAAW,GAAK,KAAI,YAAT,CAAS;gBAE1B,MAAM,CACJ,cAAC,YAAY,IAAC,OAAO,EAAE,aAAW,CAAC,eAAe,EAAE,KAAK,EAAE,aAAW,CAAC,KAAK,EAAE,OAAO,EAAE,aAAW,CAAC,OAAO,IACvG,UAAC,UAAU,EAAE,MAAM,EAAE,YAAY,EAAE,QAAQ;oBAC1C,KAAI,CAAC,aAAa,CAAC,UAAU,CAAC,CAAA;oBAC9B,KAAI,CAAC,SAAS,CAAC,MAAM,CAAC,CAAA;oBAEtB,QACE,cAAC,0BAA0B,CAAC,QAAQ,IAAC,KAAK,EAAE,KAAI,CAAC,qBAAqB;wBACpE,cAAC,eAAe,aACd,YAAY,EAAE,YAAY,EAC1B,QAAQ,EAAE,QAAQ,IACd,aAAW,EACf,CACkC,EACvC;iBACF,CACY,EACf,KAAI,CAAC,EAAE,CACR,CAAA;aACF;iBAAM,IAAI,KAAI,CAAC,UAAU,EAAE;gBAC1B,KAAI,CAAC,UAAU,GAAG,KAAK,CAAA;gBACvB,sBAAsB,CAAC,KAAI,CAAC,EAAE,CAAC,CAAA;gBAC/B,KAAI,CAAC,aAAa,CAAC,EAAE,CAAC,CAAA;gBACtB,KAAI,CAAC,SAAS,CAAC,EAAE,CAAC,CAAA;aACnB;YAED,UAAU,EAAE,CAAA;SACb,CAAA;QAzDC,KAAI,CAAC,EAAE,GAAG,EAAE,CAAA;QACZ,KAAI,CAAC,YAAY,GAAG,IAAI,aAAa,CAAC,KAAI,CAAC,mBAAmB,CAAC,CAAA;QAE/D,IAAI,mBAAmB,CAAC;YACtB,eAAe,iBAAA;YACf,WAAW,EAAE,KAAI;YACjB,QAAQ,EAAE,KAAI,CAAC,YAAY;YAC3B,MAAM,EAAE,KAAI,CAAC,UAAU;SACxB,CAAC,CAAA;;KACH;IAdD,sBAAI,0BAAI;aAAR,cAAa,OAAO,IAAI,CAAC,WAAW,CAAC,OAAO,CAAA,EAAE;;;;OAAA;IAgE9C,yBAAM,GAAN;QACE,IAAI,YAAY,GAAG,IAAI,CAAC,WAAW,CAAA;QAEnC,IAAI,CAAC,YAAY,EAAE;YACjB,IAAI,CAAC,WAAW,GAAG,IAAI,CAAA;SACxB;aAAM;YACL,IAAI,CAAC,qBAAqB,IAAI,CAAC,CAAA;SAChC;QAED,IAAI,CAAC,YAAY,CAAC,OAAO,EAAE,CAAA;QAE3B,IAAI,YAAY,EAAE;YAChB,IAAI,CAAC,UAAU,EAAE,CAAA;SAClB;KACF;IAED,0BAAO,GAAP;QACE,IAAI,IAAI,CAAC,WAAW,EAAE;YACpB,IAAI,CAAC,WAAW,GAAG,KAAK,CAAA;YACxB,IAAI,CAAC,YAAY,CAAC,OAAO,EAAE,CAAA;SAC5B;KACF;IAED,6BAAU,GAAV;QACE,iBAAM,UAAU,WAAE,CAAA;QAClB,UAAU,EAAE,CAAA;KACb;IAED,iCAAc,GAAd,UAAe,IAAI;QACjB,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,gBAAgB,CAAC,CAAA;QACzC,IAAI,EAAE,CAAA;QACN,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC,gBAAgB,CAAC,CAAA;KAC3C;IAED,iCAAc,GAAd;QACE,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,gBAAgB,CAAC,CAAA;KAC1C;IAED,kCAAe,GAAf;QACE,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC,gBAAgB,EAAE,IAAI,CAAC,CAAA;KACjD;IAED,+BAAY,GAAZ,UAAa,eAAe,EAAE,MAAO;QACnC,IAAI,CAAC,kBAAkB,CAAC,YAAY,CAAC,eAAe,EAAE,MAAM,CAAC,CAAA;KAC9D;IAED,gCAAa,GAAb,UAAc,UAAoB;QAChC,IAAI,CAAC,aAAa,CAAC,UAAU,EAAE,IAAI,CAAC,iBAAiB,CAAC,EAAE;YAChD,IAAA,SAAS,GAAK,IAAI,CAAC,EAAE,UAAZ,CAAY;YAE3B,KAAsB,UAAsB,EAAtB,KAAA,IAAI,CAAC,iBAAiB,EAAtB,cAAsB,EAAtB,IAAsB,EAAE;gBAAzC,IAAI,SAAS,SAAA;gBAChB,SAAS,CAAC,MAAM,CAAC,SAAS,CAAC,CAAA;aAC5B;YAED,KAAsB,UAAU,EAAV,yBAAU,EAAV,wBAAU,EAAV,IAAU,EAAE;gBAA7B,IAAI,SAAS,mBAAA;gBAChB,SAAS,CAAC,GAAG,CAAC,SAAS,CAAC,CAAA;aACzB;YAED,IAAI,CAAC,iBAAiB,GAAG,UAAU,CAAA;SACpC;KACF;IAED,4BAAS,GAAT,UAAU,MAAmB;QAC3B,cAAc,CAAC,IAAI,CAAC,EAAE,EAAE,QAAQ,EAAE,MAAM,CAAC,CAAA;KAC1C;IACH,eAAC;AAAD,CA1IA,CAA8B,WAAW;;;;"}