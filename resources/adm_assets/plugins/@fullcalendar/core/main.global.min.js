/*!
FullCalendar v5.10.1
Docs & License: https://fullcalendar.io/
(c) 2021 <PERSON>
*/
var FullCalendar=function(e){"use strict";var t=function(e,n){return(t=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n])})(e,n)};function n(e,n){if("function"!=typeof n&&null!==n)throw new TypeError("Class extends value "+String(n)+" is not a constructor or null");function r(){this.constructor=e}t(e,n),e.prototype=null===n?Object.create(n):(r.prototype=n.prototype,new r)}var r=function(){return(r=Object.assign||function(e){for(var t,n=1,r=arguments.length;n<r;n++)for(var o in t=arguments[n])Object.prototype.hasOwnProperty.call(t,o)&&(e[o]=t[o]);return e}).apply(this,arguments)};function o(e,t,n){if(n||2===arguments.length)for(var r,o=0,i=t.length;o<i;o++)!r&&o in t||(r||(r=Array.prototype.slice.call(t,0,o)),r[o]=t[o]);return e.concat(r||t)}var i,a,s,l,u,c={},d=[],f=/acit|ex(?:s|g|n|p|$)|rph|grid|ows|mnc|ntw|ine[ch]|zoo|^ord|itera/i;function p(e,t){for(var n in t)e[n]=t[n];return e}function h(e){var t=e.parentNode;t&&t.removeChild(e)}function v(e,t,n){var r,o,i,a=arguments,s={};for(i in t)"key"==i?r=t[i]:"ref"==i?o=t[i]:s[i]=t[i];if(arguments.length>3)for(n=[n],i=3;i<arguments.length;i++)n.push(a[i]);if(null!=n&&(s.children=n),"function"==typeof e&&null!=e.defaultProps)for(i in e.defaultProps)void 0===s[i]&&(s[i]=e.defaultProps[i]);return m(e,s,r,o,null)}function m(e,t,n,r,o){var a={type:e,props:t,key:n,ref:r,__k:null,__:null,__b:0,__e:null,__d:void 0,__c:null,__h:null,constructor:void 0,__v:null==o?++i.__v:o};return null!=i.vnode&&i.vnode(a),a}function g(e){return e.children}function y(e,t){this.props=e,this.context=t}function _(e,t){if(null==t)return e.__?_(e.__,e.__.__k.indexOf(e)+1):null;for(var n;t<e.__k.length;t++)if(null!=(n=e.__k[t])&&null!=n.__e)return n.__e;return"function"==typeof e.type?_(e):null}function b(e){var t,n;if(null!=(e=e.__)&&null!=e.__c){for(e.__e=e.__c.base=null,t=0;t<e.__k.length;t++)if(null!=(n=e.__k[t])&&null!=n.__e){e.__e=e.__c.base=n.__e;break}return b(e)}}function E(e){(!e.__d&&(e.__d=!0)&&a.push(e)&&!C.__r++||l!==i.debounceRendering)&&((l=i.debounceRendering)||s)(C)}function C(){for(var e;C.__r=a.length;)e=a.sort((function(e,t){return e.__v.__b-t.__v.__b})),a=[],e.some((function(e){var t,n,r,o,i,a;e.__d&&(i=(o=(t=e).__v).__e,(a=t.__P)&&(n=[],(r=p({},o)).__v=o.__v+1,x(a,o,r,t.__n,void 0!==a.ownerSVGElement,null!=o.__h?[i]:null,n,null==i?_(o):i,o.__h),O(n,o),o.__e!=i&&b(o)))}))}function D(e,t,n,r,o,i,a,s,l,u){var f,p,h,v,y,b,E,C=r&&r.__k||d,D=C.length;for(n.__k=[],f=0;f<t.length;f++)if(null!=(v=n.__k[f]=null==(v=t[f])||"boolean"==typeof v?null:"string"==typeof v||"number"==typeof v||"bigint"==typeof v?m(null,v,null,null,v):Array.isArray(v)?m(g,{children:v},null,null,null):v.__b>0?m(v.type,v.props,v.key,null,v.__v):v)){if(v.__=n,v.__b=n.__b+1,null===(h=C[f])||h&&v.key==h.key&&v.type===h.type)C[f]=void 0;else for(p=0;p<D;p++){if((h=C[p])&&v.key==h.key&&v.type===h.type){C[p]=void 0;break}h=null}x(e,v,h=h||c,o,i,a,s,l,u),y=v.__e,(p=v.ref)&&h.ref!=p&&(E||(E=[]),h.ref&&E.push(h.ref,null,v),E.push(p,v.__c||y,v)),null!=y?(null==b&&(b=y),"function"==typeof v.type&&null!=v.__k&&v.__k===h.__k?v.__d=l=S(v,l,e):l=T(e,v,h,C,y,l),u||"option"!==n.type?"function"==typeof n.type&&(n.__d=l):e.value=""):l&&h.__e==l&&l.parentNode!=e&&(l=_(h))}for(n.__e=b,f=D;f--;)null!=C[f]&&("function"==typeof n.type&&null!=C[f].__e&&C[f].__e==n.__d&&(n.__d=_(r,f+1)),H(C[f],C[f]));if(E)for(f=0;f<E.length;f++)N(E[f],E[++f],E[++f])}function S(e,t,n){var r,o;for(r=0;r<e.__k.length;r++)(o=e.__k[r])&&(o.__=e,t="function"==typeof o.type?S(o,t,n):T(n,o,o,e.__k,o.__e,t));return t}function w(e,t){return t=t||[],null==e||"boolean"==typeof e||(Array.isArray(e)?e.some((function(e){w(e,t)})):t.push(e)),t}function T(e,t,n,r,o,i){var a,s,l;if(void 0!==t.__d)a=t.__d,t.__d=void 0;else if(null==n||o!=i||null==o.parentNode)e:if(null==i||i.parentNode!==e)e.appendChild(o),a=null;else{for(s=i,l=0;(s=s.nextSibling)&&l<r.length;l+=2)if(s==o)break e;e.insertBefore(o,i),a=i}return void 0!==a?a:o.nextSibling}function R(e,t,n){"-"===t[0]?e.setProperty(t,n):e[t]=null==n?"":"number"!=typeof n||f.test(t)?n:n+"px"}function k(e,t,n,r,o){var i;e:if("style"===t)if("string"==typeof n)e.style.cssText=n;else{if("string"==typeof r&&(e.style.cssText=r=""),r)for(t in r)n&&t in n||R(e.style,t,"");if(n)for(t in n)r&&n[t]===r[t]||R(e.style,t,n[t])}else if("o"===t[0]&&"n"===t[1])i=t!==(t=t.replace(/Capture$/,"")),t=t.toLowerCase()in e?t.toLowerCase().slice(2):t.slice(2),e.l||(e.l={}),e.l[t+i]=n,n?r||e.addEventListener(t,i?M:I,i):e.removeEventListener(t,i?M:I,i);else if("dangerouslySetInnerHTML"!==t){if(o)t=t.replace(/xlink[H:h]/,"h").replace(/sName$/,"s");else if("href"!==t&&"list"!==t&&"form"!==t&&"tabIndex"!==t&&"download"!==t&&t in e)try{e[t]=null==n?"":n;break e}catch(e){}"function"==typeof n||(null!=n&&(!1!==n||"a"===t[0]&&"r"===t[1])?e.setAttribute(t,n):e.removeAttribute(t))}}function I(e){this.l[e.type+!1](i.event?i.event(e):e)}function M(e){this.l[e.type+!0](i.event?i.event(e):e)}function x(e,t,n,r,o,a,s,l,u){var c,d,f,h,v,m,_,b,E,C,S,w=t.type;if(void 0!==t.constructor)return null;null!=n.__h&&(u=n.__h,l=t.__e=n.__e,t.__h=null,a=[l]),(c=i.__b)&&c(t);try{e:if("function"==typeof w){if(b=t.props,E=(c=w.contextType)&&r[c.__c],C=c?E?E.props.value:c.__:r,n.__c?_=(d=t.__c=n.__c).__=d.__E:("prototype"in w&&w.prototype.render?t.__c=d=new w(b,C):(t.__c=d=new y(b,C),d.constructor=w,d.render=A),E&&E.sub(d),d.props=b,d.state||(d.state={}),d.context=C,d.__n=r,f=d.__d=!0,d.__h=[]),null==d.__s&&(d.__s=d.state),null!=w.getDerivedStateFromProps&&(d.__s==d.state&&(d.__s=p({},d.__s)),p(d.__s,w.getDerivedStateFromProps(b,d.__s))),h=d.props,v=d.state,f)null==w.getDerivedStateFromProps&&null!=d.componentWillMount&&d.componentWillMount(),null!=d.componentDidMount&&d.__h.push(d.componentDidMount);else{if(null==w.getDerivedStateFromProps&&b!==h&&null!=d.componentWillReceiveProps&&d.componentWillReceiveProps(b,C),!d.__e&&null!=d.shouldComponentUpdate&&!1===d.shouldComponentUpdate(b,d.__s,C)||t.__v===n.__v){d.props=b,d.state=d.__s,t.__v!==n.__v&&(d.__d=!1),d.__v=t,t.__e=n.__e,t.__k=n.__k,t.__k.forEach((function(e){e&&(e.__=t)})),d.__h.length&&s.push(d);break e}null!=d.componentWillUpdate&&d.componentWillUpdate(b,d.__s,C),null!=d.componentDidUpdate&&d.__h.push((function(){d.componentDidUpdate(h,v,m)}))}d.context=C,d.props=b,d.state=d.__s,(c=i.__r)&&c(t),d.__d=!1,d.__v=t,d.__P=e,c=d.render(d.props,d.state,d.context),d.state=d.__s,null!=d.getChildContext&&(r=p(p({},r),d.getChildContext())),f||null==d.getSnapshotBeforeUpdate||(m=d.getSnapshotBeforeUpdate(h,v)),S=null!=c&&c.type===g&&null==c.key?c.props.children:c,D(e,Array.isArray(S)?S:[S],t,n,r,o,a,s,l,u),d.base=t.__e,t.__h=null,d.__h.length&&s.push(d),_&&(d.__E=d.__=null),d.__e=!1}else null==a&&t.__v===n.__v?(t.__k=n.__k,t.__e=n.__e):t.__e=P(n.__e,t,n,r,o,a,s,u);(c=i.diffed)&&c(t)}catch(e){t.__v=null,(u||null!=a)&&(t.__e=l,t.__h=!!u,a[a.indexOf(l)]=null),i.__e(e,t,n)}}function O(e,t){i.__c&&i.__c(t,e),e.some((function(t){try{e=t.__h,t.__h=[],e.some((function(e){e.call(t)}))}catch(e){i.__e(e,t.__v)}}))}function P(e,t,n,r,o,i,a,s){var l,u,f,p,v=n.props,m=t.props,g=t.type,y=0;if("svg"===g&&(o=!0),null!=i)for(;y<i.length;y++)if((l=i[y])&&(l===e||(g?l.localName==g:3==l.nodeType))){e=l,i[y]=null;break}if(null==e){if(null===g)return document.createTextNode(m);e=o?document.createElementNS("http://www.w3.org/2000/svg",g):document.createElement(g,m.is&&m),i=null,s=!1}if(null===g)v===m||s&&e.data===m||(e.data=m);else{if(i=i&&d.slice.call(e.childNodes),u=(v=n.props||c).dangerouslySetInnerHTML,f=m.dangerouslySetInnerHTML,!s){if(null!=i)for(v={},p=0;p<e.attributes.length;p++)v[e.attributes[p].name]=e.attributes[p].value;(f||u)&&(f&&(u&&f.__html==u.__html||f.__html===e.innerHTML)||(e.innerHTML=f&&f.__html||""))}if(function(e,t,n,r,o){var i;for(i in n)"children"===i||"key"===i||i in t||k(e,i,null,n[i],r);for(i in t)o&&"function"!=typeof t[i]||"children"===i||"key"===i||"value"===i||"checked"===i||n[i]===t[i]||k(e,i,t[i],n[i],r)}(e,m,v,o,s),f)t.__k=[];else if(y=t.props.children,D(e,Array.isArray(y)?y:[y],t,n,r,o&&"foreignObject"!==g,i,a,e.firstChild,s),null!=i)for(y=i.length;y--;)null!=i[y]&&h(i[y]);s||("value"in m&&void 0!==(y=m.value)&&(y!==e.value||"progress"===g&&!y)&&k(e,"value",y,v.value,!1),"checked"in m&&void 0!==(y=m.checked)&&y!==e.checked&&k(e,"checked",y,v.checked,!1))}return e}function N(e,t,n){try{"function"==typeof e?e(t):e.current=t}catch(e){i.__e(e,n)}}function H(e,t,n){var r,o,a;if(i.unmount&&i.unmount(e),(r=e.ref)&&(r.current&&r.current!==e.__e||N(r,null,t)),n||"function"==typeof e.type||(n=null!=(o=e.__e)),e.__e=e.__d=void 0,null!=(r=e.__c)){if(r.componentWillUnmount)try{r.componentWillUnmount()}catch(e){i.__e(e,t)}r.base=r.__P=null}if(r=e.__k)for(a=0;a<r.length;a++)r[a]&&H(r[a],t,n);null!=o&&h(o)}function A(e,t,n){return this.constructor(e,n)}function U(e,t,n){var r,o,a;i.__&&i.__(e,t),o=(r="function"==typeof n)?null:n&&n.__k||t.__k,a=[],x(t,e=(!r&&n||t).__k=v(g,null,[e]),o||c,c,void 0!==t.ownerSVGElement,!r&&n?[n]:o?null:t.firstChild?d.slice.call(t.childNodes):null,a,!r&&n?n:o?o.__e:t.firstChild,r),O(a,e)}i={__e:function(e,t){for(var n,r,o;t=t.__;)if((n=t.__c)&&!n.__)try{if((r=n.constructor)&&null!=r.getDerivedStateFromError&&(n.setState(r.getDerivedStateFromError(e)),o=n.__d),null!=n.componentDidCatch&&(n.componentDidCatch(e),o=n.__d),o)return n.__E=n}catch(t){e=t}throw e},__v:0},y.prototype.setState=function(e,t){var n;n=null!=this.__s&&this.__s!==this.state?this.__s:this.__s=p({},this.state),"function"==typeof e&&(e=e(p({},n),this.props)),e&&p(n,e),null!=e&&this.__v&&(t&&this.__h.push(t),E(this))},y.prototype.forceUpdate=function(e){this.__v&&(this.__e=!0,e&&this.__h.push(e),E(this))},y.prototype.render=g,a=[],s="function"==typeof Promise?Promise.prototype.then.bind(Promise.resolve()):setTimeout,C.__r=0,u=0;var B,V=[],L=i.__b,W=i.__r,F=i.diffed,z=i.__c,j=i.unmount;function q(){V.forEach((function(e){if(e.__P)try{e.__H.__h.forEach(G),e.__H.__h.forEach(Y),e.__H.__h=[]}catch(t){e.__H.__h=[],i.__e(t,e.__v)}})),V=[]}i.__b=function(e){L&&L(e)},i.__r=function(e){W&&W(e);var t=e.__c.__H;t&&(t.__h.forEach(G),t.__h.forEach(Y),t.__h=[])},i.diffed=function(e){F&&F(e);var t=e.__c;t&&t.__H&&t.__H.__h.length&&(1!==V.push(t)&&B===i.requestAnimationFrame||((B=i.requestAnimationFrame)||function(e){var t,n=function(){clearTimeout(r),Z&&cancelAnimationFrame(t),setTimeout(e)},r=setTimeout(n,100);Z&&(t=requestAnimationFrame(n))})(q))},i.__c=function(e,t){t.some((function(e){try{e.__h.forEach(G),e.__h=e.__h.filter((function(e){return!e.__||Y(e)}))}catch(n){t.some((function(e){e.__h&&(e.__h=[])})),t=[],i.__e(n,e.__v)}})),z&&z(e,t)},i.unmount=function(e){j&&j(e);var t=e.__c;if(t&&t.__H)try{t.__H.__.forEach(G)}catch(e){i.__e(e,t.__v)}};var Z="function"==typeof requestAnimationFrame;function G(e){"function"==typeof e.__c&&e.__c()}function Y(e){e.__c=e.__()}function K(e,t){for(var n in e)if("__source"!==n&&!(n in t))return!0;for(var r in t)if("__source"!==r&&e[r]!==t[r])return!0;return!1}function X(e){this.props=e}(X.prototype=new y).isPureReactComponent=!0,X.prototype.shouldComponentUpdate=function(e,t){return K(this.props,e)||K(this.state,t)};var $=i.__b;i.__b=function(e){e.type&&e.type.__f&&e.ref&&(e.props.ref=e.ref,e.ref=null),$&&$(e)};var J=i.__e;i.__e=function(e,t,n){if(e.then)for(var r,o=t;o=o.__;)if((r=o.__c)&&r.__c)return null==t.__e&&(t.__e=n.__e,t.__k=n.__k),r.__c(e,t);J(e,t,n)};var Q=i.unmount;function ee(){this.__u=0,this.t=null,this.__b=null}function te(e){var t=e.__.__c;return t&&t.__e&&t.__e(e)}function ne(){this.u=null,this.o=null}i.unmount=function(e){var t=e.__c;t&&t.__R&&t.__R(),t&&!0===e.__h&&(e.type=null),Q&&Q(e)},(ee.prototype=new y).__c=function(e,t){var n=t.__c,r=this;null==r.t&&(r.t=[]),r.t.push(n);var o=te(r.__v),i=!1,a=function(){i||(i=!0,n.__R=null,o?o(s):s())};n.__R=a;var s=function(){if(!--r.__u){if(r.state.__e){var e=r.state.__e;r.__v.__k[0]=function e(t,n,r){return t&&(t.__v=null,t.__k=t.__k&&t.__k.map((function(t){return e(t,n,r)})),t.__c&&t.__c.__P===n&&(t.__e&&r.insertBefore(t.__e,t.__d),t.__c.__e=!0,t.__c.__P=r)),t}(e,e.__c.__P,e.__c.__O)}var t;for(r.setState({__e:r.__b=null});t=r.t.pop();)t.forceUpdate()}},l=!0===t.__h;r.__u++||l||r.setState({__e:r.__b=r.__v.__k[0]}),e.then(a,a)},ee.prototype.componentWillUnmount=function(){this.t=[]},ee.prototype.render=function(e,t){if(this.__b){if(this.__v.__k){var n=document.createElement("div"),r=this.__v.__k[0].__c;this.__v.__k[0]=function e(t,n,r){return t&&(t.__c&&t.__c.__H&&(t.__c.__H.__.forEach((function(e){"function"==typeof e.__c&&e.__c()})),t.__c.__H=null),null!=(t=function(e,t){for(var n in t)e[n]=t[n];return e}({},t)).__c&&(t.__c.__P===r&&(t.__c.__P=n),t.__c=null),t.__k=t.__k&&t.__k.map((function(t){return e(t,n,r)}))),t}(this.__b,n,r.__O=r.__P)}this.__b=null}var o=t.__e&&v(g,null,e.fallback);return o&&(o.__h=null),[v(g,null,t.__e?null:e.children),o]};var re=function(e,t,n){if(++n[1]===n[0]&&e.o.delete(t),e.props.revealOrder&&("t"!==e.props.revealOrder[0]||!e.o.size))for(n=e.u;n;){for(;n.length>3;)n.pop()();if(n[1]<n[0])break;e.u=n=n[2]}};function oe(e){return this.getChildContext=function(){return e.context},e.children}function ie(e){var t=this,n=e.i;t.componentWillUnmount=function(){U(null,t.l),t.l=null,t.i=null},t.i&&t.i!==n&&t.componentWillUnmount(),e.__v?(t.l||(t.i=n,t.l={nodeType:1,parentNode:n,childNodes:[],appendChild:function(e){this.childNodes.push(e),t.i.appendChild(e)},insertBefore:function(e,n){this.childNodes.push(e),t.i.appendChild(e)},removeChild:function(e){this.childNodes.splice(this.childNodes.indexOf(e)>>>1,1),t.i.removeChild(e)}}),U(v(oe,{context:t.context},e.__v),t.l)):t.l&&t.componentWillUnmount()}(ne.prototype=new y).__e=function(e){var t=this,n=te(t.__v),r=t.o.get(e);return r[0]++,function(o){var i=function(){t.props.revealOrder?(r.push(o),re(t,e,r)):o()};n?n(i):i()}},ne.prototype.render=function(e){this.u=null,this.o=new Map;var t=w(e.children);e.revealOrder&&"b"===e.revealOrder[0]&&t.reverse();for(var n=t.length;n--;)this.o.set(t[n],this.u=[1,0,this.u]);return e.children},ne.prototype.componentDidUpdate=ne.prototype.componentDidMount=function(){var e=this;this.o.forEach((function(t,n){re(e,n,t)}))};var ae="undefined"!=typeof Symbol&&Symbol.for&&Symbol.for("react.element")||60103,se=/^(?:accent|alignment|arabic|baseline|cap|clip(?!PathU)|color|fill|flood|font|glyph(?!R)|horiz|marker(?!H|W|U)|overline|paint|stop|strikethrough|stroke|text(?!L)|underline|unicode|units|v|vector|vert|word|writing|x(?!C))[A-Z]/,le=function(e){return("undefined"!=typeof Symbol&&"symbol"==typeof Symbol()?/fil|che|rad/i:/fil|che|ra/i).test(e)};y.prototype.isReactComponent={},["componentWillMount","componentWillReceiveProps","componentWillUpdate"].forEach((function(e){Object.defineProperty(y.prototype,e,{configurable:!0,get:function(){return this["UNSAFE_"+e]},set:function(t){Object.defineProperty(this,e,{configurable:!0,writable:!0,value:t})}})}));var ue=i.event;function ce(){}function de(){return this.cancelBubble}function fe(){return this.defaultPrevented}i.event=function(e){return ue&&(e=ue(e)),e.persist=ce,e.isPropagationStopped=de,e.isDefaultPrevented=fe,e.nativeEvent=e};var pe={configurable:!0,get:function(){return this.class}},he=i.vnode;i.vnode=function(e){var t=e.type,n=e.props,r=n;if("string"==typeof t){for(var o in r={},n){var i=n[o];"value"===o&&"defaultValue"in n&&null==i||("defaultValue"===o&&"value"in n&&null==n.value?o="value":"download"===o&&!0===i?i="":/ondoubleclick/i.test(o)?o="ondblclick":/^onchange(textarea|input)/i.test(o+t)&&!le(n.type)?o="oninput":/^on(Ani|Tra|Tou|BeforeInp)/.test(o)?o=o.toLowerCase():se.test(o)?o=o.replace(/[A-Z0-9]/,"-$&").toLowerCase():null===i&&(i=void 0),r[o]=i)}"select"==t&&r.multiple&&Array.isArray(r.value)&&(r.value=w(n.children).forEach((function(e){e.props.selected=-1!=r.value.indexOf(e.props.value)}))),"select"==t&&null!=r.defaultValue&&(r.value=w(n.children).forEach((function(e){e.props.selected=r.multiple?-1!=r.defaultValue.indexOf(e.props.value):r.defaultValue==e.props.value}))),e.props=r}t&&n.class!=n.className&&(pe.enumerable="className"in n,null!=n.className&&(r.class=n.className),Object.defineProperty(r,"className",pe)),e.$$typeof=ae,he&&he(e)};var ve=i.__r;i.__r=function(e){ve&&ve(e)},"object"==typeof performance&&"function"==typeof performance.now&&performance.now.bind(performance);var me="undefined"!=typeof globalThis?globalThis:window;me.FullCalendarVDom?console.warn("FullCalendar VDOM already loaded"):me.FullCalendarVDom={Component:y,createElement:v,render:U,createRef:function(){return{current:null}},Fragment:g,createContext:function(e){var t=function(e,t){var n={__c:t="__cC"+u++,__:e,Consumer:function(e,t){return e.children(t)},Provider:function(e){var n,r;return this.getChildContext||(n=[],(r={})[t]=this,this.getChildContext=function(){return r},this.shouldComponentUpdate=function(e){this.props.value!==e.value&&n.some(E)},this.sub=function(e){n.push(e);var t=e.componentWillUnmount;e.componentWillUnmount=function(){n.splice(n.indexOf(e),1),t&&t.call(e)}}),e.children}};return n.Provider.__=n.Consumer.contextType=n}(e),n=t.Provider;return t.Provider=function(){var e=this,t=!this.getChildContext,r=n.apply(this,arguments);if(t){var o=[];this.shouldComponentUpdate=function(t){e.props.value!==t.value&&o.forEach((function(e){e.context=t.value,e.forceUpdate()}))},this.sub=function(e){o.push(e);var t=e.componentWillUnmount;e.componentWillUnmount=function(){o.splice(o.indexOf(e),1),t&&t.call(e)}}}return r},t},createPortal:function(e,t){return v(ie,{__v:e,i:t})},flushToDom:function(){var e=i.debounceRendering,t=[];function n(e){t.push(e)}i.debounceRendering=n,U(v(ge,{}),document.createElement("div"));for(;t.length;)t.shift()();i.debounceRendering=e},unmountComponentAtNode:function(e){U(null,e)}};var ge=function(e){function t(){return null!==e&&e.apply(this,arguments)||this}return n(t,e),t.prototype.render=function(){return v("div",{})},t.prototype.componentDidMount=function(){this.setState({})},t}(y);var ye=function(){function e(e,t){this.context=e,this.internalEventSource=t}return e.prototype.remove=function(){this.context.dispatch({type:"REMOVE_EVENT_SOURCE",sourceId:this.internalEventSource.sourceId})},e.prototype.refetch=function(){this.context.dispatch({type:"FETCH_EVENT_SOURCES",sourceIds:[this.internalEventSource.sourceId],isRefetch:!0})},Object.defineProperty(e.prototype,"id",{get:function(){return this.internalEventSource.publicId},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"url",{get:function(){return this.internalEventSource.meta.url},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"format",{get:function(){return this.internalEventSource.meta.format},enumerable:!1,configurable:!0}),e}();function _e(e){e.parentNode&&e.parentNode.removeChild(e)}function be(e,t){if(e.closest)return e.closest(t);if(!document.documentElement.contains(e))return null;do{if(Ee(e,t))return e;e=e.parentElement||e.parentNode}while(null!==e&&1===e.nodeType);return null}function Ee(e,t){return(e.matches||e.matchesSelector||e.msMatchesSelector).call(e,t)}function Ce(e,t){for(var n=e instanceof HTMLElement?[e]:e,r=[],o=0;o<n.length;o+=1)for(var i=n[o].querySelectorAll(t),a=0;a<i.length;a+=1)r.push(i[a]);return r}var De=/(top|left|right|bottom|width|height)$/i;function Se(e,t){for(var n in t)we(e,n,t[n])}function we(e,t,n){null==n?e.style[t]="":"number"==typeof n&&De.test(t)?e.style[t]=n+"px":e.style[t]=n}function Te(e){var t,n;return null!==(n=null===(t=e.composedPath)||void 0===t?void 0:t.call(e)[0])&&void 0!==n?n:e.target}var Re=0;function ke(){return"fc-dom-"+(Re+=1)}function Ie(e){e.preventDefault()}function Me(e,t,n,r){var o=function(e,t){return function(n){var r=be(n.target,e);r&&t.call(r,n,r)}}(n,r);return e.addEventListener(t,o),function(){e.removeEventListener(t,o)}}var xe=["webkitTransitionEnd","otransitionend","oTransitionEnd","msTransitionEnd","transitionend"];function Oe(e){return r({onClick:e},Pe(e))}function Pe(e){return{tabIndex:0,onKeyDown:function(t){"Enter"!==t.key&&" "!==t.key||(e(t),t.preventDefault())}}}var Ne=0;function He(){return String(Ne+=1)}function Ae(e){var t,n,r=[],o=[];for("string"==typeof e?o=e.split(/\s*,\s*/):"function"==typeof e?o=[e]:Array.isArray(e)&&(o=e),t=0;t<o.length;t+=1)"string"==typeof(n=o[t])?r.push("-"===n.charAt(0)?{field:n.substring(1),order:-1}:{field:n,order:1}):"function"==typeof n&&r.push({func:n});return r}function Ue(e,t,n){var r,o;for(r=0;r<n.length;r+=1)if(o=Be(e,t,n[r]))return o;return 0}function Be(e,t,n){return n.func?n.func(e,t):Ve(e[n.field],t[n.field])*(n.order||1)}function Ve(e,t){return e||t?null==t?-1:null==e?1:"string"==typeof e||"string"==typeof t?String(e).localeCompare(String(t)):e-t:0}function Le(e,t){var n=String(e);return"000".substr(0,t-n.length)+n}function We(e,t,n){return"function"==typeof e?e.apply(void 0,t):"string"==typeof e?t.reduce((function(e,t,n){return e.replace("$"+n,t||"")}),e):n}function Fe(e){return e%1==0}function ze(e){var t=e.querySelector(".fc-scrollgrid-shrink-frame"),n=e.querySelector(".fc-scrollgrid-shrink-cushion");if(!t)throw new Error("needs fc-scrollgrid-shrink-frame className");if(!n)throw new Error("needs fc-scrollgrid-shrink-cushion className");return e.getBoundingClientRect().width-t.getBoundingClientRect().width+n.getBoundingClientRect().width}var je=["sun","mon","tue","wed","thu","fri","sat"];function qe(e,t){var n=nt(e);return n[2]+=t,rt(n)}function Ze(e,t){var n=nt(e);return n[6]+=t,rt(n)}function Ge(e,t){return(t.valueOf()-e.valueOf())/864e5}function Ye(e,t){var n=$e(e),r=$e(t);return{years:0,months:0,days:Math.round(Ge(n,r)),milliseconds:t.valueOf()-r.valueOf()-(e.valueOf()-n.valueOf())}}function Ke(e,t){var n=Xe(e,t);return null!==n&&n%7==0?n/7:null}function Xe(e,t){return it(e)===it(t)?Math.round(Ge(e,t)):null}function $e(e){return rt([e.getUTCFullYear(),e.getUTCMonth(),e.getUTCDate()])}function Je(e,t,n,r){var o=rt([t,0,1+Qe(t,n,r)]),i=$e(e),a=Math.round(Ge(o,i));return Math.floor(a/7)+1}function Qe(e,t,n){var r=7+t-n;return-((7+rt([e,0,r]).getUTCDay()-t)%7)+r-1}function et(e){return[e.getFullYear(),e.getMonth(),e.getDate(),e.getHours(),e.getMinutes(),e.getSeconds(),e.getMilliseconds()]}function tt(e){return new Date(e[0],e[1]||0,null==e[2]?1:e[2],e[3]||0,e[4]||0,e[5]||0)}function nt(e){return[e.getUTCFullYear(),e.getUTCMonth(),e.getUTCDate(),e.getUTCHours(),e.getUTCMinutes(),e.getUTCSeconds(),e.getUTCMilliseconds()]}function rt(e){return 1===e.length&&(e=e.concat([0])),new Date(Date.UTC.apply(Date,e))}function ot(e){return!isNaN(e.valueOf())}function it(e){return 1e3*e.getUTCHours()*60*60+1e3*e.getUTCMinutes()*60+1e3*e.getUTCSeconds()+e.getUTCMilliseconds()}function at(e,t,n,r){return{instanceId:He(),defId:e,range:t,forcedStartTzo:null==n?null:n,forcedEndTzo:null==r?null:r}}var st=Object.prototype.hasOwnProperty;function lt(e,t){var n={};if(t)for(var r in t){for(var o=[],i=e.length-1;i>=0;i-=1){var a=e[i][r];if("object"==typeof a&&a)o.unshift(a);else if(void 0!==a){n[r]=a;break}}o.length&&(n[r]=lt(o))}for(i=e.length-1;i>=0;i-=1){var s=e[i];for(var l in s)l in n||(n[l]=s[l])}return n}function ut(e,t){var n={};for(var r in e)t(e[r],r)&&(n[r]=e[r]);return n}function ct(e,t){var n={};for(var r in e)n[r]=t(e[r],r);return n}function dt(e){for(var t={},n=0,r=e;n<r.length;n++){t[r[n]]=!0}return t}function ft(e){var t=[];for(var n in e)t.push(e[n]);return t}function pt(e,t){if(e===t)return!0;for(var n in e)if(st.call(e,n)&&!(n in t))return!1;for(var n in t)if(st.call(t,n)&&e[n]!==t[n])return!1;return!0}function ht(e,t){var n=[];for(var r in e)st.call(e,r)&&(r in t||n.push(r));for(var r in t)st.call(t,r)&&e[r]!==t[r]&&n.push(r);return n}function vt(e,t,n){if(void 0===n&&(n={}),e===t)return!0;for(var r in t)if(!(r in e)||!mt(e[r],t[r],n[r]))return!1;for(var r in e)if(!(r in t))return!1;return!0}function mt(e,t,n){return e===t||!0===n||!!n&&n(e,t)}function gt(e,t,n,r){void 0===t&&(t=0),void 0===r&&(r=1);var o=[];null==n&&(n=Object.keys(e).length);for(var i=t;i<n;i+=r){var a=e[i];void 0!==a&&o.push(a)}return o}function yt(e,t,n){var r=n.dateEnv,o=n.pluginHooks,i=n.options,a=e.defs,s=e.instances;for(var l in s=ut(s,(function(e){return!a[e.defId].recurringDef})),a){var u=a[l];if(u.recurringDef){var c=u.recurringDef.duration;c||(c=u.allDay?i.defaultAllDayEventDuration:i.defaultTimedEventDuration);for(var d=0,f=_t(u,c,t,r,o.recurringTypes);d<f.length;d++){var p=f[d],h=at(l,{start:p,end:r.add(p,c)});s[h.instanceId]=h}}}return{defs:a,instances:s}}function _t(e,t,n,r,o){var i=o[e.recurringDef.typeId].expand(e.recurringDef.typeData,{start:r.subtract(n.start,t),end:n.end},r);return e.allDay&&(i=i.map($e)),i}var bt=["years","months","days","milliseconds"],Et=/^(-?)(?:(\d+)\.)?(\d+):(\d\d)(?::(\d\d)(?:\.(\d\d\d))?)?/;function Ct(e,t){var n;return"string"==typeof e?function(e){var t=Et.exec(e);if(t){var n=t[1]?-1:1;return{years:0,months:0,days:n*(t[2]?parseInt(t[2],10):0),milliseconds:n*(60*(t[3]?parseInt(t[3],10):0)*60*1e3+60*(t[4]?parseInt(t[4],10):0)*1e3+1e3*(t[5]?parseInt(t[5],10):0)+(t[6]?parseInt(t[6],10):0))}}return null}(e):"object"==typeof e&&e?Dt(e):"number"==typeof e?Dt(((n={})[t||"milliseconds"]=e,n)):null}function Dt(e){var t={years:e.years||e.year||0,months:e.months||e.month||0,days:e.days||e.day||0,milliseconds:60*(e.hours||e.hour||0)*60*1e3+60*(e.minutes||e.minute||0)*1e3+1e3*(e.seconds||e.second||0)+(e.milliseconds||e.millisecond||e.ms||0)},n=e.weeks||e.week;return n&&(t.days+=7*n,t.specifiedWeeks=!0),t}function St(e){return wt(e)/864e5}function wt(e){return 31536e6*e.years+2592e6*e.months+864e5*e.days+e.milliseconds}function Tt(e){var t=e.milliseconds;if(t){if(t%1e3!=0)return{unit:"millisecond",value:t};if(t%6e4!=0)return{unit:"second",value:t/1e3};if(t%36e5!=0)return{unit:"minute",value:t/6e4};if(t)return{unit:"hour",value:t/36e5}}return e.days?e.specifiedWeeks&&e.days%7==0?{unit:"week",value:e.days/7}:{unit:"day",value:e.days}:e.months?{unit:"month",value:e.months}:e.years?{unit:"year",value:e.years}:{unit:"millisecond",value:0}}function Rt(e,t,n){void 0===n&&(n=!1);var r=e.toISOString();return r=r.replace(".000",""),n&&(r=r.replace("T00:00:00Z","")),r.length>10&&(null==t?r=r.replace("Z",""):0!==t&&(r=r.replace("Z",It(t,!0)))),r}function kt(e){return e.toISOString().replace(/T.*$/,"")}function It(e,t){void 0===t&&(t=!1);var n=e<0?"-":"+",r=Math.abs(e),o=Math.floor(r/60),i=Math.round(r%60);return t?n+Le(o,2)+":"+Le(i,2):"GMT"+n+o+(i?":"+Le(i,2):"")}function Mt(e,t,n){if(e===t)return!0;var r,o=e.length;if(o!==t.length)return!1;for(r=0;r<o;r+=1)if(!(n?n(e[r],t[r]):e[r]===t[r]))return!1;return!0}function xt(e,t,n){var r,o;return function(){for(var i=[],a=0;a<arguments.length;a++)i[a]=arguments[a];if(r){if(!Mt(r,i)){n&&n(o);var s=e.apply(this,i);t&&t(s,o)||(o=s)}}else o=e.apply(this,i);return r=i,o}}function Ot(e,t,n){var r,o,i=this;return function(a){if(r){if(!pt(r,a)){n&&n(o);var s=e.call(i,a);t&&t(s,o)||(o=s)}}else o=e.call(i,a);return r=a,o}}var Pt={week:3,separator:0,omitZeroMinute:0,meridiem:0,omitCommas:0},Nt={timeZoneName:7,era:6,year:5,month:4,day:2,weekday:2,hour:1,minute:1,second:1},Ht=/\s*([ap])\.?m\.?/i,At=/,/g,Ut=/\s+/g,Bt=/\u200e/g,Vt=/UTC|GMT/,Lt=function(){function e(e){var t={},n={},r=0;for(var o in e)o in Pt?(n[o]=e[o],r=Math.max(Pt[o],r)):(t[o]=e[o],o in Nt&&(r=Math.max(Nt[o],r)));this.standardDateProps=t,this.extendedSettings=n,this.severity=r,this.buildFormattingFunc=xt(Wt)}return e.prototype.format=function(e,t){return this.buildFormattingFunc(this.standardDateProps,this.extendedSettings,t)(e)},e.prototype.formatRange=function(e,t,n,r){var o=this.standardDateProps,i=this.extendedSettings,a=function(e,t,n){if(n.getMarkerYear(e)!==n.getMarkerYear(t))return 5;if(n.getMarkerMonth(e)!==n.getMarkerMonth(t))return 4;if(n.getMarkerDay(e)!==n.getMarkerDay(t))return 2;if(it(e)!==it(t))return 1;return 0}(e.marker,t.marker,n.calendarSystem);if(!a)return this.format(e,n);var s=a;!(s>1)||"numeric"!==o.year&&"2-digit"!==o.year||"numeric"!==o.month&&"2-digit"!==o.month||"numeric"!==o.day&&"2-digit"!==o.day||(s=1);var l=this.format(e,n),u=this.format(t,n);if(l===u)return l;var c=Wt(function(e,t){var n={};for(var r in e)(!(r in Nt)||Nt[r]<=t)&&(n[r]=e[r]);return n}(o,s),i,n),d=c(e),f=c(t),p=function(e,t,n,r){var o=0;for(;o<e.length;){var i=e.indexOf(t,o);if(-1===i)break;var a=e.substr(0,i);o=i+t.length;for(var s=e.substr(o),l=0;l<n.length;){var u=n.indexOf(r,l);if(-1===u)break;var c=n.substr(0,u);l=u+r.length;var d=n.substr(l);if(a===c&&s===d)return{before:a,after:s}}}return null}(l,d,u,f),h=i.separator||r||n.defaultSeparator||"";return p?p.before+d+h+f+p.after:l+h+u},e.prototype.getLargestUnit=function(){switch(this.severity){case 7:case 6:case 5:return"year";case 4:return"month";case 3:return"week";case 2:return"day";default:return"time"}},e}();function Wt(e,t,n){var o=Object.keys(e).length;return 1===o&&"short"===e.timeZoneName?function(e){return It(e.timeZoneOffset)}:0===o&&t.week?function(e){return function(e,t,n,r,o){var i=[];"long"===o?i.push(n):"short"!==o&&"narrow"!==o||i.push(t);"long"!==o&&"short"!==o||i.push(" ");i.push(r.simpleNumberFormat.format(e)),"rtl"===r.options.direction&&i.reverse();return i.join("")}(n.computeWeekNumber(e.marker),n.weekText,n.weekTextLong,n.locale,t.week)}:function(e,t,n){e=r({},e),t=r({},t),function(e,t){e.timeZoneName&&(e.hour||(e.hour="2-digit"),e.minute||(e.minute="2-digit"));"long"===e.timeZoneName&&(e.timeZoneName="short");t.omitZeroMinute&&(e.second||e.millisecond)&&delete t.omitZeroMinute}(e,t),e.timeZone="UTC";var o,i=new Intl.DateTimeFormat(n.locale.codes,e);if(t.omitZeroMinute){var a=r({},e);delete a.minute,o=new Intl.DateTimeFormat(n.locale.codes,a)}return function(r){var a=r.marker;return function(e,t,n,r,o){e=e.replace(Bt,""),"short"===n.timeZoneName&&(e=function(e,t){var n=!1;e=e.replace(Vt,(function(){return n=!0,t})),n||(e+=" "+t);return e}(e,"UTC"===o.timeZone||null==t.timeZoneOffset?"UTC":It(t.timeZoneOffset)));r.omitCommas&&(e=e.replace(At,"").trim());r.omitZeroMinute&&(e=e.replace(":00",""));!1===r.meridiem?e=e.replace(Ht,"").trim():"narrow"===r.meridiem?e=e.replace(Ht,(function(e,t){return t.toLocaleLowerCase()})):"short"===r.meridiem?e=e.replace(Ht,(function(e,t){return t.toLocaleLowerCase()+"m"})):"lowercase"===r.meridiem&&(e=e.replace(Ht,(function(e){return e.toLocaleLowerCase()})));return e=(e=e.replace(Ut," ")).trim()}((o&&!a.getUTCMinutes()?o:i).format(a),r,e,t,n)}}(e,t,n)}function Ft(e,t){var n=t.markerToArray(e.marker);return{marker:e.marker,timeZoneOffset:e.timeZoneOffset,array:n,year:n[0],month:n[1],day:n[2],hour:n[3],minute:n[4],second:n[5],millisecond:n[6]}}function zt(e,t,n,r){var o=Ft(e,n.calendarSystem);return{date:o,start:o,end:t?Ft(t,n.calendarSystem):null,timeZone:n.timeZone,localeCodes:n.locale.codes,defaultSeparator:r||n.defaultSeparator}}var jt=function(){function e(e){this.cmdStr=e}return e.prototype.format=function(e,t,n){return t.cmdFormatter(this.cmdStr,zt(e,null,t,n))},e.prototype.formatRange=function(e,t,n,r){return n.cmdFormatter(this.cmdStr,zt(e,t,n,r))},e}(),qt=function(){function e(e){this.func=e}return e.prototype.format=function(e,t,n){return this.func(zt(e,null,t,n))},e.prototype.formatRange=function(e,t,n,r){return this.func(zt(e,t,n,r))},e}();function Zt(e){return"object"==typeof e&&e?new Lt(e):"string"==typeof e?new jt(e):"function"==typeof e?new qt(e):null}var Gt={navLinkDayClick:nn,navLinkWeekClick:nn,duration:Ct,bootstrapFontAwesome:nn,buttonIcons:nn,customButtons:nn,defaultAllDayEventDuration:Ct,defaultTimedEventDuration:Ct,nextDayThreshold:Ct,scrollTime:Ct,scrollTimeReset:Boolean,slotMinTime:Ct,slotMaxTime:Ct,dayPopoverFormat:Zt,slotDuration:Ct,snapDuration:Ct,headerToolbar:nn,footerToolbar:nn,defaultRangeSeparator:String,titleRangeSeparator:String,forceEventDuration:Boolean,dayHeaders:Boolean,dayHeaderFormat:Zt,dayHeaderClassNames:nn,dayHeaderContent:nn,dayHeaderDidMount:nn,dayHeaderWillUnmount:nn,dayCellClassNames:nn,dayCellContent:nn,dayCellDidMount:nn,dayCellWillUnmount:nn,initialView:String,aspectRatio:Number,weekends:Boolean,weekNumberCalculation:nn,weekNumbers:Boolean,weekNumberClassNames:nn,weekNumberContent:nn,weekNumberDidMount:nn,weekNumberWillUnmount:nn,editable:Boolean,viewClassNames:nn,viewDidMount:nn,viewWillUnmount:nn,nowIndicator:Boolean,nowIndicatorClassNames:nn,nowIndicatorContent:nn,nowIndicatorDidMount:nn,nowIndicatorWillUnmount:nn,showNonCurrentDates:Boolean,lazyFetching:Boolean,startParam:String,endParam:String,timeZoneParam:String,timeZone:String,locales:nn,locale:nn,themeSystem:String,dragRevertDuration:Number,dragScroll:Boolean,allDayMaintainDuration:Boolean,unselectAuto:Boolean,dropAccept:nn,eventOrder:Ae,eventOrderStrict:Boolean,handleWindowResize:Boolean,windowResizeDelay:Number,longPressDelay:Number,eventDragMinDistance:Number,expandRows:Boolean,height:nn,contentHeight:nn,direction:String,weekNumberFormat:Zt,eventResizableFromStart:Boolean,displayEventTime:Boolean,displayEventEnd:Boolean,weekText:String,weekTextLong:String,progressiveEventRendering:Boolean,businessHours:nn,initialDate:nn,now:nn,eventDataTransform:nn,stickyHeaderDates:nn,stickyFooterScrollbar:nn,viewHeight:nn,defaultAllDay:Boolean,eventSourceFailure:nn,eventSourceSuccess:nn,eventDisplay:String,eventStartEditable:Boolean,eventDurationEditable:Boolean,eventOverlap:nn,eventConstraint:nn,eventAllow:nn,eventBackgroundColor:String,eventBorderColor:String,eventTextColor:String,eventColor:String,eventClassNames:nn,eventContent:nn,eventDidMount:nn,eventWillUnmount:nn,selectConstraint:nn,selectOverlap:nn,selectAllow:nn,droppable:Boolean,unselectCancel:String,slotLabelFormat:nn,slotLaneClassNames:nn,slotLaneContent:nn,slotLaneDidMount:nn,slotLaneWillUnmount:nn,slotLabelClassNames:nn,slotLabelContent:nn,slotLabelDidMount:nn,slotLabelWillUnmount:nn,dayMaxEvents:nn,dayMaxEventRows:nn,dayMinWidth:Number,slotLabelInterval:Ct,allDayText:String,allDayClassNames:nn,allDayContent:nn,allDayDidMount:nn,allDayWillUnmount:nn,slotMinWidth:Number,navLinks:Boolean,eventTimeFormat:Zt,rerenderDelay:Number,moreLinkText:nn,moreLinkHint:nn,selectMinDistance:Number,selectable:Boolean,selectLongPressDelay:Number,eventLongPressDelay:Number,selectMirror:Boolean,eventMaxStack:Number,eventMinHeight:Number,eventMinWidth:Number,eventShortHeight:Number,slotEventOverlap:Boolean,plugins:nn,firstDay:Number,dayCount:Number,dateAlignment:String,dateIncrement:Ct,hiddenDays:nn,monthMode:Boolean,fixedWeekCount:Boolean,validRange:nn,visibleRange:nn,titleFormat:nn,eventInteractive:Boolean,noEventsText:String,viewHint:nn,navLinkHint:nn,closeHint:String,timeHint:String,eventHint:String,moreLinkClick:nn,moreLinkClassNames:nn,moreLinkContent:nn,moreLinkDidMount:nn,moreLinkWillUnmount:nn},Yt={eventDisplay:"auto",defaultRangeSeparator:" - ",titleRangeSeparator:" – ",defaultTimedEventDuration:"01:00:00",defaultAllDayEventDuration:{day:1},forceEventDuration:!1,nextDayThreshold:"00:00:00",dayHeaders:!0,initialView:"",aspectRatio:1.35,headerToolbar:{start:"title",center:"",end:"today prev,next"},weekends:!0,weekNumbers:!1,weekNumberCalculation:"local",editable:!1,nowIndicator:!1,scrollTime:"06:00:00",scrollTimeReset:!0,slotMinTime:"00:00:00",slotMaxTime:"24:00:00",showNonCurrentDates:!0,lazyFetching:!0,startParam:"start",endParam:"end",timeZoneParam:"timeZone",timeZone:"local",locales:[],locale:"",themeSystem:"standard",dragRevertDuration:500,dragScroll:!0,allDayMaintainDuration:!1,unselectAuto:!0,dropAccept:"*",eventOrder:"start,-duration,allDay,title",dayPopoverFormat:{month:"long",day:"numeric",year:"numeric"},handleWindowResize:!0,windowResizeDelay:100,longPressDelay:1e3,eventDragMinDistance:5,expandRows:!1,navLinks:!1,selectable:!1,eventMinHeight:15,eventMinWidth:30,eventShortHeight:30},Kt={datesSet:nn,eventsSet:nn,eventAdd:nn,eventChange:nn,eventRemove:nn,windowResize:nn,eventClick:nn,eventMouseEnter:nn,eventMouseLeave:nn,select:nn,unselect:nn,loading:nn,_unmount:nn,_beforeprint:nn,_afterprint:nn,_noEventDrop:nn,_noEventResize:nn,_resize:nn,_scrollRequest:nn},Xt={buttonText:nn,buttonHints:nn,views:nn,plugins:nn,initialEvents:nn,events:nn,eventSources:nn},$t={headerToolbar:Jt,footerToolbar:Jt,buttonText:Jt,buttonHints:Jt,buttonIcons:Jt};function Jt(e,t){return"object"==typeof e&&"object"==typeof t&&e&&t?pt(e,t):e===t}var Qt={type:String,component:nn,buttonText:String,buttonTextKey:String,dateProfileGeneratorClass:nn,usesMinMaxTime:Boolean,classNames:nn,content:nn,didMount:nn,willUnmount:nn};function en(e){return lt(e,$t)}function tn(e,t){var n={},r={};for(var o in t)o in e&&(n[o]=t[o](e[o]));for(var o in e)o in t||(r[o]=e[o]);return{refined:n,extra:r}}function nn(e){return e}function rn(e,t,n,r){for(var o={defs:{},instances:{}},i=En(n),a=0,s=e;a<s.length;a++){var l=_n(s[a],t,n,r,i);l&&on(l,o)}return o}function on(e,t){return void 0===t&&(t={defs:{},instances:{}}),t.defs[e.def.defId]=e.def,e.instance&&(t.instances[e.instance.instanceId]=e.instance),t}function an(e,t){var n=e.instances[t];if(n){var r=e.defs[n.defId],o=un(e,(function(e){return t=r,n=e,Boolean(t.groupId&&t.groupId===n.groupId);var t,n}));return o.defs[r.defId]=r,o.instances[n.instanceId]=n,o}return{defs:{},instances:{}}}function sn(){return{defs:{},instances:{}}}function ln(e,t){return{defs:r(r({},e.defs),t.defs),instances:r(r({},e.instances),t.instances)}}function un(e,t){var n=ut(e.defs,t),r=ut(e.instances,(function(e){return n[e.defId]}));return{defs:n,instances:r}}function cn(e){return Array.isArray(e)?e:"string"==typeof e?e.split(/\s+/):[]}var dn={display:String,editable:Boolean,startEditable:Boolean,durationEditable:Boolean,constraint:nn,overlap:nn,allow:nn,className:cn,classNames:cn,color:String,backgroundColor:String,borderColor:String,textColor:String},fn={display:null,startEditable:null,durationEditable:null,constraints:[],overlap:null,allows:[],backgroundColor:"",borderColor:"",textColor:"",classNames:[]};function pn(e,t){var n=function(e,t){return Array.isArray(e)?rn(e,null,t,!0):"object"==typeof e&&e?rn([e],null,t,!0):null!=e?String(e):null}(e.constraint,t);return{display:e.display||null,startEditable:null!=e.startEditable?e.startEditable:e.editable,durationEditable:null!=e.durationEditable?e.durationEditable:e.editable,constraints:null!=n?[n]:[],overlap:null!=e.overlap?e.overlap:null,allows:null!=e.allow?[e.allow]:[],backgroundColor:e.backgroundColor||e.color||"",borderColor:e.borderColor||e.color||"",textColor:e.textColor||"",classNames:(e.className||[]).concat(e.classNames||[])}}function hn(e){return e.reduce(vn,fn)}function vn(e,t){return{display:null!=t.display?t.display:e.display,startEditable:null!=t.startEditable?t.startEditable:e.startEditable,durationEditable:null!=t.durationEditable?t.durationEditable:e.durationEditable,constraints:e.constraints.concat(t.constraints),overlap:"boolean"==typeof t.overlap?t.overlap:e.overlap,allows:e.allows.concat(t.allows),backgroundColor:t.backgroundColor||e.backgroundColor,borderColor:t.borderColor||e.borderColor,textColor:t.textColor||e.textColor,classNames:e.classNames.concat(t.classNames)}}var mn={id:String,groupId:String,title:String,url:String,interactive:Boolean},gn={start:nn,end:nn,date:nn,allDay:Boolean},yn=r(r(r({},mn),gn),{extendedProps:nn});function _n(e,t,n,r,o){void 0===o&&(o=En(n));var i=bn(e,n,o),a=i.refined,s=i.extra,l=function(e,t){var n=null;e&&(n=e.defaultAllDay);null==n&&(n=t.options.defaultAllDay);return n}(t,n),u=function(e,t,n,r){for(var o=0;o<r.length;o+=1){var i=r[o].parse(e,n);if(i){var a=e.allDay;return null==a&&null==(a=t)&&null==(a=i.allDayGuess)&&(a=!1),{allDay:a,duration:i.duration,typeData:i.typeData,typeId:o}}}return null}(a,l,n.dateEnv,n.pluginHooks.recurringTypes);if(u)return(c=Cn(a,s,t?t.sourceId:"",u.allDay,Boolean(u.duration),n)).recurringDef={typeId:u.typeId,typeData:u.typeData,duration:u.duration},{def:c,instance:null};var c,d=function(e,t,n,r){var o,i,a=e.allDay,s=null,l=!1,u=null,c=null!=e.start?e.start:e.date;if(o=n.dateEnv.createMarkerMeta(c))s=o.marker;else if(!r)return null;null!=e.end&&(i=n.dateEnv.createMarkerMeta(e.end));null==a&&(a=null!=t?t:(!o||o.isTimeUnspecified)&&(!i||i.isTimeUnspecified));a&&s&&(s=$e(s));i&&(u=i.marker,a&&(u=$e(u)),s&&u<=s&&(u=null));u?l=!0:r||(l=n.options.forceEventDuration||!1,u=n.dateEnv.add(s,a?n.options.defaultAllDayEventDuration:n.options.defaultTimedEventDuration));return{allDay:a,hasEnd:l,range:{start:s,end:u},forcedStartTzo:o?o.forcedTzo:null,forcedEndTzo:i?i.forcedTzo:null}}(a,l,n,r);return d?{def:c=Cn(a,s,t?t.sourceId:"",d.allDay,d.hasEnd,n),instance:at(c.defId,d.range,d.forcedStartTzo,d.forcedEndTzo)}:null}function bn(e,t,n){return void 0===n&&(n=En(t)),tn(e,n)}function En(e){return r(r(r({},dn),yn),e.pluginHooks.eventRefiners)}function Cn(e,t,n,o,i,a){for(var s={title:e.title||"",groupId:e.groupId||"",publicId:e.id||"",url:e.url||"",recurringDef:null,defId:He(),sourceId:n,allDay:o,hasEnd:i,interactive:e.interactive,ui:pn(e,a),extendedProps:r(r({},e.extendedProps||{}),t)},l=0,u=a.pluginHooks.eventDefMemberAdders;l<u.length;l++){var c=u[l];r(s,c(e))}return Object.freeze(s.ui.classNames),Object.freeze(s.extendedProps),s}function Dn(e){var t=Math.floor(Ge(e.start,e.end))||1,n=$e(e.start);return{start:n,end:qe(n,t)}}function Sn(e,t){void 0===t&&(t=Ct(0));var n=null,r=null;if(e.end){r=$e(e.end);var o=e.end.valueOf()-r.valueOf();o&&o>=wt(t)&&(r=qe(r,1))}return e.start&&(n=$e(e.start),r&&r<=n&&(r=qe(n,1))),{start:n,end:r}}function wn(e,t,n,r){return"year"===r?Ct(n.diffWholeYears(e,t),"year"):"month"===r?Ct(n.diffWholeMonths(e,t),"month"):Ye(e,t)}function Tn(e,t){var n,r,o=[],i=t.start;for(e.sort(Rn),n=0;n<e.length;n+=1)(r=e[n]).start>i&&o.push({start:i,end:r.start}),r.end>i&&(i=r.end);return i<t.end&&o.push({start:i,end:t.end}),o}function Rn(e,t){return e.start.valueOf()-t.start.valueOf()}function kn(e,t){var n=e.start,r=e.end,o=null;return null!==t.start&&(n=null===n?t.start:new Date(Math.max(n.valueOf(),t.start.valueOf()))),null!=t.end&&(r=null===r?t.end:new Date(Math.min(r.valueOf(),t.end.valueOf()))),(null===n||null===r||n<r)&&(o={start:n,end:r}),o}function In(e,t){return(null===e.start?null:e.start.valueOf())===(null===t.start?null:t.start.valueOf())&&(null===e.end?null:e.end.valueOf())===(null===t.end?null:t.end.valueOf())}function Mn(e,t){return(null===e.end||null===t.start||e.end>t.start)&&(null===e.start||null===t.end||e.start<t.end)}function xn(e,t){return(null===e.start||null!==t.start&&t.start>=e.start)&&(null===e.end||null!==t.end&&t.end<=e.end)}function On(e,t){return(null===e.start||t>=e.start)&&(null===e.end||t<e.end)}function Pn(e,t,n,r){var o={},i={},a={},s=[],l=[],u=An(e.defs,t);for(var c in e.defs){"inverse-background"===(p=u[(b=e.defs[c]).defId]).display&&(b.groupId?(o[b.groupId]=[],a[b.groupId]||(a[b.groupId]=b)):i[c]=[])}for(var d in e.instances){var f=e.instances[d],p=u[(b=e.defs[f.defId]).defId],h=f.range,v=!b.allDay&&r?Sn(h,r):h,m=kn(v,n);m&&("inverse-background"===p.display?b.groupId?o[b.groupId].push(m):i[f.defId].push(m):"none"!==p.display&&("background"===p.display?s:l).push({def:b,ui:p,instance:f,range:m,isStart:v.start&&v.start.valueOf()===m.start.valueOf(),isEnd:v.end&&v.end.valueOf()===m.end.valueOf()}))}for(var g in o)for(var y=0,_=Tn(o[g],n);y<_.length;y++){var b,E=_[y];p=u[(b=a[g]).defId];s.push({def:b,ui:p,instance:null,range:E,isStart:!1,isEnd:!1})}for(var c in i)for(var C=0,D=Tn(i[c],n);C<D.length;C++){E=D[C];s.push({def:e.defs[c],ui:u[c],instance:null,range:E,isStart:!1,isEnd:!1})}return{bg:s,fg:l}}function Nn(e,t){e.fcSeg=t}function Hn(e){return e.fcSeg||e.parentNode.fcSeg||null}function An(e,t){return ct(e,(function(e){return Un(e,t)}))}function Un(e,t){var n=[];return t[""]&&n.push(t[""]),t[e.defId]&&n.push(t[e.defId]),n.push(e.ui),hn(n)}function Bn(e){var t=e.eventRange,n=t.def,o=t.instance?t.instance.range:t.range,i=o.start?o.start.valueOf():0,a=o.end?o.end.valueOf():0;return r(r(r({},n.extendedProps),n),{id:n.publicId,start:i,end:a,duration:a-i,allDay:Number(n.allDay),_seg:e})}function Vn(e,t){for(var n=t.pluginHooks.isDraggableTransformers,r=e.eventRange,o=r.def,i=r.ui,a=i.startEditable,s=0,l=n;s<l.length;s++){a=(0,l[s])(a,o,i,t)}return a}function Ln(e,t){return e.isStart&&e.eventRange.ui.durationEditable&&t.options.eventResizableFromStart}function Wn(e,t){return e.isEnd&&e.eventRange.ui.durationEditable}function Fn(e,t,n,r,o,i,a){var s=n.dateEnv,l=n.options,u=l.displayEventTime,c=l.displayEventEnd,d=e.eventRange.def,f=e.eventRange.instance;null==u&&(u=!1!==r),null==c&&(c=!1!==o);var p=f.range.start,h=f.range.end,v=i||e.start||e.eventRange.range.start,m=a||e.end||e.eventRange.range.end,g=$e(p).valueOf()===$e(v).valueOf(),y=$e(Ze(h,-1)).valueOf()===$e(Ze(m,-1)).valueOf();return u&&!d.allDay&&(g||y)?(v=g?p:v,m=y?h:m,c&&d.hasEnd?s.formatRange(v,m,t,{forcedStartTzo:i?null:f.forcedStartTzo,forcedEndTzo:a?null:f.forcedEndTzo}):s.format(v,t,{forcedTzo:i?null:f.forcedStartTzo})):""}function zn(e){var t=["fc-event"];return e.isMirror&&t.push("fc-event-mirror"),e.isDraggable&&t.push("fc-event-draggable"),(e.isStartResizable||e.isEndResizable)&&t.push("fc-event-resizable"),e.isDragging&&t.push("fc-event-dragging"),e.isResizing&&t.push("fc-event-resizing"),e.isSelected&&t.push("fc-event-selected"),e.isStart&&t.push("fc-event-start"),e.isEnd&&t.push("fc-event-end"),e.isPast&&t.push("fc-event-past"),e.isToday&&t.push("fc-event-today"),e.isFuture&&t.push("fc-event-future"),t}function jn(e,t){var n=e.eventRange,r=n.def,o=n.instance,i=r.url;if(i)return{href:i};var a=t.emitter,s=t.options.eventInteractive;return null==s&&null==(s=r.interactive)&&(s=Boolean(a.hasHandlers("eventClick"))),s?Pe((function(e){a.trigger("eventClick",{el:e.target,event:new lr(t,r,o),jsEvent:e,view:t.viewApi})})):{}}var qn={start:nn,end:nn,allDay:Boolean};function Zn(e,t,n){var o=function(e,t){var n=tn(e,qn),o=n.refined,i=n.extra,a=o.start?t.createMarkerMeta(o.start):null,s=o.end?t.createMarkerMeta(o.end):null,l=o.allDay;null==l&&(l=a&&a.isTimeUnspecified&&(!s||s.isTimeUnspecified));return r({range:{start:a?a.marker:null,end:s?s.marker:null},allDay:l},i)}(e,t),i=o.range;if(!i.start)return null;if(!i.end){if(null==n)return null;i.end=t.add(i.start,n)}return o}function Gn(e,t,n){return r(r({},Yn(e,t,n)),{timeZone:t.timeZone})}function Yn(e,t,n){return{start:t.toDate(e.start),end:t.toDate(e.end),startStr:t.formatIso(e.start,{omitTime:n}),endStr:t.formatIso(e.end,{omitTime:n})}}function Kn(e,t,n){var r=bn({editable:!1},n),o=Cn(r.refined,r.extra,"",e.allDay,!0,n);return{def:o,ui:Un(o,t),instance:at(o.defId,e.range),range:e.range,isStart:!0,isEnd:!0}}function Xn(e,t,n){n.emitter.trigger("select",r(r({},$n(e,n)),{jsEvent:t?t.origEvent:null,view:n.viewApi||n.calendarApi.view}))}function $n(e,t){for(var n,o,i={},a=0,s=t.pluginHooks.dateSpanTransforms;a<s.length;a++){var l=s[a];r(i,l(e,t))}return r(i,(n=e,o=t.dateEnv,r(r({},Yn(n.range,o,n.allDay)),{allDay:n.allDay}))),i}function Jn(e,t,n){var r=n.dateEnv,o=n.options,i=t;return e?(i=$e(i),i=r.add(i,o.defaultAllDayEventDuration)):i=r.add(i,o.defaultTimedEventDuration),i}function Qn(e,t,n,r){var o=An(e.defs,t),i={defs:{},instances:{}};for(var a in e.defs){var s=e.defs[a];i.defs[a]=er(s,o[a],n,r)}for(var l in e.instances){var u=e.instances[l];s=i.defs[u.defId];i.instances[l]=tr(u,s,o[u.defId],n,r)}return i}function er(e,t,n,o){var i=n.standardProps||{};null==i.hasEnd&&t.durationEditable&&(n.startDelta||n.endDelta)&&(i.hasEnd=!0);var a=r(r(r({},e),i),{ui:r(r({},e.ui),i.ui)});n.extendedProps&&(a.extendedProps=r(r({},a.extendedProps),n.extendedProps));for(var s=0,l=o.pluginHooks.eventDefMutationAppliers;s<l.length;s++){(0,l[s])(a,n,o)}return!a.hasEnd&&o.options.forceEventDuration&&(a.hasEnd=!0),a}function tr(e,t,n,o,i){var a=i.dateEnv,s=o.standardProps&&!0===o.standardProps.allDay,l=o.standardProps&&!1===o.standardProps.hasEnd,u=r({},e);return s&&(u.range=Dn(u.range)),o.datesDelta&&n.startEditable&&(u.range={start:a.add(u.range.start,o.datesDelta),end:a.add(u.range.end,o.datesDelta)}),o.startDelta&&n.durationEditable&&(u.range={start:a.add(u.range.start,o.startDelta),end:u.range.end}),o.endDelta&&n.durationEditable&&(u.range={start:u.range.start,end:a.add(u.range.end,o.endDelta)}),l&&(u.range={start:u.range.start,end:Jn(t.allDay,u.range.start,i)}),t.allDay&&(u.range={start:$e(u.range.start),end:$e(u.range.end)}),u.range.end<u.range.start&&(u.range.end=Jn(t.allDay,u.range.start,i)),u}var nr=function(){function e(e,t,n){this.type=e,this.getCurrentData=t,this.dateEnv=n}return Object.defineProperty(e.prototype,"calendar",{get:function(){return this.getCurrentData().calendarApi},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"title",{get:function(){return this.getCurrentData().viewTitle},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"activeStart",{get:function(){return this.dateEnv.toDate(this.getCurrentData().dateProfile.activeRange.start)},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"activeEnd",{get:function(){return this.dateEnv.toDate(this.getCurrentData().dateProfile.activeRange.end)},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"currentStart",{get:function(){return this.dateEnv.toDate(this.getCurrentData().dateProfile.currentRange.start)},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"currentEnd",{get:function(){return this.dateEnv.toDate(this.getCurrentData().dateProfile.currentRange.end)},enumerable:!1,configurable:!0}),e.prototype.getOption=function(e){return this.getCurrentData().options[e]},e}(),rr={id:String,defaultAllDay:Boolean,url:String,format:String,events:nn,eventDataTransform:nn,success:nn,failure:nn};function or(e,t,n){var r;if(void 0===n&&(n=ir(t)),"string"==typeof e?r={url:e}:"function"==typeof e||Array.isArray(e)?r={events:e}:"object"==typeof e&&e&&(r=e),r){var o=tn(r,n),i=o.refined,a=o.extra,s=function(e,t){for(var n=t.pluginHooks.eventSourceDefs,r=n.length-1;r>=0;r-=1){var o=n[r].parseMeta(e);if(o)return{sourceDefId:r,meta:o}}return null}(i,t);if(s)return{_raw:e,isFetching:!1,latestFetchId:"",fetchRange:null,defaultAllDay:i.defaultAllDay,eventDataTransform:i.eventDataTransform,success:i.success,failure:i.failure,publicId:i.id||"",sourceId:He(),sourceDefId:s.sourceDefId,meta:s.meta,ui:pn(i,t),extendedProps:a}}return null}function ir(e){return r(r(r({},dn),rr),e.pluginHooks.eventSourceRefiners)}function ar(e,t){return"function"==typeof e&&(e=e()),null==e?t.createNowMarker():t.createMarker(e)}var sr=function(){function e(){}return e.prototype.getCurrentData=function(){return this.currentDataManager.getCurrentData()},e.prototype.dispatch=function(e){return this.currentDataManager.dispatch(e)},Object.defineProperty(e.prototype,"view",{get:function(){return this.getCurrentData().viewApi},enumerable:!1,configurable:!0}),e.prototype.batchRendering=function(e){e()},e.prototype.updateSize=function(){this.trigger("_resize",!0)},e.prototype.setOption=function(e,t){this.dispatch({type:"SET_OPTION",optionName:e,rawOptionValue:t})},e.prototype.getOption=function(e){return this.currentDataManager.currentCalendarOptionsInput[e]},e.prototype.getAvailableLocaleCodes=function(){return Object.keys(this.getCurrentData().availableRawLocales)},e.prototype.on=function(e,t){var n=this.currentDataManager;n.currentCalendarOptionsRefiners[e]?n.emitter.on(e,t):console.warn("Unknown listener name '"+e+"'")},e.prototype.off=function(e,t){this.currentDataManager.emitter.off(e,t)},e.prototype.trigger=function(e){for(var t,n=[],r=1;r<arguments.length;r++)n[r-1]=arguments[r];(t=this.currentDataManager.emitter).trigger.apply(t,o([e],n))},e.prototype.changeView=function(e,t){var n=this;this.batchRendering((function(){if(n.unselect(),t)if(t.start&&t.end)n.dispatch({type:"CHANGE_VIEW_TYPE",viewType:e}),n.dispatch({type:"SET_OPTION",optionName:"visibleRange",rawOptionValue:t});else{var r=n.getCurrentData().dateEnv;n.dispatch({type:"CHANGE_VIEW_TYPE",viewType:e,dateMarker:r.createMarker(t)})}else n.dispatch({type:"CHANGE_VIEW_TYPE",viewType:e})}))},e.prototype.zoomTo=function(e,t){var n;t=t||"day",n=this.getCurrentData().viewSpecs[t]||this.getUnitViewSpec(t),this.unselect(),n?this.dispatch({type:"CHANGE_VIEW_TYPE",viewType:n.type,dateMarker:e}):this.dispatch({type:"CHANGE_DATE",dateMarker:e})},e.prototype.getUnitViewSpec=function(e){var t,n,r=this.getCurrentData(),o=r.viewSpecs,i=r.toolbarConfig,a=[].concat(i.header?i.header.viewsWithButtons:[],i.footer?i.footer.viewsWithButtons:[]);for(var s in o)a.push(s);for(t=0;t<a.length;t+=1)if((n=o[a[t]])&&n.singleUnit===e)return n;return null},e.prototype.prev=function(){this.unselect(),this.dispatch({type:"PREV"})},e.prototype.next=function(){this.unselect(),this.dispatch({type:"NEXT"})},e.prototype.prevYear=function(){var e=this.getCurrentData();this.unselect(),this.dispatch({type:"CHANGE_DATE",dateMarker:e.dateEnv.addYears(e.currentDate,-1)})},e.prototype.nextYear=function(){var e=this.getCurrentData();this.unselect(),this.dispatch({type:"CHANGE_DATE",dateMarker:e.dateEnv.addYears(e.currentDate,1)})},e.prototype.today=function(){var e=this.getCurrentData();this.unselect(),this.dispatch({type:"CHANGE_DATE",dateMarker:ar(e.calendarOptions.now,e.dateEnv)})},e.prototype.gotoDate=function(e){var t=this.getCurrentData();this.unselect(),this.dispatch({type:"CHANGE_DATE",dateMarker:t.dateEnv.createMarker(e)})},e.prototype.incrementDate=function(e){var t=this.getCurrentData(),n=Ct(e);n&&(this.unselect(),this.dispatch({type:"CHANGE_DATE",dateMarker:t.dateEnv.add(t.currentDate,n)}))},e.prototype.getDate=function(){var e=this.getCurrentData();return e.dateEnv.toDate(e.currentDate)},e.prototype.formatDate=function(e,t){var n=this.getCurrentData().dateEnv;return n.format(n.createMarker(e),Zt(t))},e.prototype.formatRange=function(e,t,n){var r=this.getCurrentData().dateEnv;return r.formatRange(r.createMarker(e),r.createMarker(t),Zt(n),n)},e.prototype.formatIso=function(e,t){var n=this.getCurrentData().dateEnv;return n.formatIso(n.createMarker(e),{omitTime:t})},e.prototype.select=function(e,t){var n;n=null==t?null!=e.start?e:{start:e,end:null}:{start:e,end:t};var r=this.getCurrentData(),o=Zn(n,r.dateEnv,Ct({days:1}));o&&(this.dispatch({type:"SELECT_DATES",selection:o}),Xn(o,null,r))},e.prototype.unselect=function(e){var t=this.getCurrentData();t.dateSelection&&(this.dispatch({type:"UNSELECT_DATES"}),function(e,t){t.emitter.trigger("unselect",{jsEvent:e?e.origEvent:null,view:t.viewApi||t.calendarApi.view})}(e,t))},e.prototype.addEvent=function(e,t){if(e instanceof lr){var n=e._def,r=e._instance;return this.getCurrentData().eventStore.defs[n.defId]||(this.dispatch({type:"ADD_EVENTS",eventStore:on({def:n,instance:r})}),this.triggerEventAdd(e)),e}var o,i=this.getCurrentData();if(t instanceof ye)o=t.internalEventSource;else if("boolean"==typeof t)t&&(o=ft(i.eventSources)[0]);else if(null!=t){var a=this.getEventSourceById(t);if(!a)return console.warn('Could not find an event source with ID "'+t+'"'),null;o=a.internalEventSource}var s=_n(e,o,i,!1);if(s){var l=new lr(i,s.def,s.def.recurringDef?null:s.instance);return this.dispatch({type:"ADD_EVENTS",eventStore:on(s)}),this.triggerEventAdd(l),l}return null},e.prototype.triggerEventAdd=function(e){var t=this;this.getCurrentData().emitter.trigger("eventAdd",{event:e,relatedEvents:[],revert:function(){t.dispatch({type:"REMOVE_EVENTS",eventStore:ur(e)})}})},e.prototype.getEventById=function(e){var t=this.getCurrentData(),n=t.eventStore,r=n.defs,o=n.instances;for(var i in e=String(e),r){var a=r[i];if(a.publicId===e){if(a.recurringDef)return new lr(t,a,null);for(var s in o){var l=o[s];if(l.defId===a.defId)return new lr(t,a,l)}}}return null},e.prototype.getEvents=function(){var e=this.getCurrentData();return cr(e.eventStore,e)},e.prototype.removeAllEvents=function(){this.dispatch({type:"REMOVE_ALL_EVENTS"})},e.prototype.getEventSources=function(){var e=this.getCurrentData(),t=e.eventSources,n=[];for(var r in t)n.push(new ye(e,t[r]));return n},e.prototype.getEventSourceById=function(e){var t=this.getCurrentData(),n=t.eventSources;for(var r in e=String(e),n)if(n[r].publicId===e)return new ye(t,n[r]);return null},e.prototype.addEventSource=function(e){var t=this.getCurrentData();if(e instanceof ye)return t.eventSources[e.internalEventSource.sourceId]||this.dispatch({type:"ADD_EVENT_SOURCES",sources:[e.internalEventSource]}),e;var n=or(e,t);return n?(this.dispatch({type:"ADD_EVENT_SOURCES",sources:[n]}),new ye(t,n)):null},e.prototype.removeAllEventSources=function(){this.dispatch({type:"REMOVE_ALL_EVENT_SOURCES"})},e.prototype.refetchEvents=function(){this.dispatch({type:"FETCH_EVENT_SOURCES",isRefetch:!0})},e.prototype.scrollToTime=function(e){var t=Ct(e);t&&this.trigger("_scrollRequest",{time:t})},e}(),lr=function(){function e(e,t,n){this._context=e,this._def=t,this._instance=n||null}return e.prototype.setProp=function(e,t){var n,r;if(e in gn)console.warn("Could not set date-related prop 'name'. Use one of the date-related methods instead.");else if("id"===e)t=mn[e](t),this.mutate({standardProps:{publicId:t}});else if(e in mn)t=mn[e](t),this.mutate({standardProps:(n={},n[e]=t,n)});else if(e in dn){var o=dn[e](t);"color"===e?o={backgroundColor:t,borderColor:t}:"editable"===e?o={startEditable:t,durationEditable:t}:((r={})[e]=t,o=r),this.mutate({standardProps:{ui:o}})}else console.warn("Could not set prop '"+e+"'. Use setExtendedProp instead.")},e.prototype.setExtendedProp=function(e,t){var n;this.mutate({extendedProps:(n={},n[e]=t,n)})},e.prototype.setStart=function(e,t){void 0===t&&(t={});var n=this._context.dateEnv,r=n.createMarker(e);if(r&&this._instance){var o=wn(this._instance.range.start,r,n,t.granularity);t.maintainDuration?this.mutate({datesDelta:o}):this.mutate({startDelta:o})}},e.prototype.setEnd=function(e,t){void 0===t&&(t={});var n,r=this._context.dateEnv;if((null==e||(n=r.createMarker(e)))&&this._instance)if(n){var o=wn(this._instance.range.end,n,r,t.granularity);this.mutate({endDelta:o})}else this.mutate({standardProps:{hasEnd:!1}})},e.prototype.setDates=function(e,t,n){void 0===n&&(n={});var r,o,i,a=this._context.dateEnv,s={allDay:n.allDay},l=a.createMarker(e);if(l&&((null==t||(r=a.createMarker(t)))&&this._instance)){var u=this._instance.range;!0===n.allDay&&(u=Dn(u));var c=wn(u.start,l,a,n.granularity);if(r){var d=wn(u.end,r,a,n.granularity);i=d,(o=c).years===i.years&&o.months===i.months&&o.days===i.days&&o.milliseconds===i.milliseconds?this.mutate({datesDelta:c,standardProps:s}):this.mutate({startDelta:c,endDelta:d,standardProps:s})}else s.hasEnd=!1,this.mutate({datesDelta:c,standardProps:s})}},e.prototype.moveStart=function(e){var t=Ct(e);t&&this.mutate({startDelta:t})},e.prototype.moveEnd=function(e){var t=Ct(e);t&&this.mutate({endDelta:t})},e.prototype.moveDates=function(e){var t=Ct(e);t&&this.mutate({datesDelta:t})},e.prototype.setAllDay=function(e,t){void 0===t&&(t={});var n={allDay:e},r=t.maintainDuration;null==r&&(r=this._context.options.allDayMaintainDuration),this._def.allDay!==e&&(n.hasEnd=r),this.mutate({standardProps:n})},e.prototype.formatRange=function(e){var t=this._context.dateEnv,n=this._instance,r=Zt(e);return this._def.hasEnd?t.formatRange(n.range.start,n.range.end,r,{forcedStartTzo:n.forcedStartTzo,forcedEndTzo:n.forcedEndTzo}):t.format(n.range.start,r,{forcedTzo:n.forcedStartTzo})},e.prototype.mutate=function(t){var n=this._instance;if(n){var r=this._def,o=this._context,i=o.getCurrentData().eventStore,a=an(i,n.instanceId);a=Qn(a,{"":{display:"",startEditable:!0,durationEditable:!0,constraints:[],overlap:null,allows:[],backgroundColor:"",borderColor:"",textColor:"",classNames:[]}},t,o);var s=new e(o,r,n);this._def=a.defs[r.defId],this._instance=a.instances[n.instanceId],o.dispatch({type:"MERGE_EVENTS",eventStore:a}),o.emitter.trigger("eventChange",{oldEvent:s,event:this,relatedEvents:cr(a,o,n),revert:function(){o.dispatch({type:"RESET_EVENTS",eventStore:i})}})}},e.prototype.remove=function(){var e=this._context,t=ur(this);e.dispatch({type:"REMOVE_EVENTS",eventStore:t}),e.emitter.trigger("eventRemove",{event:this,relatedEvents:[],revert:function(){e.dispatch({type:"MERGE_EVENTS",eventStore:t})}})},Object.defineProperty(e.prototype,"source",{get:function(){var e=this._def.sourceId;return e?new ye(this._context,this._context.getCurrentData().eventSources[e]):null},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"start",{get:function(){return this._instance?this._context.dateEnv.toDate(this._instance.range.start):null},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"end",{get:function(){return this._instance&&this._def.hasEnd?this._context.dateEnv.toDate(this._instance.range.end):null},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"startStr",{get:function(){var e=this._instance;return e?this._context.dateEnv.formatIso(e.range.start,{omitTime:this._def.allDay,forcedTzo:e.forcedStartTzo}):""},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"endStr",{get:function(){var e=this._instance;return e&&this._def.hasEnd?this._context.dateEnv.formatIso(e.range.end,{omitTime:this._def.allDay,forcedTzo:e.forcedEndTzo}):""},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"id",{get:function(){return this._def.publicId},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"groupId",{get:function(){return this._def.groupId},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"allDay",{get:function(){return this._def.allDay},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"title",{get:function(){return this._def.title},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"url",{get:function(){return this._def.url},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"display",{get:function(){return this._def.ui.display||"auto"},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"startEditable",{get:function(){return this._def.ui.startEditable},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"durationEditable",{get:function(){return this._def.ui.durationEditable},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"constraint",{get:function(){return this._def.ui.constraints[0]||null},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"overlap",{get:function(){return this._def.ui.overlap},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"allow",{get:function(){return this._def.ui.allows[0]||null},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"backgroundColor",{get:function(){return this._def.ui.backgroundColor},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"borderColor",{get:function(){return this._def.ui.borderColor},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"textColor",{get:function(){return this._def.ui.textColor},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"classNames",{get:function(){return this._def.ui.classNames},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"extendedProps",{get:function(){return this._def.extendedProps},enumerable:!1,configurable:!0}),e.prototype.toPlainObject=function(e){void 0===e&&(e={});var t=this._def,n=t.ui,o=this.startStr,i=this.endStr,a={};return t.title&&(a.title=t.title),o&&(a.start=o),i&&(a.end=i),t.publicId&&(a.id=t.publicId),t.groupId&&(a.groupId=t.groupId),t.url&&(a.url=t.url),n.display&&"auto"!==n.display&&(a.display=n.display),e.collapseColor&&n.backgroundColor&&n.backgroundColor===n.borderColor?a.color=n.backgroundColor:(n.backgroundColor&&(a.backgroundColor=n.backgroundColor),n.borderColor&&(a.borderColor=n.borderColor)),n.textColor&&(a.textColor=n.textColor),n.classNames.length&&(a.classNames=n.classNames),Object.keys(t.extendedProps).length&&(e.collapseExtendedProps?r(a,t.extendedProps):a.extendedProps=t.extendedProps),a},e.prototype.toJSON=function(){return this.toPlainObject()},e}();function ur(e){var t,n,r=e._def,o=e._instance;return{defs:(t={},t[r.defId]=r,t),instances:o?(n={},n[o.instanceId]=o,n):{}}}function cr(e,t,n){var r=e.defs,o=e.instances,i=[],a=n?n.instanceId:"";for(var s in o){var l=o[s],u=r[l.defId];l.instanceId!==a&&i.push(new lr(t,u,l))}return i}var dr={};var fr,pr=function(){function e(){}return e.prototype.getMarkerYear=function(e){return e.getUTCFullYear()},e.prototype.getMarkerMonth=function(e){return e.getUTCMonth()},e.prototype.getMarkerDay=function(e){return e.getUTCDate()},e.prototype.arrayToMarker=function(e){return rt(e)},e.prototype.markerToArray=function(e){return nt(e)},e}();fr=pr,dr["gregory"]=fr;var hr=/^\s*(\d{4})(-?(\d{2})(-?(\d{2})([T ](\d{2}):?(\d{2})(:?(\d{2})(\.(\d+))?)?(Z|(([-+])(\d{2})(:?(\d{2}))?))?)?)?)?$/;function vr(e){var t=hr.exec(e);if(t){var n=new Date(Date.UTC(Number(t[1]),t[3]?Number(t[3])-1:0,Number(t[5]||1),Number(t[7]||0),Number(t[8]||0),Number(t[10]||0),t[12]?1e3*Number("0."+t[12]):0));if(ot(n)){var r=null;return t[13]&&(r=("-"===t[15]?-1:1)*(60*Number(t[16]||0)+Number(t[18]||0))),{marker:n,isTimeUnspecified:!t[6],timeZoneOffset:r}}}return null}var mr=function(){function e(e){var t=this.timeZone=e.timeZone,n="local"!==t&&"UTC"!==t;e.namedTimeZoneImpl&&n&&(this.namedTimeZoneImpl=new e.namedTimeZoneImpl(t)),this.canComputeOffset=Boolean(!n||this.namedTimeZoneImpl),this.calendarSystem=function(e){return new dr[e]}(e.calendarSystem),this.locale=e.locale,this.weekDow=e.locale.week.dow,this.weekDoy=e.locale.week.doy,"ISO"===e.weekNumberCalculation&&(this.weekDow=1,this.weekDoy=4),"number"==typeof e.firstDay&&(this.weekDow=e.firstDay),"function"==typeof e.weekNumberCalculation&&(this.weekNumberFunc=e.weekNumberCalculation),this.weekText=null!=e.weekText?e.weekText:e.locale.options.weekText,this.weekTextLong=(null!=e.weekTextLong?e.weekTextLong:e.locale.options.weekTextLong)||this.weekText,this.cmdFormatter=e.cmdFormatter,this.defaultSeparator=e.defaultSeparator}return e.prototype.createMarker=function(e){var t=this.createMarkerMeta(e);return null===t?null:t.marker},e.prototype.createNowMarker=function(){return this.canComputeOffset?this.timestampToMarker((new Date).valueOf()):rt(et(new Date))},e.prototype.createMarkerMeta=function(e){if("string"==typeof e)return this.parse(e);var t=null;return"number"==typeof e?t=this.timestampToMarker(e):e instanceof Date?(e=e.valueOf(),isNaN(e)||(t=this.timestampToMarker(e))):Array.isArray(e)&&(t=rt(e)),null!==t&&ot(t)?{marker:t,isTimeUnspecified:!1,forcedTzo:null}:null},e.prototype.parse=function(e){var t=vr(e);if(null===t)return null;var n=t.marker,r=null;return null!==t.timeZoneOffset&&(this.canComputeOffset?n=this.timestampToMarker(n.valueOf()-60*t.timeZoneOffset*1e3):r=t.timeZoneOffset),{marker:n,isTimeUnspecified:t.isTimeUnspecified,forcedTzo:r}},e.prototype.getYear=function(e){return this.calendarSystem.getMarkerYear(e)},e.prototype.getMonth=function(e){return this.calendarSystem.getMarkerMonth(e)},e.prototype.add=function(e,t){var n=this.calendarSystem.markerToArray(e);return n[0]+=t.years,n[1]+=t.months,n[2]+=t.days,n[6]+=t.milliseconds,this.calendarSystem.arrayToMarker(n)},e.prototype.subtract=function(e,t){var n=this.calendarSystem.markerToArray(e);return n[0]-=t.years,n[1]-=t.months,n[2]-=t.days,n[6]-=t.milliseconds,this.calendarSystem.arrayToMarker(n)},e.prototype.addYears=function(e,t){var n=this.calendarSystem.markerToArray(e);return n[0]+=t,this.calendarSystem.arrayToMarker(n)},e.prototype.addMonths=function(e,t){var n=this.calendarSystem.markerToArray(e);return n[1]+=t,this.calendarSystem.arrayToMarker(n)},e.prototype.diffWholeYears=function(e,t){var n=this.calendarSystem;return it(e)===it(t)&&n.getMarkerDay(e)===n.getMarkerDay(t)&&n.getMarkerMonth(e)===n.getMarkerMonth(t)?n.getMarkerYear(t)-n.getMarkerYear(e):null},e.prototype.diffWholeMonths=function(e,t){var n=this.calendarSystem;return it(e)===it(t)&&n.getMarkerDay(e)===n.getMarkerDay(t)?n.getMarkerMonth(t)-n.getMarkerMonth(e)+12*(n.getMarkerYear(t)-n.getMarkerYear(e)):null},e.prototype.greatestWholeUnit=function(e,t){var n=this.diffWholeYears(e,t);return null!==n?{unit:"year",value:n}:null!==(n=this.diffWholeMonths(e,t))?{unit:"month",value:n}:null!==(n=Ke(e,t))?{unit:"week",value:n}:null!==(n=Xe(e,t))?{unit:"day",value:n}:Fe(n=function(e,t){return(t.valueOf()-e.valueOf())/36e5}(e,t))?{unit:"hour",value:n}:Fe(n=function(e,t){return(t.valueOf()-e.valueOf())/6e4}(e,t))?{unit:"minute",value:n}:Fe(n=function(e,t){return(t.valueOf()-e.valueOf())/1e3}(e,t))?{unit:"second",value:n}:{unit:"millisecond",value:t.valueOf()-e.valueOf()}},e.prototype.countDurationsBetween=function(e,t,n){var r;return n.years&&null!==(r=this.diffWholeYears(e,t))?r/(St(n)/365):n.months&&null!==(r=this.diffWholeMonths(e,t))?r/function(e){return St(e)/30}(n):n.days&&null!==(r=Xe(e,t))?r/St(n):(t.valueOf()-e.valueOf())/wt(n)},e.prototype.startOf=function(e,t){return"year"===t?this.startOfYear(e):"month"===t?this.startOfMonth(e):"week"===t?this.startOfWeek(e):"day"===t?$e(e):"hour"===t?function(e){return rt([e.getUTCFullYear(),e.getUTCMonth(),e.getUTCDate(),e.getUTCHours()])}(e):"minute"===t?function(e){return rt([e.getUTCFullYear(),e.getUTCMonth(),e.getUTCDate(),e.getUTCHours(),e.getUTCMinutes()])}(e):"second"===t?function(e){return rt([e.getUTCFullYear(),e.getUTCMonth(),e.getUTCDate(),e.getUTCHours(),e.getUTCMinutes(),e.getUTCSeconds()])}(e):null},e.prototype.startOfYear=function(e){return this.calendarSystem.arrayToMarker([this.calendarSystem.getMarkerYear(e)])},e.prototype.startOfMonth=function(e){return this.calendarSystem.arrayToMarker([this.calendarSystem.getMarkerYear(e),this.calendarSystem.getMarkerMonth(e)])},e.prototype.startOfWeek=function(e){return this.calendarSystem.arrayToMarker([this.calendarSystem.getMarkerYear(e),this.calendarSystem.getMarkerMonth(e),e.getUTCDate()-(e.getUTCDay()-this.weekDow+7)%7])},e.prototype.computeWeekNumber=function(e){return this.weekNumberFunc?this.weekNumberFunc(this.toDate(e)):function(e,t,n){var r=e.getUTCFullYear(),o=Je(e,r,t,n);if(o<1)return Je(e,r-1,t,n);var i=Je(e,r+1,t,n);return i>=1?Math.min(o,i):o}(e,this.weekDow,this.weekDoy)},e.prototype.format=function(e,t,n){return void 0===n&&(n={}),t.format({marker:e,timeZoneOffset:null!=n.forcedTzo?n.forcedTzo:this.offsetForMarker(e)},this)},e.prototype.formatRange=function(e,t,n,r){return void 0===r&&(r={}),r.isEndExclusive&&(t=Ze(t,-1)),n.formatRange({marker:e,timeZoneOffset:null!=r.forcedStartTzo?r.forcedStartTzo:this.offsetForMarker(e)},{marker:t,timeZoneOffset:null!=r.forcedEndTzo?r.forcedEndTzo:this.offsetForMarker(t)},this,r.defaultSeparator)},e.prototype.formatIso=function(e,t){void 0===t&&(t={});var n=null;return t.omitTimeZoneOffset||(n=null!=t.forcedTzo?t.forcedTzo:this.offsetForMarker(e)),Rt(e,n,t.omitTime)},e.prototype.timestampToMarker=function(e){return"local"===this.timeZone?rt(et(new Date(e))):"UTC"!==this.timeZone&&this.namedTimeZoneImpl?rt(this.namedTimeZoneImpl.timestampToArray(e)):new Date(e)},e.prototype.offsetForMarker=function(e){return"local"===this.timeZone?-tt(nt(e)).getTimezoneOffset():"UTC"===this.timeZone?0:this.namedTimeZoneImpl?this.namedTimeZoneImpl.offsetForArray(nt(e)):null},e.prototype.toDate=function(e,t){return"local"===this.timeZone?tt(nt(e)):"UTC"===this.timeZone?new Date(e.valueOf()):this.namedTimeZoneImpl?new Date(e.valueOf()-1e3*this.namedTimeZoneImpl.offsetForArray(nt(e))*60):new Date(e.valueOf()-(t||0))},e}(),gr=[],yr={code:"en",week:{dow:0,doy:4},direction:"ltr",buttonText:{prev:"prev",next:"next",prevYear:"prev year",nextYear:"next year",year:"year",today:"today",month:"month",week:"week",day:"day",list:"list"},weekText:"W",weekTextLong:"Week",closeHint:"Close",timeHint:"Time",eventHint:"Event",allDayText:"all-day",moreLinkText:"more",noEventsText:"No events to display"},_r=r(r({},yr),{buttonHints:{prev:"Previous $0",next:"Next $0",today:function(e,t){return"day"===t?"Today":"This "+e}},viewHint:"$0 view",navLinkHint:"Go to $0",moreLinkHint:function(e){return"Show "+e+" more event"+(1===e?"":"s")}});function br(e){for(var t=e.length>0?e[0].code:"en",n=gr.concat(e),r={en:_r},o=0,i=n;o<i.length;o++){var a=i[o];r[a.code]=a}return{map:r,defaultCode:t}}function Er(e,t){return"object"!=typeof e||Array.isArray(e)?function(e,t){var n=[].concat(e||[]),r=function(e,t){for(var n=0;n<e.length;n+=1)for(var r=e[n].toLocaleLowerCase().split("-"),o=r.length;o>0;o-=1){var i=r.slice(0,o).join("-");if(t[i])return t[i]}return null}(n,t)||_r;return Cr(e,n,r)}(e,t):Cr(e.code,[e.code],e)}function Cr(e,t,n){var r=lt([yr,n],["buttonText"]);delete r.code;var o=r.week;return delete r.week,{codeArg:e,codes:t,week:o,simpleNumberFormat:new Intl.NumberFormat(e),options:r}}function Dr(e){var t=Er(e.locale||"en",br([]).map);return new mr(r(r({timeZone:Yt.timeZone,calendarSystem:"gregory"},e),{locale:t}))}var Sr,wr={startTime:"09:00",endTime:"17:00",daysOfWeek:[1,2,3,4,5],display:"inverse-background",classNames:"fc-non-business",groupId:"_businessHours"};function Tr(e,t){return rn(function(e){var t;t=!0===e?[{}]:Array.isArray(e)?e.filter((function(e){return e.daysOfWeek})):"object"==typeof e&&e?[e]:[];return t=t.map((function(e){return r(r({},wr),e)}))}(e),null,t)}function Rr(e,t){var n={left:Math.max(e.left,t.left),right:Math.min(e.right,t.right),top:Math.max(e.top,t.top),bottom:Math.min(e.bottom,t.bottom)};return n.left<n.right&&n.top<n.bottom&&n}function kr(){return null==Sr&&(Sr=function(){if("undefined"==typeof document)return!0;var e=document.createElement("div");e.style.position="absolute",e.style.top="0px",e.style.left="0px",e.innerHTML="<table><tr><td><div></div></td></tr></table>",e.querySelector("table").style.height="100px",e.querySelector("div").style.height="100%",document.body.appendChild(e);var t=e.querySelector("div").offsetHeight>0;return document.body.removeChild(e),t}()),Sr}var Ir={defs:{},instances:{}},Mr=function(){function e(){this.getKeysForEventDefs=xt(this._getKeysForEventDefs),this.splitDateSelection=xt(this._splitDateSpan),this.splitEventStore=xt(this._splitEventStore),this.splitIndividualUi=xt(this._splitIndividualUi),this.splitEventDrag=xt(this._splitInteraction),this.splitEventResize=xt(this._splitInteraction),this.eventUiBuilders={}}return e.prototype.splitProps=function(e){var t=this,n=this.getKeyInfo(e),r=this.getKeysForEventDefs(e.eventStore),o=this.splitDateSelection(e.dateSelection),i=this.splitIndividualUi(e.eventUiBases,r),a=this.splitEventStore(e.eventStore,r),s=this.splitEventDrag(e.eventDrag),l=this.splitEventResize(e.eventResize),u={};for(var c in this.eventUiBuilders=ct(n,(function(e,n){return t.eventUiBuilders[n]||xt(xr)})),n){var d=n[c],f=a[c]||Ir,p=this.eventUiBuilders[c];u[c]={businessHours:d.businessHours||e.businessHours,dateSelection:o[c]||null,eventStore:f,eventUiBases:p(e.eventUiBases[""],d.ui,i[c]),eventSelection:f.instances[e.eventSelection]?e.eventSelection:"",eventDrag:s[c]||null,eventResize:l[c]||null}}return u},e.prototype._splitDateSpan=function(e){var t={};if(e)for(var n=0,r=this.getKeysForDateSpan(e);n<r.length;n++){t[r[n]]=e}return t},e.prototype._getKeysForEventDefs=function(e){var t=this;return ct(e.defs,(function(e){return t.getKeysForEventDef(e)}))},e.prototype._splitEventStore=function(e,t){var n=e.defs,r=e.instances,o={};for(var i in n)for(var a=0,s=t[i];a<s.length;a++){o[f=s[a]]||(o[f]={defs:{},instances:{}}),o[f].defs[i]=n[i]}for(var l in r)for(var u=r[l],c=0,d=t[u.defId];c<d.length;c++){var f;o[f=d[c]]&&(o[f].instances[l]=u)}return o},e.prototype._splitIndividualUi=function(e,t){var n={};for(var r in e)if(r)for(var o=0,i=t[r];o<i.length;o++){var a=i[o];n[a]||(n[a]={}),n[a][r]=e[r]}return n},e.prototype._splitInteraction=function(e){var t={};if(e){var n=this._splitEventStore(e.affectedEvents,this._getKeysForEventDefs(e.affectedEvents)),r=this._getKeysForEventDefs(e.mutatedEvents),o=this._splitEventStore(e.mutatedEvents,r),i=function(r){t[r]||(t[r]={affectedEvents:n[r]||Ir,mutatedEvents:o[r]||Ir,isEvent:e.isEvent})};for(var a in n)i(a);for(var a in o)i(a)}return t},e}();function xr(e,t,n){var o=[];e&&o.push(e),t&&o.push(t);var i={"":hn(o)};return n&&r(i,n),i}function Or(e,t,n,r){return{dow:e.getUTCDay(),isDisabled:Boolean(r&&!On(r.activeRange,e)),isOther:Boolean(r&&!On(r.currentRange,e)),isToday:Boolean(t&&On(t,e)),isPast:Boolean(n?e<n:!!t&&e<t.start),isFuture:Boolean(n?e>n:!!t&&e>=t.end)}}function Pr(e,t){var n=["fc-day","fc-day-"+je[e.dow]];return e.isDisabled?n.push("fc-day-disabled"):(e.isToday&&(n.push("fc-day-today"),n.push(t.getClass("today"))),e.isPast&&n.push("fc-day-past"),e.isFuture&&n.push("fc-day-future"),e.isOther&&n.push("fc-day-other")),n}var Nr=Zt({year:"numeric",month:"long",day:"numeric"}),Hr=Zt({week:"long"});function Ar(e,t,n,o){void 0===n&&(n="day"),void 0===o&&(o=!0);var i=e.dateEnv,a=e.options,s=e.calendarApi,l=i.format(t,"week"===n?Hr:Nr);if(a.navLinks){var u=i.toDate(t),c=function(e){var r="day"===n?a.navLinkDayClick:"week"===n?a.navLinkWeekClick:null;"function"==typeof r?r.call(s,i.toDate(t),e):("string"==typeof r&&(n=r),s.zoomTo(t,n))};return r({title:We(a.navLinkHint,[l,u],l),"data-navlink":""},o?Oe(c):{onClick:c})}return{"aria-label":l}}var Ur,Br=null;function Vr(){return null===Br&&(Br=function(){var e=document.createElement("div");Se(e,{position:"absolute",top:-1e3,left:0,border:0,padding:0,overflow:"scroll",direction:"rtl"}),e.innerHTML="<div></div>",document.body.appendChild(e);var t=e.firstChild.getBoundingClientRect().left>e.getBoundingClientRect().left;return _e(e),t}()),Br}function Lr(){return Ur||(Ur=function(){var e=document.createElement("div");e.style.overflow="scroll",e.style.position="absolute",e.style.top="-9999px",e.style.left="-9999px",document.body.appendChild(e);var t=Wr(e);return document.body.removeChild(e),t}()),Ur}function Wr(e){return{x:e.offsetHeight-e.clientHeight,y:e.offsetWidth-e.clientWidth}}function Fr(e,t){void 0===t&&(t=!1);var n=window.getComputedStyle(e),r=parseInt(n.borderLeftWidth,10)||0,o=parseInt(n.borderRightWidth,10)||0,i=parseInt(n.borderTopWidth,10)||0,a=parseInt(n.borderBottomWidth,10)||0,s=Wr(e),l=s.y-r-o,u={borderLeft:r,borderRight:o,borderTop:i,borderBottom:a,scrollbarBottom:s.x-i-a,scrollbarLeft:0,scrollbarRight:0};return Vr()&&"rtl"===n.direction?u.scrollbarLeft=l:u.scrollbarRight=l,t&&(u.paddingLeft=parseInt(n.paddingLeft,10)||0,u.paddingRight=parseInt(n.paddingRight,10)||0,u.paddingTop=parseInt(n.paddingTop,10)||0,u.paddingBottom=parseInt(n.paddingBottom,10)||0),u}function zr(e){var t=e.getBoundingClientRect();return{left:t.left+window.pageXOffset,top:t.top+window.pageYOffset,right:t.right+window.pageXOffset,bottom:t.bottom+window.pageYOffset}}function jr(e){for(var t=[];e instanceof HTMLElement;){var n=window.getComputedStyle(e);if("fixed"===n.position)break;/(auto|scroll)/.test(n.overflow+n.overflowY+n.overflowX)&&t.push(e),e=e.parentNode}return t}function qr(e,t,n){var r=!1,o=function(){r||(r=!0,t.apply(this,arguments))},i=function(){r||(r=!0,n&&n.apply(this,arguments))},a=e(o,i);a&&"function"==typeof a.then&&a.then(o,i)}var Zr=function(){function e(){this.handlers={},this.thisContext=null}return e.prototype.setThisContext=function(e){this.thisContext=e},e.prototype.setOptions=function(e){this.options=e},e.prototype.on=function(e,t){!function(e,t,n){(e[t]||(e[t]=[])).push(n)}(this.handlers,e,t)},e.prototype.off=function(e,t){!function(e,t,n){n?e[t]&&(e[t]=e[t].filter((function(e){return e!==n}))):delete e[t]}(this.handlers,e,t)},e.prototype.trigger=function(e){for(var t=[],n=1;n<arguments.length;n++)t[n-1]=arguments[n];for(var r=this.handlers[e]||[],o=this.options&&this.options[e],i=[].concat(o||[],r),a=0,s=i;a<s.length;a++){var l=s[a];l.apply(this.thisContext,t)}},e.prototype.hasHandlers=function(e){return Boolean(this.handlers[e]&&this.handlers[e].length||this.options&&this.options[e])},e}();var Gr=function(){function e(e,t,n,r){this.els=t;var o=this.originClientRect=e.getBoundingClientRect();n&&this.buildElHorizontals(o.left),r&&this.buildElVerticals(o.top)}return e.prototype.buildElHorizontals=function(e){for(var t=[],n=[],r=0,o=this.els;r<o.length;r++){var i=o[r].getBoundingClientRect();t.push(i.left-e),n.push(i.right-e)}this.lefts=t,this.rights=n},e.prototype.buildElVerticals=function(e){for(var t=[],n=[],r=0,o=this.els;r<o.length;r++){var i=o[r].getBoundingClientRect();t.push(i.top-e),n.push(i.bottom-e)}this.tops=t,this.bottoms=n},e.prototype.leftToIndex=function(e){var t,n=this.lefts,r=this.rights,o=n.length;for(t=0;t<o;t+=1)if(e>=n[t]&&e<r[t])return t},e.prototype.topToIndex=function(e){var t,n=this.tops,r=this.bottoms,o=n.length;for(t=0;t<o;t+=1)if(e>=n[t]&&e<r[t])return t},e.prototype.getWidth=function(e){return this.rights[e]-this.lefts[e]},e.prototype.getHeight=function(e){return this.bottoms[e]-this.tops[e]},e}(),Yr=function(){function e(){}return e.prototype.getMaxScrollTop=function(){return this.getScrollHeight()-this.getClientHeight()},e.prototype.getMaxScrollLeft=function(){return this.getScrollWidth()-this.getClientWidth()},e.prototype.canScrollVertically=function(){return this.getMaxScrollTop()>0},e.prototype.canScrollHorizontally=function(){return this.getMaxScrollLeft()>0},e.prototype.canScrollUp=function(){return this.getScrollTop()>0},e.prototype.canScrollDown=function(){return this.getScrollTop()<this.getMaxScrollTop()},e.prototype.canScrollLeft=function(){return this.getScrollLeft()>0},e.prototype.canScrollRight=function(){return this.getScrollLeft()<this.getMaxScrollLeft()},e}(),Kr=function(e){function t(t){var n=e.call(this)||this;return n.el=t,n}return n(t,e),t.prototype.getScrollTop=function(){return this.el.scrollTop},t.prototype.getScrollLeft=function(){return this.el.scrollLeft},t.prototype.setScrollTop=function(e){this.el.scrollTop=e},t.prototype.setScrollLeft=function(e){this.el.scrollLeft=e},t.prototype.getScrollWidth=function(){return this.el.scrollWidth},t.prototype.getScrollHeight=function(){return this.el.scrollHeight},t.prototype.getClientHeight=function(){return this.el.clientHeight},t.prototype.getClientWidth=function(){return this.el.clientWidth},t}(Yr),Xr=function(e){function t(){return null!==e&&e.apply(this,arguments)||this}return n(t,e),t.prototype.getScrollTop=function(){return window.pageYOffset},t.prototype.getScrollLeft=function(){return window.pageXOffset},t.prototype.setScrollTop=function(e){window.scroll(window.pageXOffset,e)},t.prototype.setScrollLeft=function(e){window.scroll(e,window.pageYOffset)},t.prototype.getScrollWidth=function(){return document.documentElement.scrollWidth},t.prototype.getScrollHeight=function(){return document.documentElement.scrollHeight},t.prototype.getClientHeight=function(){return document.documentElement.clientHeight},t.prototype.getClientWidth=function(){return document.documentElement.clientWidth},t}(Yr),$r=function(){function e(e){this.iconOverrideOption&&this.setIconOverride(e[this.iconOverrideOption])}return e.prototype.setIconOverride=function(e){var t,n;if("object"==typeof e&&e){for(n in t=r({},this.iconClasses),e)t[n]=this.applyIconOverridePrefix(e[n]);this.iconClasses=t}else!1===e&&(this.iconClasses={})},e.prototype.applyIconOverridePrefix=function(e){var t=this.iconOverridePrefix;return t&&0!==e.indexOf(t)&&(e=t+e),e},e.prototype.getClass=function(e){return this.classes[e]||""},e.prototype.getIconClass=function(e,t){var n;return(n=t&&this.rtlIconClasses&&this.rtlIconClasses[e]||this.iconClasses[e])?this.baseIconClass+" "+n:""},e.prototype.getCustomButtonIconClass=function(e){var t;return this.iconOverrideCustomButtonOption&&(t=e[this.iconOverrideCustomButtonOption])?this.baseIconClass+" "+this.applyIconOverridePrefix(t):""},e}();if($r.prototype.classes={},$r.prototype.iconClasses={},$r.prototype.baseIconClass="",$r.prototype.iconOverridePrefix="","undefined"==typeof FullCalendarVDom)throw new Error("Please import the top-level fullcalendar lib before attempting to import a plugin.");var Jr=FullCalendarVDom.Component,Qr=FullCalendarVDom.createElement,eo=FullCalendarVDom.render,to=FullCalendarVDom.createRef,no=FullCalendarVDom.Fragment,ro=FullCalendarVDom.createContext,oo=FullCalendarVDom.createPortal,io=FullCalendarVDom.flushToDom,ao=FullCalendarVDom.unmountComponentAtNode,so=function(){function e(e,t,n,o){var i=this;this.execFunc=e,this.emitter=t,this.scrollTime=n,this.scrollTimeReset=o,this.handleScrollRequest=function(e){i.queuedRequest=r({},i.queuedRequest||{},e),i.drain()},t.on("_scrollRequest",this.handleScrollRequest),this.fireInitialScroll()}return e.prototype.detach=function(){this.emitter.off("_scrollRequest",this.handleScrollRequest)},e.prototype.update=function(e){e&&this.scrollTimeReset?this.fireInitialScroll():this.drain()},e.prototype.fireInitialScroll=function(){this.handleScrollRequest({time:this.scrollTime})},e.prototype.drain=function(){this.queuedRequest&&this.execFunc(this.queuedRequest)&&(this.queuedRequest=null)},e}(),lo=ro({});function uo(e,t,n,r,o,i,a,s,l,u,c,d,f){return{dateEnv:o,options:n,pluginHooks:a,emitter:u,dispatch:s,getCurrentData:l,calendarApi:c,viewSpec:e,viewApi:t,dateProfileGenerator:r,theme:i,isRtl:"rtl"===n.direction,addResizeHandler:function(e){u.on("_resize",e)},removeResizeHandler:function(e){u.off("_resize",e)},createScrollResponder:function(e){return new so(e,u,Ct(n.scrollTime),n.scrollTimeReset)},registerInteractiveComponent:d,unregisterInteractiveComponent:f}}var co=function(e){function t(){return null!==e&&e.apply(this,arguments)||this}return n(t,e),t.prototype.shouldComponentUpdate=function(e,t){return this.debug&&console.log(ht(e,this.props),ht(t,this.state)),!vt(this.props,e,this.propEquality)||!vt(this.state,t,this.stateEquality)},t.addPropsEquality=po,t.addStateEquality=ho,t.contextType=lo,t}(Jr);co.prototype.propEquality={},co.prototype.stateEquality={};var fo=function(e){function t(){return null!==e&&e.apply(this,arguments)||this}return n(t,e),t.contextType=lo,t}(co);function po(e){var t=Object.create(this.prototype.propEquality);r(t,e),this.prototype.propEquality=t}function ho(e){var t=Object.create(this.prototype.stateEquality);r(t,e),this.prototype.stateEquality=t}function vo(e,t){"function"==typeof e?e(t):e&&(e.current=t)}var mo=function(e){function t(){var t=null!==e&&e.apply(this,arguments)||this;return t.uid=He(),t}return n(t,e),t.prototype.prepareHits=function(){},t.prototype.queryHit=function(e,t,n,r){return null},t.prototype.isValidSegDownEl=function(e){return!this.props.eventDrag&&!this.props.eventResize&&!be(e,".fc-event-mirror")},t.prototype.isValidDateDownEl=function(e){return!(be(e,".fc-event:not(.fc-bg-event)")||be(e,".fc-more-link")||be(e,"a[data-navlink]")||be(e,".fc-popover"))},t}(fo);function go(e){return{id:He(),deps:e.deps||[],reducers:e.reducers||[],isLoadingFuncs:e.isLoadingFuncs||[],contextInit:[].concat(e.contextInit||[]),eventRefiners:e.eventRefiners||{},eventDefMemberAdders:e.eventDefMemberAdders||[],eventSourceRefiners:e.eventSourceRefiners||{},isDraggableTransformers:e.isDraggableTransformers||[],eventDragMutationMassagers:e.eventDragMutationMassagers||[],eventDefMutationAppliers:e.eventDefMutationAppliers||[],dateSelectionTransformers:e.dateSelectionTransformers||[],datePointTransforms:e.datePointTransforms||[],dateSpanTransforms:e.dateSpanTransforms||[],views:e.views||{},viewPropsTransformers:e.viewPropsTransformers||[],isPropsValid:e.isPropsValid||null,externalDefTransforms:e.externalDefTransforms||[],viewContainerAppends:e.viewContainerAppends||[],eventDropTransformers:e.eventDropTransformers||[],componentInteractions:e.componentInteractions||[],calendarInteractions:e.calendarInteractions||[],themeClasses:e.themeClasses||{},eventSourceDefs:e.eventSourceDefs||[],cmdFormatter:e.cmdFormatter,recurringTypes:e.recurringTypes||[],namedTimeZonedImpl:e.namedTimeZonedImpl,initialView:e.initialView||"",elementDraggingImpl:e.elementDraggingImpl,optionChangeHandlers:e.optionChangeHandlers||{},scrollGridImpl:e.scrollGridImpl||null,contentTypeHandlers:e.contentTypeHandlers||{},listenerRefiners:e.listenerRefiners||{},optionRefiners:e.optionRefiners||{},propSetHandlers:e.propSetHandlers||{}}}function yo(){var e,t=[],n=[];return function(o,i){return e&&Mt(o,t)&&Mt(i,n)||(e=function(e,t){var n={},o={reducers:[],isLoadingFuncs:[],contextInit:[],eventRefiners:{},eventDefMemberAdders:[],eventSourceRefiners:{},isDraggableTransformers:[],eventDragMutationMassagers:[],eventDefMutationAppliers:[],dateSelectionTransformers:[],datePointTransforms:[],dateSpanTransforms:[],views:{},viewPropsTransformers:[],isPropsValid:null,externalDefTransforms:[],viewContainerAppends:[],eventDropTransformers:[],componentInteractions:[],calendarInteractions:[],themeClasses:{},eventSourceDefs:[],cmdFormatter:null,recurringTypes:[],namedTimeZonedImpl:null,initialView:"",elementDraggingImpl:null,optionChangeHandlers:{},scrollGridImpl:null,contentTypeHandlers:{},listenerRefiners:{},optionRefiners:{},propSetHandlers:{}};function i(e){for(var t=0,a=e;t<a.length;t++){var s=a[t];n[s.id]||(n[s.id]=!0,i(s.deps),u=s,o={reducers:(l=o).reducers.concat(u.reducers),isLoadingFuncs:l.isLoadingFuncs.concat(u.isLoadingFuncs),contextInit:l.contextInit.concat(u.contextInit),eventRefiners:r(r({},l.eventRefiners),u.eventRefiners),eventDefMemberAdders:l.eventDefMemberAdders.concat(u.eventDefMemberAdders),eventSourceRefiners:r(r({},l.eventSourceRefiners),u.eventSourceRefiners),isDraggableTransformers:l.isDraggableTransformers.concat(u.isDraggableTransformers),eventDragMutationMassagers:l.eventDragMutationMassagers.concat(u.eventDragMutationMassagers),eventDefMutationAppliers:l.eventDefMutationAppliers.concat(u.eventDefMutationAppliers),dateSelectionTransformers:l.dateSelectionTransformers.concat(u.dateSelectionTransformers),datePointTransforms:l.datePointTransforms.concat(u.datePointTransforms),dateSpanTransforms:l.dateSpanTransforms.concat(u.dateSpanTransforms),views:r(r({},l.views),u.views),viewPropsTransformers:l.viewPropsTransformers.concat(u.viewPropsTransformers),isPropsValid:u.isPropsValid||l.isPropsValid,externalDefTransforms:l.externalDefTransforms.concat(u.externalDefTransforms),viewContainerAppends:l.viewContainerAppends.concat(u.viewContainerAppends),eventDropTransformers:l.eventDropTransformers.concat(u.eventDropTransformers),calendarInteractions:l.calendarInteractions.concat(u.calendarInteractions),componentInteractions:l.componentInteractions.concat(u.componentInteractions),themeClasses:r(r({},l.themeClasses),u.themeClasses),eventSourceDefs:l.eventSourceDefs.concat(u.eventSourceDefs),cmdFormatter:u.cmdFormatter||l.cmdFormatter,recurringTypes:l.recurringTypes.concat(u.recurringTypes),namedTimeZonedImpl:u.namedTimeZonedImpl||l.namedTimeZonedImpl,initialView:l.initialView||u.initialView,elementDraggingImpl:l.elementDraggingImpl||u.elementDraggingImpl,optionChangeHandlers:r(r({},l.optionChangeHandlers),u.optionChangeHandlers),scrollGridImpl:u.scrollGridImpl||l.scrollGridImpl,contentTypeHandlers:r(r({},l.contentTypeHandlers),u.contentTypeHandlers),listenerRefiners:r(r({},l.listenerRefiners),u.listenerRefiners),optionRefiners:r(r({},l.optionRefiners),u.optionRefiners),propSetHandlers:r(r({},l.propSetHandlers),u.propSetHandlers)})}var l,u}return e&&i(e),i(t),o}(o,i)),t=o,n=i,e}}var _o=function(e){function t(){return null!==e&&e.apply(this,arguments)||this}return n(t,e),t}($r);function bo(e,t,n,o){if(t[e])return t[e];var i=function(e,t,n,o){var i=n[e],a=o[e],s=function(e){return i&&null!==i[e]?i[e]:a&&null!==a[e]?a[e]:null},l=s("component"),u=s("superType"),c=null;if(u){if(u===e)throw new Error("Can't have a custom view type that references itself");c=bo(u,t,n,o)}!l&&c&&(l=c.component);if(!l)return null;return{type:e,component:l,defaults:r(r({},c?c.defaults:{}),i?i.rawOptions:{}),overrides:r(r({},c?c.overrides:{}),a?a.rawOptions:{})}}(e,t,n,o);return i&&(t[e]=i),i}_o.prototype.classes={root:"fc-theme-standard",tableCellShaded:"fc-cell-shaded",buttonGroup:"fc-button-group",button:"fc-button fc-button-primary",buttonActive:"fc-button-active"},_o.prototype.baseIconClass="fc-icon",_o.prototype.iconClasses={close:"fc-icon-x",prev:"fc-icon-chevron-left",next:"fc-icon-chevron-right",prevYear:"fc-icon-chevrons-left",nextYear:"fc-icon-chevrons-right"},_o.prototype.rtlIconClasses={prev:"fc-icon-chevron-right",next:"fc-icon-chevron-left",prevYear:"fc-icon-chevrons-right",nextYear:"fc-icon-chevrons-left"},_o.prototype.iconOverrideOption="buttonIcons",_o.prototype.iconOverrideCustomButtonOption="icon",_o.prototype.iconOverridePrefix="fc-icon-";var Eo=function(e){function t(){var t=null!==e&&e.apply(this,arguments)||this;return t.rootElRef=to(),t.handleRootEl=function(e){vo(t.rootElRef,e),t.props.elRef&&vo(t.props.elRef,e)},t}return n(t,e),t.prototype.render=function(){var e=this,t=this.props,n=t.hookProps;return Qr(wo,{hookProps:n,didMount:t.didMount,willUnmount:t.willUnmount,elRef:this.handleRootEl},(function(r){return Qr(Do,{hookProps:n,content:t.content,defaultContent:t.defaultContent,backupElRef:e.rootElRef},(function(e,o){return t.children(r,Ro(t.classNames,n),e,o)}))}))},t}(fo),Co=ro(0);function Do(e){return Qr(Co.Consumer,null,(function(t){return Qr(So,r({renderId:t},e))}))}var So=function(e){function t(){var t=null!==e&&e.apply(this,arguments)||this;return t.innerElRef=to(),t}return n(t,e),t.prototype.render=function(){return this.props.children(this.innerElRef,this.renderInnerContent())},t.prototype.componentDidMount=function(){this.updateCustomContent()},t.prototype.componentDidUpdate=function(){this.updateCustomContent()},t.prototype.componentWillUnmount=function(){this.customContentInfo&&this.customContentInfo.destroy&&this.customContentInfo.destroy()},t.prototype.renderInnerContent=function(){var e=this.customContentInfo,t=this.getInnerContent(),n=this.getContentMeta(t);return e&&e.contentKey===n.contentKey?e&&(e.contentVal=t[n.contentKey]):(e&&(e.destroy&&e.destroy(),e=this.customContentInfo=null),n.contentKey&&(e=this.customContentInfo=r({contentKey:n.contentKey,contentVal:t[n.contentKey]},n.buildLifecycleFuncs()))),e?[]:t},t.prototype.getInnerContent=function(){var e=this.props,t=ko(e.content,e.hookProps);return void 0===t&&(t=ko(e.defaultContent,e.hookProps)),null==t?null:t},t.prototype.getContentMeta=function(e){var t=this.context.pluginHooks.contentTypeHandlers,n="",r=null;if(e)for(var o in t)if(void 0!==e[o]){n=o,r=t[o];break}return{contentKey:n,buildLifecycleFuncs:r}},t.prototype.updateCustomContent=function(){this.customContentInfo&&this.customContentInfo.render(this.innerElRef.current||this.props.backupElRef.current,this.customContentInfo.contentVal)},t}(fo),wo=function(e){function t(){var t=null!==e&&e.apply(this,arguments)||this;return t.handleRootEl=function(e){t.rootEl=e,t.props.elRef&&vo(t.props.elRef,e)},t}return n(t,e),t.prototype.render=function(){return this.props.children(this.handleRootEl)},t.prototype.componentDidMount=function(){var e=this.props.didMount;e&&e(r(r({},this.props.hookProps),{el:this.rootEl}))},t.prototype.componentWillUnmount=function(){var e=this.props.willUnmount;e&&e(r(r({},this.props.hookProps),{el:this.rootEl}))},t}(fo);function To(){var e,t,n=[];return function(r,o){return t&&pt(t,o)&&r===e||(e=r,t=o,n=Ro(r,o)),n}}function Ro(e,t){return"function"==typeof e&&(e=e(t)),cn(e)}function ko(e,t){return"function"==typeof e?e(t,Qr):e}var Io=function(e){function t(){var t=null!==e&&e.apply(this,arguments)||this;return t.normalizeClassNames=To(),t}return n(t,e),t.prototype.render=function(){var e=this.props,t=this.context,n=t.options,r={view:t.viewApi},o=this.normalizeClassNames(n.viewClassNames,r);return Qr(wo,{hookProps:r,didMount:n.viewDidMount,willUnmount:n.viewWillUnmount,elRef:e.elRef},(function(t){return e.children(t,["fc-"+e.viewSpec.type+"-view","fc-view"].concat(o))}))},t}(fo);function Mo(e){return ct(e,xo)}function xo(e){var t,n="function"==typeof e?{component:e}:e,o=n.component;return n.content&&(t=n,o=function(e){return Qr(lo.Consumer,null,(function(n){return Qr(Io,{viewSpec:n.viewSpec},(function(o,i){var a=r(r({},e),{nextDayThreshold:n.options.nextDayThreshold});return Qr(Eo,{hookProps:a,classNames:t.classNames,content:t.content,didMount:t.didMount,willUnmount:t.willUnmount,elRef:o},(function(e,t,n,r){return Qr("div",{className:i.concat(t).join(" "),ref:e},r)}))}))}))}),{superType:n.type,component:o,rawOptions:n}}function Oo(e,t,n,o){var i=Mo(e),a=Mo(t.views);return ct(function(e,t){var n,r={};for(n in e)bo(n,r,e,t);for(n in t)bo(n,r,e,t);return r}(i,a),(function(e){return function(e,t,n,o,i){var a=e.overrides.duration||e.defaults.duration||o.duration||n.duration,s=null,l="",u="",c={};if(a&&(s=function(e){var t=JSON.stringify(e),n=Po[t];void 0===n&&(n=Ct(e),Po[t]=n);return n}(a))){var d=Tt(s);l=d.unit,1===d.value&&(u=l,c=t[l]?t[l].rawOptions:{})}var f=function(t){var n=t.buttonText||{},r=e.defaults.buttonTextKey;return null!=r&&null!=n[r]?n[r]:null!=n[e.type]?n[e.type]:null!=n[u]?n[u]:null},p=function(t){var n=t.buttonHints||{},r=e.defaults.buttonTextKey;return null!=r&&null!=n[r]?n[r]:null!=n[e.type]?n[e.type]:null!=n[u]?n[u]:null};return{type:e.type,component:e.component,duration:s,durationUnit:l,singleUnit:u,optionDefaults:e.defaults,optionOverrides:r(r({},c),e.overrides),buttonTextOverride:f(o)||f(n)||e.overrides.buttonText,buttonTextDefault:f(i)||e.defaults.buttonText||f(Yt)||e.type,buttonTitleOverride:p(o)||p(n)||e.overrides.buttonHint,buttonTitleDefault:p(i)||e.defaults.buttonHint||p(Yt)}}(e,a,t,n,o)}))}var Po={};var No=function(){function e(e){this.props=e,this.nowDate=ar(e.nowInput,e.dateEnv),this.initHiddenDays()}return e.prototype.buildPrev=function(e,t,n){var r=this.props.dateEnv,o=r.subtract(r.startOf(t,e.currentRangeUnit),e.dateIncrement);return this.build(o,-1,n)},e.prototype.buildNext=function(e,t,n){var r=this.props.dateEnv,o=r.add(r.startOf(t,e.currentRangeUnit),e.dateIncrement);return this.build(o,1,n)},e.prototype.build=function(e,t,n){void 0===n&&(n=!0);var r,o,i,a,s,l,u,c,d=this.props;return r=this.buildValidRange(),r=this.trimHiddenDays(r),n&&(u=e,e=null!=(c=r).start&&u<c.start?c.start:null!=c.end&&u>=c.end?new Date(c.end.valueOf()-1):u),o=this.buildCurrentRangeInfo(e,t),i=/^(year|month|week|day)$/.test(o.unit),a=this.buildRenderRange(this.trimHiddenDays(o.range),o.unit,i),s=a=this.trimHiddenDays(a),d.showNonCurrentDates||(s=kn(s,o.range)),s=kn(s=this.adjustActiveRange(s),r),l=Mn(o.range,r),{validRange:r,currentRange:o.range,currentRangeUnit:o.unit,isRangeAllDay:i,activeRange:s,renderRange:a,slotMinTime:d.slotMinTime,slotMaxTime:d.slotMaxTime,isValid:l,dateIncrement:this.buildDateIncrement(o.duration)}},e.prototype.buildValidRange=function(){var e=this.props.validRangeInput,t="function"==typeof e?e.call(this.props.calendarApi,this.nowDate):e;return this.refineRange(t)||{start:null,end:null}},e.prototype.buildCurrentRangeInfo=function(e,t){var n,r=this.props,o=null,i=null,a=null;return r.duration?(o=r.duration,i=r.durationUnit,a=this.buildRangeFromDuration(e,t,o,i)):(n=this.props.dayCount)?(i="day",a=this.buildRangeFromDayCount(e,t,n)):(a=this.buildCustomVisibleRange(e))?i=r.dateEnv.greatestWholeUnit(a.start,a.end).unit:(i=Tt(o=this.getFallbackDuration()).unit,a=this.buildRangeFromDuration(e,t,o,i)),{duration:o,unit:i,range:a}},e.prototype.getFallbackDuration=function(){return Ct({day:1})},e.prototype.adjustActiveRange=function(e){var t=this.props,n=t.dateEnv,r=t.usesMinMaxTime,o=t.slotMinTime,i=t.slotMaxTime,a=e.start,s=e.end;return r&&(St(o)<0&&(a=$e(a),a=n.add(a,o)),St(i)>1&&(s=qe(s=$e(s),-1),s=n.add(s,i))),{start:a,end:s}},e.prototype.buildRangeFromDuration=function(e,t,n,r){var o,i,a,s=this.props,l=s.dateEnv,u=s.dateAlignment;if(!u){var c=this.props.dateIncrement;u=c&&wt(c)<wt(n)?Tt(c).unit:r}function d(){o=l.startOf(e,u),i=l.add(o,n),a={start:o,end:i}}return St(n)<=1&&this.isHiddenDay(o)&&(o=$e(o=this.skipHiddenDays(o,t))),d(),this.trimHiddenDays(a)||(e=this.skipHiddenDays(e,t),d()),a},e.prototype.buildRangeFromDayCount=function(e,t,n){var r,o=this.props,i=o.dateEnv,a=o.dateAlignment,s=0,l=e;a&&(l=i.startOf(l,a)),l=$e(l),r=l=this.skipHiddenDays(l,t);do{r=qe(r,1),this.isHiddenDay(r)||(s+=1)}while(s<n);return{start:l,end:r}},e.prototype.buildCustomVisibleRange=function(e){var t=this.props,n=t.visibleRangeInput,r="function"==typeof n?n.call(t.calendarApi,t.dateEnv.toDate(e)):n,o=this.refineRange(r);return!o||null!=o.start&&null!=o.end?o:null},e.prototype.buildRenderRange=function(e,t,n){return e},e.prototype.buildDateIncrement=function(e){var t,n=this.props.dateIncrement;return n||((t=this.props.dateAlignment)?Ct(1,t):e||Ct({days:1}))},e.prototype.refineRange=function(e){if(e){var t=(n=e,r=this.props.dateEnv,o=null,i=null,n.start&&(o=r.createMarker(n.start)),n.end&&(i=r.createMarker(n.end)),o||i?o&&i&&i<o?null:{start:o,end:i}:null);return t&&(t=Sn(t)),t}var n,r,o,i;return null},e.prototype.initHiddenDays=function(){var e,t=this.props.hiddenDays||[],n=[],r=0;for(!1===this.props.weekends&&t.push(0,6),e=0;e<7;e+=1)(n[e]=-1!==t.indexOf(e))||(r+=1);if(!r)throw new Error("invalid hiddenDays");this.isHiddenDayHash=n},e.prototype.trimHiddenDays=function(e){var t=e.start,n=e.end;return t&&(t=this.skipHiddenDays(t)),n&&(n=this.skipHiddenDays(n,-1,!0)),null==t||null==n||t<n?{start:t,end:n}:null},e.prototype.isHiddenDay=function(e){return e instanceof Date&&(e=e.getUTCDay()),this.isHiddenDayHash[e]},e.prototype.skipHiddenDays=function(e,t,n){for(void 0===t&&(t=1),void 0===n&&(n=!1);this.isHiddenDayHash[(e.getUTCDay()+(n?t:0)+7)%7];)e=qe(e,t);return e},e}();function Ho(e,t,n){var r=t?t.activeRange:null;return Bo({},function(e,t){var n=ir(t),r=[].concat(e.eventSources||[]),o=[];e.initialEvents&&r.unshift(e.initialEvents);e.events&&r.unshift(e.events);for(var i=0,a=r;i<a.length;i++){var s=or(a[i],t,n);s&&o.push(s)}return o}(e,n),r,n)}function Ao(e,t,n,o){var i,a,s=n?n.activeRange:null;switch(t.type){case"ADD_EVENT_SOURCES":return Bo(e,t.sources,s,o);case"REMOVE_EVENT_SOURCE":return i=e,a=t.sourceId,ut(i,(function(e){return e.sourceId!==a}));case"PREV":case"NEXT":case"CHANGE_DATE":case"CHANGE_VIEW_TYPE":return n?Vo(e,s,o):e;case"FETCH_EVENT_SOURCES":return Lo(e,t.sourceIds?dt(t.sourceIds):Fo(e,o),s,t.isRefetch||!1,o);case"RECEIVE_EVENTS":case"RECEIVE_EVENT_ERROR":return function(e,t,n,o){var i,a=e[t];if(a&&n===a.latestFetchId)return r(r({},e),((i={})[t]=r(r({},a),{isFetching:!1,fetchRange:o}),i));return e}(e,t.sourceId,t.fetchId,t.fetchRange);case"REMOVE_ALL_EVENT_SOURCES":return{};default:return e}}function Uo(e){for(var t in e)if(e[t].isFetching)return!0;return!1}function Bo(e,t,n,o){for(var i={},a=0,s=t;a<s.length;a++){var l=s[a];i[l.sourceId]=l}return n&&(i=Vo(i,n,o)),r(r({},e),i)}function Vo(e,t,n){return Lo(e,ut(e,(function(e){return function(e,t,n){if(!zo(e,n))return!e.latestFetchId;return!n.options.lazyFetching||!e.fetchRange||e.isFetching||t.start<e.fetchRange.start||t.end>e.fetchRange.end}(e,t,n)})),t,!1,n)}function Lo(e,t,n,r,o){var i={};for(var a in e){var s=e[a];t[a]?i[a]=Wo(s,n,r,o):i[a]=s}return i}function Wo(e,t,n,o){var i=o.options,a=o.calendarApi,s=o.pluginHooks.eventSourceDefs[e.sourceDefId],l=He();return s.fetch({eventSource:e,range:t,isRefetch:n,context:o},(function(n){var r=n.rawEvents;i.eventSourceSuccess&&(r=i.eventSourceSuccess.call(a,r,n.xhr)||r),e.success&&(r=e.success.call(a,r,n.xhr)||r),o.dispatch({type:"RECEIVE_EVENTS",sourceId:e.sourceId,fetchId:l,fetchRange:t,rawEvents:r})}),(function(n){console.warn(n.message,n),i.eventSourceFailure&&i.eventSourceFailure.call(a,n),e.failure&&e.failure(n),o.dispatch({type:"RECEIVE_EVENT_ERROR",sourceId:e.sourceId,fetchId:l,fetchRange:t,error:n})})),r(r({},e),{isFetching:!0,latestFetchId:l})}function Fo(e,t){return ut(e,(function(e){return zo(e,t)}))}function zo(e,t){return!t.pluginHooks.eventSourceDefs[e.sourceDefId].ignoreRange}function jo(e,t,n,r,o){switch(t.type){case"RECEIVE_EVENTS":return function(e,t,n,r,o,i){if(t&&n===t.latestFetchId){var a=rn(function(e,t,n){var r=n.options.eventDataTransform,o=t?t.eventDataTransform:null;o&&(e=qo(e,o));r&&(e=qo(e,r));return e}(o,t,i),t,i);return r&&(a=yt(a,r,i)),ln(Zo(e,t.sourceId),a)}return e}(e,n[t.sourceId],t.fetchId,t.fetchRange,t.rawEvents,o);case"ADD_EVENTS":return function(e,t,n,r){n&&(t=yt(t,n,r));return ln(e,t)}(e,t.eventStore,r?r.activeRange:null,o);case"RESET_EVENTS":return t.eventStore;case"MERGE_EVENTS":return ln(e,t.eventStore);case"PREV":case"NEXT":case"CHANGE_DATE":case"CHANGE_VIEW_TYPE":return r?yt(e,r.activeRange,o):e;case"REMOVE_EVENTS":return function(e,t){var n=e.defs,r=e.instances,o={},i={};for(var a in n)t.defs[a]||(o[a]=n[a]);for(var s in r)!t.instances[s]&&o[r[s].defId]&&(i[s]=r[s]);return{defs:o,instances:i}}(e,t.eventStore);case"REMOVE_EVENT_SOURCE":return Zo(e,t.sourceId);case"REMOVE_ALL_EVENT_SOURCES":return un(e,(function(e){return!e.sourceId}));case"REMOVE_ALL_EVENTS":return{defs:{},instances:{}};default:return e}}function qo(e,t){var n;if(t){n=[];for(var r=0,o=e;r<o.length;r++){var i=o[r],a=t(i);a?n.push(a):null==a&&n.push(i)}}else n=e;return n}function Zo(e,t){return un(e,(function(e){return e.sourceId!==t}))}function Go(e,t){switch(t.type){case"UNSELECT_DATES":return null;case"SELECT_DATES":return t.selection;default:return e}}function Yo(e,t){switch(t.type){case"UNSELECT_EVENT":return"";case"SELECT_EVENT":return t.eventInstanceId;default:return e}}function Ko(e,t){var n;switch(t.type){case"UNSET_EVENT_DRAG":return null;case"SET_EVENT_DRAG":return{affectedEvents:(n=t.state).affectedEvents,mutatedEvents:n.mutatedEvents,isEvent:n.isEvent};default:return e}}function Xo(e,t){var n;switch(t.type){case"UNSET_EVENT_RESIZE":return null;case"SET_EVENT_RESIZE":return{affectedEvents:(n=t.state).affectedEvents,mutatedEvents:n.mutatedEvents,isEvent:n.isEvent};default:return e}}function $o(e,t,n,r,o){return{header:e.headerToolbar?Jo(e.headerToolbar,e,t,n,r,o):null,footer:e.footerToolbar?Jo(e.footerToolbar,e,t,n,r,o):null}}function Jo(e,t,n,r,o,i){var a={},s=[],l=!1;for(var u in e){var c=Qo(e[u],t,n,r,o,i);a[u]=c.widgets,s.push.apply(s,c.viewsWithButtons),l=l||c.hasTitle}return{sectionWidgets:a,viewsWithButtons:s,hasTitle:l}}function Qo(e,t,n,r,o,i){var a="rtl"===t.direction,s=t.customButtons||{},l=n.buttonText||{},u=t.buttonText||{},c=n.buttonHints||{},d=t.buttonHints||{},f=e?e.split(" "):[],p=[],h=!1;return{widgets:f.map((function(e){return e.split(",").map((function(e){if("title"===e)return h=!0,{buttonName:e};var n,f,v,m,g,y;if(n=s[e])v=function(e){n.click&&n.click.call(e.target,e,e.target)},(m=r.getCustomButtonIconClass(n))||(m=r.getIconClass(e,a))||(g=n.text),y=n.hint||n.text;else if(f=o[e]){p.push(e),v=function(){i.changeView(e)},(g=f.buttonTextOverride)||(m=r.getIconClass(e,a))||(g=f.buttonTextDefault);var _=f.buttonTextOverride||f.buttonTextDefault;y=We(f.buttonTitleOverride||f.buttonTitleDefault||t.viewHint,[_,e],_)}else if(i[e])if(v=function(){i[e]()},(g=l[e])||(m=r.getIconClass(e,a))||(g=u[e]),"prevYear"===e||"nextYear"===e){var b="prevYear"===e?"prev":"next";y=We(c[b]||d[b],[u.year||"year","year"],u[e])}else y=function(t){return We(c[e]||d[e],[u[t]||t,t],u[e])};return{buttonName:e,buttonClick:v,buttonIcon:m,buttonText:g,buttonHint:y}}))})),viewsWithButtons:p,hasTitle:h}}function ei(e,t,n,r,o){var i=null;"GET"===(e=e.toUpperCase())?t=function(e,t){return e+(-1===e.indexOf("?")?"?":"&")+ti(t)}(t,n):i=ti(n);var a=new XMLHttpRequest;a.open(e,t,!0),"GET"!==e&&a.setRequestHeader("Content-Type","application/x-www-form-urlencoded"),a.onload=function(){if(a.status>=200&&a.status<400){var e=!1,t=void 0;try{t=JSON.parse(a.responseText),e=!0}catch(e){}e?r(t,a):o("Failure parsing JSON",a)}else o("Request failed",a)},a.onerror=function(){o("Request failed",a)},a.send(i)}function ti(e){var t=[];for(var n in e)t.push(encodeURIComponent(n)+"="+encodeURIComponent(e[n]));return t.join("&")}function ni(e,t){for(var n=ft(t.getCurrentData().eventSources),r=[],o=0,i=e;o<i.length;o++){for(var a=i[o],s=!1,l=0;l<n.length;l+=1)if(n[l]._raw===a){n.splice(l,1),s=!0;break}s||r.push(a)}for(var u=0,c=n;u<c.length;u++){var d=c[u];t.dispatch({type:"REMOVE_EVENT_SOURCE",sourceId:d.sourceId})}for(var f=0,p=r;f<p.length;f++){var h=p[f];t.calendarApi.addEventSource(h)}}var ri=[go({eventSourceDefs:[{ignoreRange:!0,parseMeta:function(e){return Array.isArray(e.events)?e.events:null},fetch:function(e,t){t({rawEvents:e.eventSource.meta})}}]}),go({eventSourceDefs:[{parseMeta:function(e){return"function"==typeof e.events?e.events:null},fetch:function(e,t,n){var r=e.context.dateEnv;qr(e.eventSource.meta.bind(null,Gn(e.range,r)),(function(e){t({rawEvents:e})}),n)}}]}),go({eventSourceRefiners:{method:String,extraParams:nn,startParam:String,endParam:String,timeZoneParam:String},eventSourceDefs:[{parseMeta:function(e){return!e.url||"json"!==e.format&&e.format?null:{url:e.url,format:"json",method:(e.method||"GET").toUpperCase(),extraParams:e.extraParams,startParam:e.startParam,endParam:e.endParam,timeZoneParam:e.timeZoneParam}},fetch:function(e,t,n){var o=e.eventSource.meta,i=function(e,t,n){var o,i,a,s,l=n.dateEnv,u=n.options,c={};null==(o=e.startParam)&&(o=u.startParam);null==(i=e.endParam)&&(i=u.endParam);null==(a=e.timeZoneParam)&&(a=u.timeZoneParam);s="function"==typeof e.extraParams?e.extraParams():e.extraParams||{};r(c,s),c[o]=l.formatIso(t.start),c[i]=l.formatIso(t.end),"local"!==l.timeZone&&(c[a]=l.timeZone);return c}(o,e.range,e.context);ei(o.method,o.url,i,(function(e,n){t({rawEvents:e,xhr:n})}),(function(e,t){n({message:e,xhr:t})}))}}]}),go({recurringTypes:[{parse:function(e,t){if(e.daysOfWeek||e.startTime||e.endTime||e.startRecur||e.endRecur){var n={daysOfWeek:e.daysOfWeek||null,startTime:e.startTime||null,endTime:e.endTime||null,startRecur:e.startRecur?t.createMarker(e.startRecur):null,endRecur:e.endRecur?t.createMarker(e.endRecur):null},r=void 0;return e.duration&&(r=e.duration),!r&&e.startTime&&e.endTime&&(o=e.endTime,i=e.startTime,r={years:o.years-i.years,months:o.months-i.months,days:o.days-i.days,milliseconds:o.milliseconds-i.milliseconds}),{allDayGuess:Boolean(!e.startTime&&!e.endTime),duration:r,typeData:n}}var o,i;return null},expand:function(e,t,n){var r=kn(t,{start:e.startRecur,end:e.endRecur});return r?function(e,t,n,r){var o=e?dt(e):null,i=$e(n.start),a=n.end,s=[];for(;i<a;){var l=void 0;o&&!o[i.getUTCDay()]||(l=t?r.add(i,t):i,s.push(l)),i=qe(i,1)}return s}(e.daysOfWeek,e.startTime,r,n):[]}}],eventRefiners:{daysOfWeek:nn,startTime:Ct,endTime:Ct,duration:Ct,startRecur:nn,endRecur:nn}}),go({optionChangeHandlers:{events:function(e,t){ni([e],t)},eventSources:ni}}),go({isLoadingFuncs:[function(e){return Uo(e.eventSources)}],contentTypeHandlers:{html:function(){var e=null,t="";return{render:function(n,r){n===e&&r===t||(n.innerHTML=r),e=n,t=r},destroy:function(){e.innerHTML="",e=null,t=""}}},domNodes:function(){var e=null,t=[];function n(){t.forEach(_e),t=[],e=null}return{render:function(r,o){var i=Array.prototype.slice.call(o);if(r!==e||!Mt(t,i)){for(var a=0,s=i;a<s.length;a++){var l=s[a];r.appendChild(l)}n()}e=r,t=i},destroy:n}}},propSetHandlers:{dateProfile:function(e,t){t.emitter.trigger("datesSet",r(r({},Gn(e.activeRange,t.dateEnv)),{view:t.viewApi}))},eventStore:function(e,t){var n=t.emitter;n.hasHandlers("eventsSet")&&n.trigger("eventsSet",cr(e,t))}}})];var oi=function(){function e(e){this.drainedOption=e,this.isRunning=!1,this.isDirty=!1,this.pauseDepths={},this.timeoutId=0}return e.prototype.request=function(e){this.isDirty=!0,this.isPaused()||(this.clearTimeout(),null==e?this.tryDrain():this.timeoutId=setTimeout(this.tryDrain.bind(this),e))},e.prototype.pause=function(e){void 0===e&&(e="");var t=this.pauseDepths;t[e]=(t[e]||0)+1,this.clearTimeout()},e.prototype.resume=function(e,t){void 0===e&&(e="");var n=this.pauseDepths;if(e in n){if(t)delete n[e];else n[e]-=1,n[e]<=0&&delete n[e];this.tryDrain()}},e.prototype.isPaused=function(){return Object.keys(this.pauseDepths).length},e.prototype.tryDrain=function(){if(!this.isRunning&&!this.isPaused()){for(this.isRunning=!0;this.isDirty;)this.isDirty=!1,this.drained();this.isRunning=!1}},e.prototype.clear=function(){this.clearTimeout(),this.isDirty=!1,this.pauseDepths={}},e.prototype.clearTimeout=function(){this.timeoutId&&(clearTimeout(this.timeoutId),this.timeoutId=0)},e.prototype.drained=function(){this.drainedOption&&this.drainedOption()},e}(),ii=function(){function e(e,t){this.runTaskOption=e,this.drainedOption=t,this.queue=[],this.delayedRunner=new oi(this.drain.bind(this))}return e.prototype.request=function(e,t){this.queue.push(e),this.delayedRunner.request(t)},e.prototype.pause=function(e){this.delayedRunner.pause(e)},e.prototype.resume=function(e,t){this.delayedRunner.resume(e,t)},e.prototype.drain=function(){for(var e=this.queue;e.length;){for(var t=[],n=void 0;n=e.shift();)this.runTask(n),t.push(n);this.drained(t)}},e.prototype.runTask=function(e){this.runTaskOption&&this.runTaskOption(e)},e.prototype.drained=function(e){this.drainedOption&&this.drainedOption(e)},e}();function ai(e,t,n){var r;return r=/^(year|month)$/.test(e.currentRangeUnit)?e.currentRange:e.activeRange,n.formatRange(r.start,r.end,Zt(t.titleFormat||function(e){var t=e.currentRangeUnit;if("year"===t)return{year:"numeric"};if("month"===t)return{year:"numeric",month:"long"};var n=Xe(e.currentRange.start,e.currentRange.end);if(null!==n&&n>1)return{year:"numeric",month:"short",day:"numeric"};return{year:"numeric",month:"long",day:"numeric"}}(e)),{isEndExclusive:e.isRangeAllDay,defaultSeparator:t.titleRangeSeparator})}var si=function(){function e(e){var t=this;this.computeOptionsData=xt(this._computeOptionsData),this.computeCurrentViewData=xt(this._computeCurrentViewData),this.organizeRawLocales=xt(br),this.buildLocale=xt(Er),this.buildPluginHooks=yo(),this.buildDateEnv=xt(li),this.buildTheme=xt(ui),this.parseToolbars=xt($o),this.buildViewSpecs=xt(Oo),this.buildDateProfileGenerator=Ot(ci),this.buildViewApi=xt(di),this.buildViewUiProps=Ot(hi),this.buildEventUiBySource=xt(fi,pt),this.buildEventUiBases=xt(pi),this.parseContextBusinessHours=Ot(mi),this.buildTitle=xt(ai),this.emitter=new Zr,this.actionRunner=new ii(this._handleAction.bind(this),this.updateData.bind(this)),this.currentCalendarOptionsInput={},this.currentCalendarOptionsRefined={},this.currentViewOptionsInput={},this.currentViewOptionsRefined={},this.currentCalendarOptionsRefiners={},this.getCurrentData=function(){return t.data},this.dispatch=function(e){t.actionRunner.request(e)},this.props=e,this.actionRunner.pause();var n={},o=this.computeOptionsData(e.optionOverrides,n,e.calendarApi),i=o.calendarOptions.initialView||o.pluginHooks.initialView,a=this.computeCurrentViewData(i,o,e.optionOverrides,n);e.calendarApi.currentDataManager=this,this.emitter.setThisContext(e.calendarApi),this.emitter.setOptions(a.options);var s,l,u,c=(s=o.calendarOptions,l=o.dateEnv,null!=(u=s.initialDate)?l.createMarker(u):ar(s.now,l)),d=a.dateProfileGenerator.build(c);On(d.activeRange,c)||(c=d.currentRange.start);for(var f={dateEnv:o.dateEnv,options:o.calendarOptions,pluginHooks:o.pluginHooks,calendarApi:e.calendarApi,dispatch:this.dispatch,emitter:this.emitter,getCurrentData:this.getCurrentData},p=0,h=o.pluginHooks.contextInit;p<h.length;p++){(0,h[p])(f)}for(var v=Ho(o.calendarOptions,d,f),m={dynamicOptionOverrides:n,currentViewType:i,currentDate:c,dateProfile:d,businessHours:this.parseContextBusinessHours(f),eventSources:v,eventUiBases:{},eventStore:{defs:{},instances:{}},renderableEventStore:{defs:{},instances:{}},dateSelection:null,eventSelection:"",eventDrag:null,eventResize:null,selectionConfig:this.buildViewUiProps(f).selectionConfig},g=r(r({},f),m),y=0,_=o.pluginHooks.reducers;y<_.length;y++){var b=_[y];r(m,b(null,null,g))}vi(m,f)&&this.emitter.trigger("loading",!0),this.state=m,this.updateData(),this.actionRunner.resume()}return e.prototype.resetOptions=function(e,t){var n=this.props;n.optionOverrides=t?r(r({},n.optionOverrides),e):e,this.actionRunner.request({type:"NOTHING"})},e.prototype._handleAction=function(e){var t=this,n=t.props,o=t.state,i=t.emitter,a=function(e,t){var n;switch(t.type){case"SET_OPTION":return r(r({},e),((n={})[t.optionName]=t.rawOptionValue,n));default:return e}}(o.dynamicOptionOverrides,e),s=this.computeOptionsData(n.optionOverrides,a,n.calendarApi),l=function(e,t){switch(t.type){case"CHANGE_VIEW_TYPE":e=t.viewType}return e}(o.currentViewType,e),u=this.computeCurrentViewData(l,s,n.optionOverrides,a);n.calendarApi.currentDataManager=this,i.setThisContext(n.calendarApi),i.setOptions(u.options);var c={dateEnv:s.dateEnv,options:s.calendarOptions,pluginHooks:s.pluginHooks,calendarApi:n.calendarApi,dispatch:this.dispatch,emitter:i,getCurrentData:this.getCurrentData},d=o.currentDate,f=o.dateProfile;this.data&&this.data.dateProfileGenerator!==u.dateProfileGenerator&&(f=u.dateProfileGenerator.build(d)),f=function(e,t,n,r){var o;switch(t.type){case"CHANGE_VIEW_TYPE":return r.build(t.dateMarker||n);case"CHANGE_DATE":return r.build(t.dateMarker);case"PREV":if((o=r.buildPrev(e,n)).isValid)return o;break;case"NEXT":if((o=r.buildNext(e,n)).isValid)return o}return e}(f,e,d=function(e,t){switch(t.type){case"CHANGE_DATE":return t.dateMarker;default:return e}}(d,e),u.dateProfileGenerator),"PREV"!==e.type&&"NEXT"!==e.type&&On(f.currentRange,d)||(d=f.currentRange.start);for(var p=Ao(o.eventSources,e,f,c),h=jo(o.eventStore,e,p,f,c),v=Uo(p)&&!u.options.progressiveEventRendering&&o.renderableEventStore||h,m=this.buildViewUiProps(c),g=m.eventUiSingleBase,y=m.selectionConfig,_=this.buildEventUiBySource(p),b={dynamicOptionOverrides:a,currentViewType:l,currentDate:d,dateProfile:f,eventSources:p,eventStore:h,renderableEventStore:v,selectionConfig:y,eventUiBases:this.buildEventUiBases(v.defs,g,_),businessHours:this.parseContextBusinessHours(c),dateSelection:Go(o.dateSelection,e),eventSelection:Yo(o.eventSelection,e),eventDrag:Ko(o.eventDrag,e),eventResize:Xo(o.eventResize,e)},E=r(r({},c),b),C=0,D=s.pluginHooks.reducers;C<D.length;C++){var S=D[C];r(b,S(o,e,E))}var w=vi(o,c),T=vi(b,c);!w&&T?i.trigger("loading",!0):w&&!T&&i.trigger("loading",!1),this.state=b,n.onAction&&n.onAction(e)},e.prototype.updateData=function(){var e,t,n,o,i,a,s,l,u,c=this.props,d=this.state,f=this.data,p=this.computeOptionsData(c.optionOverrides,d.dynamicOptionOverrides,c.calendarApi),h=this.computeCurrentViewData(d.currentViewType,p,c.optionOverrides,d.dynamicOptionOverrides),v=this.data=r(r(r({viewTitle:this.buildTitle(d.dateProfile,h.options,p.dateEnv),calendarApi:c.calendarApi,dispatch:this.dispatch,emitter:this.emitter,getCurrentData:this.getCurrentData},p),h),d),m=p.pluginHooks.optionChangeHandlers,g=f&&f.calendarOptions,y=p.calendarOptions;if(g&&g!==y)for(var _ in g.timeZone!==y.timeZone&&(d.eventSources=v.eventSources=(a=v.eventSources,s=d.dateProfile,l=v,u=s?s.activeRange:null,Lo(a,Fo(a,l),u,!0,l)),d.eventStore=v.eventStore=(e=v.eventStore,t=f.dateEnv,n=v.dateEnv,o=e.defs,i=ct(e.instances,(function(e){var i=o[e.defId];return i.allDay||i.recurringDef?e:r(r({},e),{range:{start:n.createMarker(t.toDate(e.range.start,e.forcedStartTzo)),end:n.createMarker(t.toDate(e.range.end,e.forcedEndTzo))},forcedStartTzo:n.canComputeOffset?null:e.forcedStartTzo,forcedEndTzo:n.canComputeOffset?null:e.forcedEndTzo})})),{defs:o,instances:i})),m)g[_]!==y[_]&&m[_](y[_],v);c.onData&&c.onData(v)},e.prototype._computeOptionsData=function(e,t,n){var r=this.processRawCalendarOptions(e,t),o=r.refinedOptions,i=r.pluginHooks,a=r.localeDefaults,s=r.availableLocaleData;gi(r.extra);var l=this.buildDateEnv(o.timeZone,o.locale,o.weekNumberCalculation,o.firstDay,o.weekText,i,s,o.defaultRangeSeparator),u=this.buildViewSpecs(i.views,e,t,a),c=this.buildTheme(o,i);return{calendarOptions:o,pluginHooks:i,dateEnv:l,viewSpecs:u,theme:c,toolbarConfig:this.parseToolbars(o,e,c,u,n),localeDefaults:a,availableRawLocales:s.map}},e.prototype.processRawCalendarOptions=function(e,t){var n=en([Yt,e,t]),o=n.locales,i=n.locale,a=this.organizeRawLocales(o),s=a.map,l=this.buildLocale(i||a.defaultCode,s).options,u=this.buildPluginHooks(e.plugins||[],ri),c=this.currentCalendarOptionsRefiners=r(r(r(r(r({},Gt),Kt),Xt),u.listenerRefiners),u.optionRefiners),d={},f=en([Yt,l,e,t]),p={},h=this.currentCalendarOptionsInput,v=this.currentCalendarOptionsRefined,m=!1;for(var g in f)"plugins"!==g&&(f[g]===h[g]||$t[g]&&g in h&&$t[g](h[g],f[g])?p[g]=v[g]:c[g]?(p[g]=c[g](f[g]),m=!0):d[g]=h[g]);return m&&(this.currentCalendarOptionsInput=f,this.currentCalendarOptionsRefined=p),{rawOptions:this.currentCalendarOptionsInput,refinedOptions:this.currentCalendarOptionsRefined,pluginHooks:u,availableLocaleData:a,localeDefaults:l,extra:d}},e.prototype._computeCurrentViewData=function(e,t,n,r){var o=t.viewSpecs[e];if(!o)throw new Error('viewType "'+e+"\" is not available. Please make sure you've loaded all neccessary plugins");var i=this.processRawViewOptions(o,t.pluginHooks,t.localeDefaults,n,r),a=i.refinedOptions;return gi(i.extra),{viewSpec:o,options:a,dateProfileGenerator:this.buildDateProfileGenerator({dateProfileGeneratorClass:o.optionDefaults.dateProfileGeneratorClass,duration:o.duration,durationUnit:o.durationUnit,usesMinMaxTime:o.optionDefaults.usesMinMaxTime,dateEnv:t.dateEnv,calendarApi:this.props.calendarApi,slotMinTime:a.slotMinTime,slotMaxTime:a.slotMaxTime,showNonCurrentDates:a.showNonCurrentDates,dayCount:a.dayCount,dateAlignment:a.dateAlignment,dateIncrement:a.dateIncrement,hiddenDays:a.hiddenDays,weekends:a.weekends,nowInput:a.now,validRangeInput:a.validRange,visibleRangeInput:a.visibleRange,monthMode:a.monthMode,fixedWeekCount:a.fixedWeekCount}),viewApi:this.buildViewApi(e,this.getCurrentData,t.dateEnv)}},e.prototype.processRawViewOptions=function(e,t,n,o,i){var a=en([Yt,e.optionDefaults,n,o,e.optionOverrides,i]),s=r(r(r(r(r(r({},Gt),Kt),Xt),Qt),t.listenerRefiners),t.optionRefiners),l={},u=this.currentViewOptionsInput,c=this.currentViewOptionsRefined,d=!1,f={};for(var p in a)a[p]===u[p]?l[p]=c[p]:(a[p]===this.currentCalendarOptionsInput[p]?p in this.currentCalendarOptionsRefined&&(l[p]=this.currentCalendarOptionsRefined[p]):s[p]?l[p]=s[p](a[p]):f[p]=a[p],d=!0);return d&&(this.currentViewOptionsInput=a,this.currentViewOptionsRefined=l),{rawOptions:this.currentViewOptionsInput,refinedOptions:this.currentViewOptionsRefined,extra:f}},e}();function li(e,t,n,r,o,i,a,s){var l=Er(t||a.defaultCode,a.map);return new mr({calendarSystem:"gregory",timeZone:e,namedTimeZoneImpl:i.namedTimeZonedImpl,locale:l,weekNumberCalculation:n,firstDay:r,weekText:o,cmdFormatter:i.cmdFormatter,defaultSeparator:s})}function ui(e,t){return new(t.themeClasses[e.themeSystem]||_o)(e)}function ci(e){return new(e.dateProfileGeneratorClass||No)(e)}function di(e,t,n){return new nr(e,t,n)}function fi(e){return ct(e,(function(e){return e.ui}))}function pi(e,t,n){var r={"":t};for(var o in e){var i=e[o];i.sourceId&&n[i.sourceId]&&(r[o]=n[i.sourceId])}return r}function hi(e){var t=e.options;return{eventUiSingleBase:pn({display:t.eventDisplay,editable:t.editable,startEditable:t.eventStartEditable,durationEditable:t.eventDurationEditable,constraint:t.eventConstraint,overlap:"boolean"==typeof t.eventOverlap?t.eventOverlap:void 0,allow:t.eventAllow,backgroundColor:t.eventBackgroundColor,borderColor:t.eventBorderColor,textColor:t.eventTextColor,color:t.eventColor},e),selectionConfig:pn({constraint:t.selectConstraint,overlap:"boolean"==typeof t.selectOverlap?t.selectOverlap:void 0,allow:t.selectAllow},e)}}function vi(e,t){for(var n=0,r=t.pluginHooks.isLoadingFuncs;n<r.length;n++){if((0,r[n])(e))return!0}return!1}function mi(e){return Tr(e.options.businessHours,e)}function gi(e,t){for(var n in e)console.warn("Unknown option '"+n+"'"+(t?" for view '"+t+"'":""))}var yi=function(e){function t(t){var n=e.call(this,t)||this;return n.handleData=function(e){n.dataManager?n.setState(e):n.state=e},n.dataManager=new si({optionOverrides:t.optionOverrides,calendarApi:t.calendarApi,onData:n.handleData}),n}return n(t,e),t.prototype.render=function(){return this.props.children(this.state)},t.prototype.componentDidUpdate=function(e){var t=this.props.optionOverrides;t!==e.optionOverrides&&this.dataManager.resetOptions(t)},t}(Jr);var _i=function(e){this.timeZoneName=e},bi=function(){function e(){this.strictOrder=!1,this.allowReslicing=!1,this.maxCoord=-1,this.maxStackCnt=-1,this.levelCoords=[],this.entriesByLevel=[],this.stackCnts={}}return e.prototype.addSegs=function(e){for(var t=[],n=0,r=e;n<r.length;n++){var o=r[n];this.insertEntry(o,t)}return t},e.prototype.insertEntry=function(e,t){var n=this.findInsertion(e);return this.isInsertionValid(n,e)?(this.insertEntryAt(e,n),1):this.handleInvalidInsertion(n,e,t)},e.prototype.isInsertionValid=function(e,t){return(-1===this.maxCoord||e.levelCoord+t.thickness<=this.maxCoord)&&(-1===this.maxStackCnt||e.stackCnt<this.maxStackCnt)},e.prototype.handleInvalidInsertion=function(e,t,n){return this.allowReslicing&&e.touchingEntry?this.splitEntry(t,e.touchingEntry,n):(n.push(t),0)},e.prototype.splitEntry=function(e,t,n){var r=0,i=[],a=e.span,s=t.span;return a.start<s.start&&(r+=this.insertEntry({index:e.index,thickness:e.thickness,span:{start:a.start,end:s.start}},i)),a.end>s.end&&(r+=this.insertEntry({index:e.index,thickness:e.thickness,span:{start:s.end,end:a.end}},i)),r?(n.push.apply(n,o([{index:e.index,thickness:e.thickness,span:Si(s,a)}],i)),r):(n.push(e),0)},e.prototype.insertEntryAt=function(e,t){var n=this.entriesByLevel,r=this.levelCoords;-1===t.lateral?(wi(r,t.level,t.levelCoord),wi(n,t.level,[e])):wi(n[t.level],t.lateral,e),this.stackCnts[Ci(e)]=t.stackCnt},e.prototype.findInsertion=function(e){for(var t=this,n=t.levelCoords,r=t.entriesByLevel,o=t.strictOrder,i=t.stackCnts,a=n.length,s=0,l=-1,u=-1,c=null,d=0,f=0;f<a;f+=1){var p=n[f];if(!o&&p>=s+e.thickness)break;for(var h=r[f],v=void 0,m=Ti(h,e.span.start,Ei),g=m[0]+m[1];(v=h[g])&&v.span.start<e.span.end;){var y=p+v.thickness;y>s&&(s=y,c=v,l=f,u=g),y===s&&(d=Math.max(d,i[Ci(v)]+1)),g+=1}}var _=0;if(c)for(_=l+1;_<a&&n[_]<s;)_+=1;var b=-1;return _<a&&n[_]===s&&(b=Ti(r[_],e.span.end,Ei)[0]),{touchingLevel:l,touchingLateral:u,touchingEntry:c,stackCnt:d,levelCoord:s,level:_,lateral:b}},e.prototype.toRects=function(){for(var e=this.entriesByLevel,t=this.levelCoords,n=e.length,o=[],i=0;i<n;i+=1)for(var a=e[i],s=t[i],l=0,u=a;l<u.length;l++){var c=u[l];o.push(r(r({},c),{levelCoord:s}))}return o},e}();function Ei(e){return e.span.end}function Ci(e){return e.index+":"+e.span.start}function Di(e,t){return{start:Math.min(e.start,t.start),end:Math.max(e.end,t.end)}}function Si(e,t){var n=Math.max(e.start,t.start),r=Math.min(e.end,t.end);return n<r?{start:n,end:r}:null}function wi(e,t,n){e.splice(t,0,n)}function Ti(e,t,n){var r=0,o=e.length;if(!o||t<n(e[r]))return[0,0];if(t>n(e[o-1]))return[o,0];for(;r<o;){var i=Math.floor(r+(o-r)/2),a=n(e[i]);if(t<a)o=i;else{if(!(t>a))return[i,1];r=i+1}}return[r,0]}var Ri=function(){function e(e){this.component=e.component,this.isHitComboAllowed=e.isHitComboAllowed||null}return e.prototype.destroy=function(){},e}();function ki(e,t){return{component:e,el:t.el,useEventCenter:null==t.useEventCenter||t.useEventCenter,isHitComboAllowed:t.isHitComboAllowed||null}}var Ii={},Mi=function(){function e(e,t){this.emitter=new Zr}return e.prototype.destroy=function(){},e.prototype.setMirrorIsVisible=function(e){},e.prototype.setMirrorNeedsRevert=function(e){},e.prototype.setAutoScrollEnabled=function(e){},e}(),xi={startTime:Ct,duration:Ct,create:Boolean,sourceId:String};var Oi=function(e){function t(){return null!==e&&e.apply(this,arguments)||this}return n(t,e),t.prototype.render=function(){var e=this,t=this.props.widgetGroups.map((function(t){return e.renderWidgetGroup(t)}));return Qr.apply(void 0,o(["div",{className:"fc-toolbar-chunk"}],t))},t.prototype.renderWidgetGroup=function(e){for(var t=this.props,n=this.context.theme,r=[],i=!0,a=0,s=e;a<s.length;a++){var l=s[a],u=l.buttonName,c=l.buttonClick,d=l.buttonText,f=l.buttonIcon,p=l.buttonHint;if("title"===u)i=!1,r.push(Qr("h2",{className:"fc-toolbar-title",id:t.titleId},t.title));else{var h=u===t.activeButton,v=!t.isTodayEnabled&&"today"===u||!t.isPrevEnabled&&"prev"===u||!t.isNextEnabled&&"next"===u,m=["fc-"+u+"-button",n.getClass("button")];h&&m.push(n.getClass("buttonActive")),r.push(Qr("button",{type:"button",title:"function"==typeof p?p(t.navUnit):p,disabled:v,"aria-pressed":h,className:m.join(" "),onClick:c},d||(f?Qr("span",{className:f}):"")))}}if(r.length>1){var g=i&&n.getClass("buttonGroup")||"";return Qr.apply(void 0,o(["div",{className:g}],r))}return r[0]},t}(fo),Pi=function(e){function t(){return null!==e&&e.apply(this,arguments)||this}return n(t,e),t.prototype.render=function(){var e,t,n=this.props,r=n.model,o=n.extraClassName,i=!1,a=r.sectionWidgets,s=a.center;return a.left?(i=!0,e=a.left):e=a.start,a.right?(i=!0,t=a.right):t=a.end,Qr("div",{className:[o||"","fc-toolbar",i?"fc-toolbar-ltr":""].join(" ")},this.renderSection("start",e||[]),this.renderSection("center",s||[]),this.renderSection("end",t||[]))},t.prototype.renderSection=function(e,t){var n=this.props;return Qr(Oi,{key:e,widgetGroups:t,title:n.title,navUnit:n.navUnit,activeButton:n.activeButton,isTodayEnabled:n.isTodayEnabled,isPrevEnabled:n.isPrevEnabled,isNextEnabled:n.isNextEnabled,titleId:n.titleId})},t}(fo),Ni=function(e){function t(){var t=null!==e&&e.apply(this,arguments)||this;return t.state={availableWidth:null},t.handleEl=function(e){t.el=e,vo(t.props.elRef,e),t.updateAvailableWidth()},t.handleResize=function(){t.updateAvailableWidth()},t}return n(t,e),t.prototype.render=function(){var e=this.props,t=this.state,n=e.aspectRatio,r=["fc-view-harness",n||e.liquid||e.height?"fc-view-harness-active":"fc-view-harness-passive"],o="",i="";return n?null!==t.availableWidth?o=t.availableWidth/n:i=1/n*100+"%":o=e.height||"",Qr("div",{"aria-labelledby":e.labeledById,ref:this.handleEl,className:r.join(" "),style:{height:o,paddingBottom:i}},e.children)},t.prototype.componentDidMount=function(){this.context.addResizeHandler(this.handleResize)},t.prototype.componentWillUnmount=function(){this.context.removeResizeHandler(this.handleResize)},t.prototype.updateAvailableWidth=function(){this.el&&this.props.aspectRatio&&this.setState({availableWidth:this.el.offsetWidth})},t}(fo),Hi=function(e){function t(t){var n=e.call(this,t)||this;return n.handleSegClick=function(e,t){var r=n.component,o=r.context,i=Hn(t);if(i&&r.isValidSegDownEl(e.target)){var a=be(e.target,".fc-event-forced-url"),s=a?a.querySelector("a[href]").href:"";o.emitter.trigger("eventClick",{el:t,event:new lr(r.context,i.eventRange.def,i.eventRange.instance),jsEvent:e,view:o.viewApi}),s&&!e.defaultPrevented&&(window.location.href=s)}},n.destroy=Me(t.el,"click",".fc-event",n.handleSegClick),n}return n(t,e),t}(Ri),Ai=function(e){function t(t){var n,r,o,i,a,s=e.call(this,t)||this;return s.handleEventElRemove=function(e){e===s.currentSegEl&&s.handleSegLeave(null,s.currentSegEl)},s.handleSegEnter=function(e,t){Hn(t)&&(s.currentSegEl=t,s.triggerEvent("eventMouseEnter",e,t))},s.handleSegLeave=function(e,t){s.currentSegEl&&(s.currentSegEl=null,s.triggerEvent("eventMouseLeave",e,t))},s.removeHoverListeners=(n=t.el,r=".fc-event",o=s.handleSegEnter,i=s.handleSegLeave,Me(n,"mouseover",r,(function(e,t){if(t!==a){a=t,o(e,t);var n=function(e){a=null,i(e,t),t.removeEventListener("mouseleave",n)};t.addEventListener("mouseleave",n)}}))),s}return n(t,e),t.prototype.destroy=function(){this.removeHoverListeners()},t.prototype.triggerEvent=function(e,t,n){var r=this.component,o=r.context,i=Hn(n);t&&!r.isValidSegDownEl(t.target)||o.emitter.trigger(e,{el:n,event:new lr(o,i.eventRange.def,i.eventRange.instance),jsEvent:t,view:o.viewApi})},t}(Ri),Ui=function(e){function t(){var t=null!==e&&e.apply(this,arguments)||this;return t.buildViewContext=xt(uo),t.buildViewPropTransformers=xt(Vi),t.buildToolbarProps=xt(Bi),t.headerRef=to(),t.footerRef=to(),t.interactionsStore={},t.state={viewLabelId:ke()},t.registerInteractiveComponent=function(e,n){var r=ki(e,n),o=[Hi,Ai].concat(t.props.pluginHooks.componentInteractions).map((function(e){return new e(r)}));t.interactionsStore[e.uid]=o,Ii[e.uid]=r},t.unregisterInteractiveComponent=function(e){for(var n=0,r=t.interactionsStore[e.uid];n<r.length;n++){r[n].destroy()}delete t.interactionsStore[e.uid],delete Ii[e.uid]},t.resizeRunner=new oi((function(){t.props.emitter.trigger("_resize",!0),t.props.emitter.trigger("windowResize",{view:t.props.viewApi})})),t.handleWindowResize=function(e){var n=t.props.options;n.handleWindowResize&&e.target===window&&t.resizeRunner.request(n.windowResizeDelay)},t}return n(t,e),t.prototype.render=function(){var e,t=this.props,n=t.toolbarConfig,o=t.options,i=this.buildToolbarProps(t.viewSpec,t.dateProfile,t.dateProfileGenerator,t.currentDate,ar(t.options.now,t.dateEnv),t.viewTitle),a=!1,s="";t.isHeightAuto||t.forPrint?s="":null!=o.height?a=!0:null!=o.contentHeight?s=o.contentHeight:e=Math.max(o.aspectRatio,.5);var l=this.buildViewContext(t.viewSpec,t.viewApi,t.options,t.dateProfileGenerator,t.dateEnv,t.theme,t.pluginHooks,t.dispatch,t.getCurrentData,t.emitter,t.calendarApi,this.registerInteractiveComponent,this.unregisterInteractiveComponent),u=n.header&&n.header.hasTitle?this.state.viewLabelId:"";return Qr(lo.Provider,{value:l},n.header&&Qr(Pi,r({ref:this.headerRef,extraClassName:"fc-header-toolbar",model:n.header,titleId:u},i)),Qr(Ni,{liquid:a,height:s,aspectRatio:e,labeledById:u},this.renderView(t),this.buildAppendContent()),n.footer&&Qr(Pi,r({ref:this.footerRef,extraClassName:"fc-footer-toolbar",model:n.footer,titleId:""},i)))},t.prototype.componentDidMount=function(){var e=this.props;this.calendarInteractions=e.pluginHooks.calendarInteractions.map((function(t){return new t(e)})),window.addEventListener("resize",this.handleWindowResize);var t=e.pluginHooks.propSetHandlers;for(var n in t)t[n](e[n],e)},t.prototype.componentDidUpdate=function(e){var t=this.props,n=t.pluginHooks.propSetHandlers;for(var r in n)t[r]!==e[r]&&n[r](t[r],t)},t.prototype.componentWillUnmount=function(){window.removeEventListener("resize",this.handleWindowResize),this.resizeRunner.clear();for(var e=0,t=this.calendarInteractions;e<t.length;e++){t[e].destroy()}this.props.emitter.trigger("_unmount")},t.prototype.buildAppendContent=function(){var e=this.props,t=e.pluginHooks.viewContainerAppends.map((function(t){return t(e)}));return Qr.apply(void 0,o([no,{}],t))},t.prototype.renderView=function(e){for(var t=e.pluginHooks,n=e.viewSpec,o={dateProfile:e.dateProfile,businessHours:e.businessHours,eventStore:e.renderableEventStore,eventUiBases:e.eventUiBases,dateSelection:e.dateSelection,eventSelection:e.eventSelection,eventDrag:e.eventDrag,eventResize:e.eventResize,isHeightAuto:e.isHeightAuto,forPrint:e.forPrint},i=0,a=this.buildViewPropTransformers(t.viewPropsTransformers);i<a.length;i++){var s=a[i];r(o,s.transform(o,e))}var l=n.component;return Qr(l,r({},o))},t}(co);function Bi(e,t,n,r,o,i){var a=n.build(o,void 0,!1),s=n.buildPrev(t,r,!1),l=n.buildNext(t,r,!1);return{title:i,activeButton:e.type,navUnit:e.singleUnit,isTodayEnabled:a.isValid&&!On(t.currentRange,o),isPrevEnabled:s.isValid,isNextEnabled:l.isValid}}function Vi(e){return e.map((function(e){return new e}))}var Li=function(e){function t(){var t=null!==e&&e.apply(this,arguments)||this;return t.state={forPrint:!1},t.handleBeforePrint=function(){t.setState({forPrint:!0})},t.handleAfterPrint=function(){t.setState({forPrint:!1})},t}return n(t,e),t.prototype.render=function(){var e=this.props,t=e.options,n=this.state.forPrint,r=n||"auto"===t.height||"auto"===t.contentHeight,o=r||null==t.height?"":t.height,i=["fc",n?"fc-media-print":"fc-media-screen","fc-direction-"+t.direction,e.theme.getClass("root")];return kr()||i.push("fc-liquid-hack"),e.children(i,o,r,n)},t.prototype.componentDidMount=function(){var e=this.props.emitter;e.on("_beforeprint",this.handleBeforePrint),e.on("_afterprint",this.handleAfterPrint)},t.prototype.componentWillUnmount=function(){var e=this.props.emitter;e.off("_beforeprint",this.handleBeforePrint),e.off("_afterprint",this.handleAfterPrint)},t}(fo);function Wi(e,t){return Zt(!e||t>10?{weekday:"short"}:t>1?{weekday:"short",month:"numeric",day:"numeric",omitCommas:!0}:{weekday:"long"})}var Fi="fc-col-header-cell";function zi(e){return e.text}var ji=function(e){function t(){return null!==e&&e.apply(this,arguments)||this}return n(t,e),t.prototype.render=function(){var e=this.context,t=e.dateEnv,n=e.options,o=e.theme,i=e.viewApi,a=this.props,s=a.date,l=a.dateProfile,u=Or(s,a.todayRange,null,l),c=[Fi].concat(Pr(u,o)),d=t.format(s,a.dayHeaderFormat),f=!u.isDisabled&&a.colCnt>1?Ar(this.context,s):{},p=r(r(r({date:t.toDate(s),view:i},a.extraHookProps),{text:d}),u);return Qr(Eo,{hookProps:p,classNames:n.dayHeaderClassNames,content:n.dayHeaderContent,defaultContent:zi,didMount:n.dayHeaderDidMount,willUnmount:n.dayHeaderWillUnmount},(function(e,t,n,o){return Qr("th",r({ref:e,role:"columnheader",className:c.concat(t).join(" "),"data-date":u.isDisabled?void 0:kt(s),colSpan:a.colSpan},a.extraDataAttrs),Qr("div",{className:"fc-scrollgrid-sync-inner"},!u.isDisabled&&Qr("a",r({ref:n,className:["fc-col-header-cell-cushion",a.isSticky?"fc-sticky":""].join(" ")},f),o)))}))},t}(fo),qi=Zt({weekday:"long"}),Zi=function(e){function t(){return null!==e&&e.apply(this,arguments)||this}return n(t,e),t.prototype.render=function(){var e=this.props,t=this.context,n=t.dateEnv,o=t.theme,i=t.viewApi,a=t.options,s=qe(new Date(2592e5),e.dow),l={dow:e.dow,isDisabled:!1,isFuture:!1,isPast:!1,isToday:!1,isOther:!1},u=[Fi].concat(Pr(l,o),e.extraClassNames||[]),c=n.format(s,e.dayHeaderFormat),d=r(r(r(r({date:s},l),{view:i}),e.extraHookProps),{text:c});return Qr(Eo,{hookProps:d,classNames:a.dayHeaderClassNames,content:a.dayHeaderContent,defaultContent:zi,didMount:a.dayHeaderDidMount,willUnmount:a.dayHeaderWillUnmount},(function(t,o,i,a){return Qr("th",r({ref:t,role:"columnheader",className:u.concat(o).join(" "),colSpan:e.colSpan},e.extraDataAttrs),Qr("div",{className:"fc-scrollgrid-sync-inner"},Qr("a",{"aria-label":n.format(s,qi),className:["fc-col-header-cell-cushion",e.isSticky?"fc-sticky":""].join(" "),ref:i},a)))}))},t}(fo),Gi=function(e){function t(t,n){var r=e.call(this,t,n)||this;return r.initialNowDate=ar(n.options.now,n.dateEnv),r.initialNowQueriedMs=(new Date).valueOf(),r.state=r.computeTiming().currentState,r}return n(t,e),t.prototype.render=function(){var e=this.props,t=this.state;return e.children(t.nowDate,t.todayRange)},t.prototype.componentDidMount=function(){this.setTimeout()},t.prototype.componentDidUpdate=function(e){e.unit!==this.props.unit&&(this.clearTimeout(),this.setTimeout())},t.prototype.componentWillUnmount=function(){this.clearTimeout()},t.prototype.computeTiming=function(){var e=this.props,t=this.context,n=Ze(this.initialNowDate,(new Date).valueOf()-this.initialNowQueriedMs),r=t.dateEnv.startOf(n,e.unit),o=t.dateEnv.add(r,Ct(1,e.unit)),i=o.valueOf()-n.valueOf();return i=Math.min(864e5,i),{currentState:{nowDate:r,todayRange:Yi(r)},nextState:{nowDate:o,todayRange:Yi(o)},waitMs:i}},t.prototype.setTimeout=function(){var e=this,t=this.computeTiming(),n=t.nextState,r=t.waitMs;this.timeoutId=setTimeout((function(){e.setState(n,(function(){e.setTimeout()}))}),r)},t.prototype.clearTimeout=function(){this.timeoutId&&clearTimeout(this.timeoutId)},t.contextType=lo,t}(Jr);function Yi(e){var t=$e(e);return{start:t,end:qe(t,1)}}var Ki=function(e){function t(){var t=null!==e&&e.apply(this,arguments)||this;return t.createDayHeaderFormatter=xt(Xi),t}return n(t,e),t.prototype.render=function(){var e=this.context,t=this.props,n=t.dates,r=t.dateProfile,o=t.datesRepDistinctDays,i=t.renderIntro,a=this.createDayHeaderFormatter(e.options.dayHeaderFormat,o,n.length);return Qr(Gi,{unit:"day"},(function(e,t){return Qr("tr",{role:"row"},i&&i("day"),n.map((function(e){return o?Qr(ji,{key:e.toISOString(),date:e,dateProfile:r,todayRange:t,colCnt:n.length,dayHeaderFormat:a}):Qr(Zi,{key:e.getUTCDay(),dow:e.getUTCDay(),dayHeaderFormat:a})})))}))},t}(fo);function Xi(e,t,n){return e||Wi(t,n)}var $i=function(){function e(e,t){for(var n=e.start,r=e.end,o=[],i=[],a=-1;n<r;)t.isHiddenDay(n)?o.push(a+.5):(a+=1,o.push(a),i.push(n)),n=qe(n,1);this.dates=i,this.indices=o,this.cnt=i.length}return e.prototype.sliceRange=function(e){var t=this.getDateDayIndex(e.start),n=this.getDateDayIndex(qe(e.end,-1)),r=Math.max(0,t),o=Math.min(this.cnt-1,n);return(r=Math.ceil(r))<=(o=Math.floor(o))?{firstIndex:r,lastIndex:o,isStart:t===r,isEnd:n===o}:null},e.prototype.getDateDayIndex=function(e){var t=this.indices,n=Math.floor(Ge(this.dates[0],e));return n<0?t[0]-1:n>=t.length?t[t.length-1]+1:t[n]},e}(),Ji=function(){function e(e,t){var n,r,o,i=e.dates;if(t){for(r=i[0].getUTCDay(),n=1;n<i.length&&i[n].getUTCDay()!==r;n+=1);o=Math.ceil(i.length/n)}else o=1,n=i.length;this.rowCnt=o,this.colCnt=n,this.daySeries=e,this.cells=this.buildCells(),this.headerDates=this.buildHeaderDates()}return e.prototype.buildCells=function(){for(var e=[],t=0;t<this.rowCnt;t+=1){for(var n=[],r=0;r<this.colCnt;r+=1)n.push(this.buildCell(t,r));e.push(n)}return e},e.prototype.buildCell=function(e,t){var n=this.daySeries.dates[e*this.colCnt+t];return{key:n.toISOString(),date:n}},e.prototype.buildHeaderDates=function(){for(var e=[],t=0;t<this.colCnt;t+=1)e.push(this.cells[0][t].date);return e},e.prototype.sliceRange=function(e){var t=this.colCnt,n=this.daySeries.sliceRange(e),r=[];if(n)for(var o=n.firstIndex,i=n.lastIndex,a=o;a<=i;){var s=Math.floor(a/t),l=Math.min((s+1)*t,i+1);r.push({row:s,firstCol:a%t,lastCol:(l-1)%t,isStart:n.isStart&&a===o,isEnd:n.isEnd&&l-1===i}),a=l}return r},e}(),Qi=function(){function e(){this.sliceBusinessHours=xt(this._sliceBusinessHours),this.sliceDateSelection=xt(this._sliceDateSpan),this.sliceEventStore=xt(this._sliceEventStore),this.sliceEventDrag=xt(this._sliceInteraction),this.sliceEventResize=xt(this._sliceInteraction),this.forceDayIfListItem=!1}return e.prototype.sliceProps=function(e,t,n,r){for(var i=[],a=4;a<arguments.length;a++)i[a-4]=arguments[a];var s=e.eventUiBases,l=this.sliceEventStore.apply(this,o([e.eventStore,s,t,n],i));return{dateSelectionSegs:this.sliceDateSelection.apply(this,o([e.dateSelection,s,r],i)),businessHourSegs:this.sliceBusinessHours.apply(this,o([e.businessHours,t,n,r],i)),fgEventSegs:l.fg,bgEventSegs:l.bg,eventDrag:this.sliceEventDrag.apply(this,o([e.eventDrag,s,t,n],i)),eventResize:this.sliceEventResize.apply(this,o([e.eventResize,s,t,n],i)),eventSelection:e.eventSelection}},e.prototype.sliceNowDate=function(e,t){for(var n=[],r=2;r<arguments.length;r++)n[r-2]=arguments[r];return this._sliceDateSpan.apply(this,o([{range:{start:e,end:Ze(e,1)},allDay:!1},{},t],n))},e.prototype._sliceBusinessHours=function(e,t,n,r){for(var i=[],a=4;a<arguments.length;a++)i[a-4]=arguments[a];return e?this._sliceEventStore.apply(this,o([yt(e,ea(t,Boolean(n)),r),{},t,n],i)).bg:[]},e.prototype._sliceEventStore=function(e,t,n,r){for(var o=[],i=4;i<arguments.length;i++)o[i-4]=arguments[i];if(e){var a=Pn(e,t,ea(n,Boolean(r)),r);return{bg:this.sliceEventRanges(a.bg,o),fg:this.sliceEventRanges(a.fg,o)}}return{bg:[],fg:[]}},e.prototype._sliceInteraction=function(e,t,n,r){for(var o=[],i=4;i<arguments.length;i++)o[i-4]=arguments[i];if(!e)return null;var a=Pn(e.mutatedEvents,t,ea(n,Boolean(r)),r);return{segs:this.sliceEventRanges(a.fg,o),affectedInstances:e.affectedEvents.instances,isEvent:e.isEvent}},e.prototype._sliceDateSpan=function(e,t,n){for(var r=[],i=3;i<arguments.length;i++)r[i-3]=arguments[i];if(!e)return[];for(var a=Kn(e,t,n),s=this.sliceRange.apply(this,o([e.range],r)),l=0,u=s;l<u.length;l++){var c=u[l];c.eventRange=a}return s},e.prototype.sliceEventRanges=function(e,t){for(var n=[],r=0,o=e;r<o.length;r++){var i=o[r];n.push.apply(n,this.sliceEventRange(i,t))}return n},e.prototype.sliceEventRange=function(e,t){var n=e.range;this.forceDayIfListItem&&"list-item"===e.ui.display&&(n={start:n.start,end:qe(n.start,1)});for(var r=this.sliceRange.apply(this,o([n],t)),i=0,a=r;i<a.length;i++){var s=a[i];s.eventRange=e,s.isStart=e.isStart&&s.isStart,s.isEnd=e.isEnd&&s.isEnd}return r},e}();function ea(e,t){var n=e.activeRange;return t?n:{start:Ze(n.start,e.slotMinTime.milliseconds),end:Ze(n.end,e.slotMaxTime.milliseconds-864e5)}}function ta(e,t){var n=t.getCurrentData(),o=r({businessHours:n.businessHours,dateSelection:"",eventStore:n.eventStore,eventUiBases:n.eventUiBases,eventSelection:"",eventDrag:null,eventResize:null},e);return(t.pluginHooks.isPropsValid||na)(o,t)}function na(e,t,n,o){return void 0===n&&(n={}),!(e.eventDrag&&!function(e,t,n,o){var i=t.getCurrentData(),a=e.eventDrag,s=a.mutatedEvents,l=s.defs,u=s.instances,c=An(l,a.isEvent?e.eventUiBases:{"":i.selectionConfig});o&&(c=ct(c,o));var d=(v=e.eventStore,m=a.affectedEvents.instances,{defs:v.defs,instances:ut(v.instances,(function(e){return!m[e.instanceId]}))}),f=d.defs,p=d.instances,h=An(f,e.eventUiBases);var v,m;for(var g in u){var y=u[g],_=y.range,b=c[y.defId],E=l[y.defId];if(!ra(b.constraints,_,d,e.businessHours,t))return!1;var C=t.options.eventOverlap,D="function"==typeof C?C:null;for(var S in p){var w=p[S];if(Mn(_,w.range)){if(!1===h[w.defId].overlap&&a.isEvent)return!1;if(!1===b.overlap)return!1;if(D&&!D(new lr(t,f[w.defId],w),new lr(t,E,y)))return!1}}for(var T=i.eventStore,R=0,k=b.allows;R<k.length;R++){var I=k[R],M=r(r({},n),{range:y.range,allDay:E.allDay}),x=T.defs[E.defId],O=T.instances[g],P=void 0;if(P=x?new lr(t,x,O):new lr(t,E),!I($n(M,t),P))return!1}}return!0}(e,t,n,o))&&!(e.dateSelection&&!function(e,t,n,o){var i=e.eventStore,a=i.defs,s=i.instances,l=e.dateSelection,u=l.range,c=t.getCurrentData().selectionConfig;o&&(c=o(c));if(!ra(c.constraints,u,i,e.businessHours,t))return!1;var d=t.options.selectOverlap,f="function"==typeof d?d:null;for(var p in s){var h=s[p];if(Mn(u,h.range)){if(!1===c.overlap)return!1;if(f&&!f(new lr(t,a[h.defId],h),null))return!1}}for(var v=0,m=c.allows;v<m.length;v++){if(!(0,m[v])($n(r(r({},n),l),t),null))return!1}return!0}(e,t,n,o))}function ra(e,t,n,r,o){for(var i=0,a=e;i<a.length;i++){if(!aa(oa(a[i],t,n,r,o),t))return!1}return!0}function oa(e,t,n,r,o){return"businessHours"===e?ia(yt(r,t,o)):"string"==typeof e?ia(un(n,(function(t){return t.groupId===e}))):"object"==typeof e&&e?ia(yt(e,t,o)):[]}function ia(e){var t=e.instances,n=[];for(var r in t)n.push(t[r].range);return n}function aa(e,t){for(var n=0,r=e;n<r.length;n++){if(xn(r[n],t))return!0}return!1}var sa=/^(visible|hidden)$/,la=function(e){function t(){var t=null!==e&&e.apply(this,arguments)||this;return t.handleEl=function(e){t.el=e,vo(t.props.elRef,e)},t}return n(t,e),t.prototype.render=function(){var e=this.props,t=e.liquid,n=e.liquidIsAbsolute,r=t&&n,o=["fc-scroller"];return t&&(n?o.push("fc-scroller-liquid-absolute"):o.push("fc-scroller-liquid")),Qr("div",{ref:this.handleEl,className:o.join(" "),style:{overflowX:e.overflowX,overflowY:e.overflowY,left:r&&-(e.overcomeLeft||0)||"",right:r&&-(e.overcomeRight||0)||"",bottom:r&&-(e.overcomeBottom||0)||"",marginLeft:!r&&-(e.overcomeLeft||0)||"",marginRight:!r&&-(e.overcomeRight||0)||"",marginBottom:!r&&-(e.overcomeBottom||0)||"",maxHeight:e.maxHeight||""}},e.children)},t.prototype.needsXScrolling=function(){if(sa.test(this.props.overflowX))return!1;for(var e=this.el,t=this.el.getBoundingClientRect().width-this.getYScrollbarWidth(),n=e.children,r=0;r<n.length;r+=1){if(n[r].getBoundingClientRect().width>t)return!0}return!1},t.prototype.needsYScrolling=function(){if(sa.test(this.props.overflowY))return!1;for(var e=this.el,t=this.el.getBoundingClientRect().height-this.getXScrollbarWidth(),n=e.children,r=0;r<n.length;r+=1){if(n[r].getBoundingClientRect().height>t)return!0}return!1},t.prototype.getXScrollbarWidth=function(){return sa.test(this.props.overflowX)?0:this.el.offsetHeight-this.el.clientHeight},t.prototype.getYScrollbarWidth=function(){return sa.test(this.props.overflowY)?0:this.el.offsetWidth-this.el.clientWidth},t}(fo),ua=function(){function e(e){var t=this;this.masterCallback=e,this.currentMap={},this.depths={},this.callbackMap={},this.handleValue=function(e,n){var r=t,o=r.depths,i=r.currentMap,a=!1,s=!1;null!==e?(a=n in i,i[n]=e,o[n]=(o[n]||0)+1,s=!0):(o[n]-=1,o[n]||(delete i[n],delete t.callbackMap[n],a=!0)),t.masterCallback&&(a&&t.masterCallback(null,String(n)),s&&t.masterCallback(e,String(n)))}}return e.prototype.createRef=function(e){var t=this,n=this.callbackMap[e];return n||(n=this.callbackMap[e]=function(n){t.handleValue(n,String(e))}),n},e.prototype.collect=function(e,t,n){return gt(this.currentMap,e,t,n)},e.prototype.getAll=function(){return ft(this.currentMap)},e}();function ca(e){for(var t=0,n=0,r=Ce(e,".fc-scrollgrid-shrink");n<r.length;n++){var o=r[n];t=Math.max(t,ze(o))}return Math.ceil(t)}function da(e,t){return e.liquid&&t.liquid}function fa(e,t){return null!=t.maxHeight||da(e,t)}function pa(e,t,n,r){var o=n.expandRows;return"function"==typeof t.content?t.content(n):Qr("table",{role:"presentation",className:[t.tableClassName,e.syncRowHeights?"fc-scrollgrid-sync-table":""].join(" "),style:{minWidth:n.tableMinWidth,width:n.clientWidth,height:o?n.clientHeight:""}},n.tableColGroupNode,Qr(r?"thead":"tbody",{role:"presentation"},"function"==typeof t.rowContent?t.rowContent(n):t.rowContent))}function ha(e,t){return Mt(e,t,pt)}function va(e,t){for(var n=[],r=0,i=e;r<i.length;r++)for(var a=i[r],s=a.span||1,l=0;l<s;l+=1)n.push(Qr("col",{style:{width:"shrink"===a.width?ma(t):a.width||"",minWidth:a.minWidth||""}}));return Qr.apply(void 0,o(["colgroup",{}],n))}function ma(e){return null==e?4:e}function ga(e){for(var t=0,n=e;t<n.length;t++){if("shrink"===n[t].width)return!0}return!1}function ya(e,t){var n=["fc-scrollgrid",t.theme.getClass("table")];return e&&n.push("fc-scrollgrid-liquid"),n}function _a(e,t){var n=["fc-scrollgrid-section","fc-scrollgrid-section-"+e.type,e.className];return t&&e.liquid&&null==e.maxHeight&&n.push("fc-scrollgrid-section-liquid"),e.isSticky&&n.push("fc-scrollgrid-section-sticky"),n}var ba=function(e){function t(){var t=null!==e&&e.apply(this,arguments)||this;return t.processCols=xt((function(e){return e}),ha),t.renderMicroColGroup=xt(va),t.scrollerRefs=new ua,t.scrollerElRefs=new ua(t._handleScrollerEl.bind(t)),t.state={shrinkWidth:null,forceYScrollbars:!1,scrollerClientWidths:{},scrollerClientHeights:{}},t.handleSizing=function(){t.setState(r({shrinkWidth:t.computeShrinkWidth()},t.computeScrollerDims()))},t}return n(t,e),t.prototype.render=function(){var e=this,t=e.props,n=e.state,r=e.context,i=t.sections||[],a=this.processCols(t.cols),s=this.renderMicroColGroup(a,n.shrinkWidth),l=ya(t.liquid,r);t.collapsibleWidth&&l.push("fc-scrollgrid-collapsible");for(var u,c=i.length,d=0,f=[],p=[],h=[];d<c&&"header"===(u=i[d]).type;)f.push(this.renderSection(u,s,!0)),d+=1;for(;d<c&&"body"===(u=i[d]).type;)p.push(this.renderSection(u,s,!1)),d+=1;for(;d<c&&"footer"===(u=i[d]).type;)h.push(this.renderSection(u,s,!0)),d+=1;var v=!kr(),m={role:"rowgroup"};return Qr("table",{role:"grid",className:l.join(" "),style:{height:t.height}},Boolean(!v&&f.length)&&Qr.apply(void 0,o(["thead",m],f)),Boolean(!v&&p.length)&&Qr.apply(void 0,o(["tbody",m],p)),Boolean(!v&&h.length)&&Qr.apply(void 0,o(["tfoot",m],h)),v&&Qr.apply(void 0,o(o(o(["tbody",m],f),p),h)))},t.prototype.renderSection=function(e,t,n){return"outerContent"in e?Qr(no,{key:e.key},e.outerContent):Qr("tr",{key:e.key,role:"presentation",className:_a(e,this.props.liquid).join(" ")},this.renderChunkTd(e,t,e.chunk,n))},t.prototype.renderChunkTd=function(e,t,n,r){if("outerContent"in n)return n.outerContent;var o=this.props,i=this.state,a=i.forceYScrollbars,s=i.scrollerClientWidths,l=i.scrollerClientHeights,u=fa(o,e),c=da(o,e),d=o.liquid?a?"scroll":u?"auto":"hidden":"visible",f=e.key,p=pa(e,n,{tableColGroupNode:t,tableMinWidth:"",clientWidth:o.collapsibleWidth||void 0===s[f]?null:s[f],clientHeight:void 0!==l[f]?l[f]:null,expandRows:e.expandRows,syncRowHeights:!1,rowSyncHeights:[],reportRowHeightChange:function(){}},r);return Qr(r?"th":"td",{ref:n.elRef,role:"presentation"},Qr("div",{className:"fc-scroller-harness"+(c?" fc-scroller-harness-liquid":"")},Qr(la,{ref:this.scrollerRefs.createRef(f),elRef:this.scrollerElRefs.createRef(f),overflowY:d,overflowX:o.liquid?"hidden":"visible",maxHeight:e.maxHeight,liquid:c,liquidIsAbsolute:!0},p)))},t.prototype._handleScrollerEl=function(e,t){var n=function(e,t){for(var n=0,r=e;n<r.length;n++){var o=r[n];if(o.key===t)return o}return null}(this.props.sections,t);n&&vo(n.chunk.scrollerElRef,e)},t.prototype.componentDidMount=function(){this.handleSizing(),this.context.addResizeHandler(this.handleSizing)},t.prototype.componentDidUpdate=function(){this.handleSizing()},t.prototype.componentWillUnmount=function(){this.context.removeResizeHandler(this.handleSizing)},t.prototype.computeShrinkWidth=function(){return ga(this.props.cols)?ca(this.scrollerElRefs.getAll()):0},t.prototype.computeScrollerDims=function(){var e=Lr(),t=this.scrollerRefs,n=this.scrollerElRefs,r=!1,o={},i={};for(var a in t.currentMap){var s=t.currentMap[a];if(s&&s.needsYScrolling()){r=!0;break}}for(var l=0,u=this.props.sections;l<u.length;l++){a=u[l].key;var c=n.currentMap[a];if(c){var d=c.parentNode;o[a]=Math.floor(d.getBoundingClientRect().width-(r?e.y:0)),i[a]=Math.floor(d.getBoundingClientRect().height)}}return{forceYScrollbars:r,scrollerClientWidths:o,scrollerClientHeights:i}},t}(fo);ba.addStateEquality({scrollerClientWidths:pt,scrollerClientHeights:pt});var Ea=function(e){function t(){var t=null!==e&&e.apply(this,arguments)||this;return t.elRef=to(),t}return n(t,e),t.prototype.render=function(){var e=this.props,t=this.context,n=t.options,r=e.seg,o=r.eventRange,i=o.ui,a={event:new lr(t,o.def,o.instance),view:t.viewApi,timeText:e.timeText,textColor:i.textColor,backgroundColor:i.backgroundColor,borderColor:i.borderColor,isDraggable:!e.disableDragging&&Vn(r,t),isStartResizable:!e.disableResizing&&Ln(r,t),isEndResizable:!e.disableResizing&&Wn(r),isMirror:Boolean(e.isDragging||e.isResizing||e.isDateSelecting),isStart:Boolean(r.isStart),isEnd:Boolean(r.isEnd),isPast:Boolean(e.isPast),isFuture:Boolean(e.isFuture),isToday:Boolean(e.isToday),isSelected:Boolean(e.isSelected),isDragging:Boolean(e.isDragging),isResizing:Boolean(e.isResizing)},s=zn(a).concat(i.classNames);return Qr(Eo,{hookProps:a,classNames:n.eventClassNames,content:n.eventContent,defaultContent:e.defaultContent,didMount:n.eventDidMount,willUnmount:n.eventWillUnmount,elRef:this.elRef},(function(t,n,r,o){return e.children(t,s.concat(n),r,o,a)}))},t.prototype.componentDidMount=function(){Nn(this.elRef.current,this.props.seg)},t.prototype.componentDidUpdate=function(e){var t=this.props.seg;t!==e.seg&&Nn(this.elRef.current,t)},t}(fo),Ca=function(e){function t(){return null!==e&&e.apply(this,arguments)||this}return n(t,e),t.prototype.render=function(){var e=this.props,t=this.context,n=e.seg,o=t.options.eventTimeFormat||e.defaultTimeFormat,i=Fn(n,o,t,e.defaultDisplayEventTime,e.defaultDisplayEventEnd);return Qr(Ea,{seg:n,timeText:i,disableDragging:e.disableDragging,disableResizing:e.disableResizing,defaultContent:e.defaultContent||Da,isDragging:e.isDragging,isResizing:e.isResizing,isDateSelecting:e.isDateSelecting,isSelected:e.isSelected,isPast:e.isPast,isFuture:e.isFuture,isToday:e.isToday},(function(o,i,a,s,l){return Qr("a",r({className:e.extraClassNames.concat(i).join(" "),style:{borderColor:l.borderColor,backgroundColor:l.backgroundColor},ref:o},jn(n,t)),Qr("div",{className:"fc-event-main",ref:a,style:{color:l.textColor}},s),l.isStartResizable&&Qr("div",{className:"fc-event-resizer fc-event-resizer-start"}),l.isEndResizable&&Qr("div",{className:"fc-event-resizer fc-event-resizer-end"}))}))},t}(fo);function Da(e){return Qr("div",{className:"fc-event-main-frame"},e.timeText&&Qr("div",{className:"fc-event-time"},e.timeText),Qr("div",{className:"fc-event-title-container"},Qr("div",{className:"fc-event-title fc-sticky"},e.event.title||Qr(no,null," "))))}var Sa=Zt({day:"numeric"}),wa=function(e){function t(){return null!==e&&e.apply(this,arguments)||this}return n(t,e),t.prototype.render=function(){var e=this.props,t=this.context,n=t.options,r=Ta({date:e.date,dateProfile:e.dateProfile,todayRange:e.todayRange,showDayNumber:e.showDayNumber,extraProps:e.extraHookProps,viewApi:t.viewApi,dateEnv:t.dateEnv});return Qr(Do,{hookProps:r,content:n.dayCellContent,defaultContent:e.defaultContent},e.children)},t}(fo);function Ta(e){var t=e.date,n=e.dateEnv,o=Or(t,e.todayRange,null,e.dateProfile);return r(r(r({date:n.toDate(t),view:e.viewApi},o),{dayNumberText:e.showDayNumber?n.format(t,Sa):""}),e.extraProps)}var Ra=function(e){function t(){var t=null!==e&&e.apply(this,arguments)||this;return t.refineHookProps=Ot(Ta),t.normalizeClassNames=To(),t}return n(t,e),t.prototype.render=function(){var e=this.props,t=this.context,n=t.options,r=this.refineHookProps({date:e.date,dateProfile:e.dateProfile,todayRange:e.todayRange,showDayNumber:e.showDayNumber,extraProps:e.extraHookProps,viewApi:t.viewApi,dateEnv:t.dateEnv}),o=Pr(r,t.theme).concat(r.isDisabled?[]:this.normalizeClassNames(n.dayCellClassNames,r)),i=r.isDisabled?{}:{"data-date":kt(e.date)};return Qr(wo,{hookProps:r,didMount:n.dayCellDidMount,willUnmount:n.dayCellWillUnmount,elRef:e.elRef},(function(t){return e.children(t,o,i,r.isDisabled)}))},t}(fo);function ka(e){return e.event.title&&Qr("div",{className:"fc-event-title"},e.event.title)}function Ia(e){return e.text}var Ma=function(e){function t(){var t=null!==e&&e.apply(this,arguments)||this;return t.state={titleId:ke()},t.handleRootEl=function(e){t.rootEl=e,t.props.elRef&&vo(t.props.elRef,e)},t.handleDocumentMouseDown=function(e){var n=Te(e);t.rootEl.contains(n)||t.handleCloseClick()},t.handleDocumentKeyDown=function(e){"Escape"===e.key&&t.handleCloseClick()},t.handleCloseClick=function(){var e=t.props.onClose;e&&e()},t}return n(t,e),t.prototype.render=function(){var e=this.context,t=e.theme,n=e.options,o=this.props,i=this.state,a=["fc-popover",t.getClass("popover")].concat(o.extraClassNames||[]);return oo(Qr("div",r({id:o.id,className:a.join(" "),"aria-labelledby":i.titleId},o.extraAttrs,{ref:this.handleRootEl}),Qr("div",{className:"fc-popover-header "+t.getClass("popoverHeader")},Qr("span",{className:"fc-popover-title",id:i.titleId},o.title),Qr("span",{className:"fc-popover-close "+t.getIconClass("close"),title:n.closeHint,onClick:this.handleCloseClick})),Qr("div",{className:"fc-popover-body "+t.getClass("popoverContent")},o.children)),o.parentEl)},t.prototype.componentDidMount=function(){document.addEventListener("mousedown",this.handleDocumentMouseDown),document.addEventListener("keydown",this.handleDocumentKeyDown),this.updateSize()},t.prototype.componentWillUnmount=function(){document.removeEventListener("mousedown",this.handleDocumentMouseDown),document.removeEventListener("keydown",this.handleDocumentKeyDown)},t.prototype.updateSize=function(){var e=this.context.isRtl,t=this.props,n=t.alignmentEl,r=t.alignGridTop,o=this.rootEl,i=function(e){for(var t=jr(e),n=e.getBoundingClientRect(),r=0,o=t;r<o.length;r++){var i=Rr(n,o[r].getBoundingClientRect());if(!i)return null;n=i}return n}(n);if(i){var a=o.getBoundingClientRect(),s=r?be(n,".fc-scrollgrid").getBoundingClientRect().top:i.top,l=e?i.right-a.width:i.left;s=Math.max(s,10),l=Math.min(l,document.documentElement.clientWidth-10-a.width),l=Math.max(l,10);var u=o.offsetParent.getBoundingClientRect();Se(o,{top:s-u.top,left:l-u.left})}},t}(fo),xa=function(e){function t(){var t=null!==e&&e.apply(this,arguments)||this;return t.handleRootEl=function(e){t.rootEl=e,e?t.context.registerInteractiveComponent(t,{el:e,useEventCenter:!1}):t.context.unregisterInteractiveComponent(t)},t}return n(t,e),t.prototype.render=function(){var e=this.context,t=e.options,n=e.dateEnv,r=this.props,o=r.startDate,i=r.todayRange,a=r.dateProfile,s=n.format(o,t.dayPopoverFormat);return Qr(Ra,{date:o,dateProfile:a,todayRange:i,elRef:this.handleRootEl},(function(e,t,n){return Qr(Ma,{elRef:e,id:r.id,title:s,extraClassNames:["fc-more-popover"].concat(t),extraAttrs:n,parentEl:r.parentEl,alignmentEl:r.alignmentEl,alignGridTop:r.alignGridTop,onClose:r.onClose},Qr(wa,{date:o,dateProfile:a,todayRange:i},(function(e,t){return t&&Qr("div",{className:"fc-more-popover-misc",ref:e},t)})),r.children)}))},t.prototype.queryHit=function(e,t,n,o){var i=this.rootEl,a=this.props;return e>=0&&e<n&&t>=0&&t<o?{dateProfile:a.dateProfile,dateSpan:r({allDay:!0,range:{start:a.startDate,end:a.endDate}},a.extraDateSpan),dayEl:i,rect:{left:0,top:0,right:n,bottom:o},layer:1}:null},t}(mo),Oa=function(e){function t(){var t=null!==e&&e.apply(this,arguments)||this;return t.linkElRef=to(),t.state={isPopoverOpen:!1,popoverId:ke()},t.handleClick=function(e){var n=t,r=n.props,o=n.context,i=o.options.moreLinkClick,a=Na(r).start;function s(e){var t=e.eventRange,n=t.def,r=t.instance,i=t.range;return{event:new lr(o,n,r),start:o.dateEnv.toDate(i.start),end:o.dateEnv.toDate(i.end),isStart:e.isStart,isEnd:e.isEnd}}"function"==typeof i&&(i=i({date:a,allDay:Boolean(r.allDayDate),allSegs:r.allSegs.map(s),hiddenSegs:r.hiddenSegs.map(s),jsEvent:e,view:o.viewApi})),i&&"popover"!==i?"string"==typeof i&&o.calendarApi.zoomTo(a,i):t.setState({isPopoverOpen:!0})},t.handlePopoverClose=function(){t.setState({isPopoverOpen:!1})},t}return n(t,e),t.prototype.render=function(){var e=this,t=this.props,n=this.state;return Qr(lo.Consumer,null,(function(r){var o=r.viewApi,i=r.options,a=r.calendarApi,s=i.moreLinkText,l=t.moreCnt,u=Na(t),c="function"==typeof s?s.call(a,l):"+"+l+" "+s,d=We(i.moreLinkHint,[l],c),f={num:l,shortText:"+"+l,text:c,view:o};return Qr(no,null,Boolean(t.moreCnt)&&Qr(Eo,{elRef:e.linkElRef,hookProps:f,classNames:i.moreLinkClassNames,content:i.moreLinkContent,defaultContent:t.defaultContent||Pa,didMount:i.moreLinkDidMount,willUnmount:i.moreLinkWillUnmount},(function(r,o,i,a){return t.children(r,["fc-more-link"].concat(o),i,a,e.handleClick,d,n.isPopoverOpen,n.isPopoverOpen?n.popoverId:"")})),n.isPopoverOpen&&Qr(xa,{id:n.popoverId,startDate:u.start,endDate:u.end,dateProfile:t.dateProfile,todayRange:t.todayRange,extraDateSpan:t.extraDateSpan,parentEl:e.parentEl,alignmentEl:t.alignmentElRef.current,alignGridTop:t.alignGridTop,onClose:e.handlePopoverClose},t.popoverContent()))}))},t.prototype.componentDidMount=function(){this.updateParentEl()},t.prototype.componentDidUpdate=function(){this.updateParentEl()},t.prototype.updateParentEl=function(){this.linkElRef.current&&(this.parentEl=be(this.linkElRef.current,".fc-view-harness"))},t}(fo);function Pa(e){return e.text}function Na(e){if(e.allDayDate)return{start:e.allDayDate,end:qe(e.allDayDate,1)};var t,n=e.hiddenSegs;return{start:Ha(n),end:(t=n,t.reduce(Ua).eventRange.range.end)}}function Ha(e){return e.reduce(Aa).eventRange.range.start}function Aa(e,t){return e.eventRange.range.start<t.eventRange.range.start?e:t}function Ua(e,t){return e.eventRange.range.end>t.eventRange.range.end?e:t}var Ba=function(e){function t(t,n){void 0===n&&(n={});var o=e.call(this)||this;return o.isRendering=!1,o.isRendered=!1,o.currentClassNames=[],o.customContentRenderId=0,o.handleAction=function(e){switch(e.type){case"SET_EVENT_DRAG":case"SET_EVENT_RESIZE":o.renderRunner.tryDrain()}},o.handleData=function(e){o.currentData=e,o.renderRunner.request(e.calendarOptions.rerenderDelay)},o.handleRenderRequest=function(){if(o.isRendering){o.isRendered=!0;var e=o.currentData;eo(Qr(Li,{options:e.calendarOptions,theme:e.theme,emitter:e.emitter},(function(t,n,i,a){return o.setClassNames(t),o.setHeight(n),Qr(Co.Provider,{value:o.customContentRenderId},Qr(Ui,r({isHeightAuto:i,forPrint:a},e)))})),o.el)}else o.isRendered&&(o.isRendered=!1,ao(o.el),o.setClassNames([]),o.setHeight(""));io()},o.el=t,o.renderRunner=new oi(o.handleRenderRequest),new si({optionOverrides:n,calendarApi:o,onAction:o.handleAction,onData:o.handleData}),o}return n(t,e),Object.defineProperty(t.prototype,"view",{get:function(){return this.currentData.viewApi},enumerable:!1,configurable:!0}),t.prototype.render=function(){var e=this.isRendering;e?this.customContentRenderId+=1:this.isRendering=!0,this.renderRunner.request(),e&&this.updateSize()},t.prototype.destroy=function(){this.isRendering&&(this.isRendering=!1,this.renderRunner.request())},t.prototype.updateSize=function(){e.prototype.updateSize.call(this),io()},t.prototype.batchRendering=function(e){this.renderRunner.pause("batchRendering"),e(),this.renderRunner.resume("batchRendering")},t.prototype.pauseRendering=function(){this.renderRunner.pause("pauseRendering")},t.prototype.resumeRendering=function(){this.renderRunner.resume("pauseRendering",!0)},t.prototype.resetOptions=function(e,t){this.currentDataManager.resetOptions(e,t)},t.prototype.setClassNames=function(e){if(!Mt(e,this.currentClassNames)){for(var t=this.el.classList,n=0,r=this.currentClassNames;n<r.length;n++){var o=r[n];t.remove(o)}for(var i=0,a=e;i<a.length;i++){o=a[i];t.add(o)}this.currentClassNames=e}},t.prototype.setHeight=function(e){we(this.el,"height",e)},t}(sr);return e.BASE_OPTION_DEFAULTS=Yt,e.BASE_OPTION_REFINERS=Gt,e.BaseComponent=fo,e.BgEvent=function(e){return Qr(Ea,{defaultContent:ka,seg:e.seg,timeText:"",disableDragging:!0,disableResizing:!0,isDragging:!1,isResizing:!1,isDateSelecting:!1,isSelected:!1,isPast:e.isPast,isFuture:e.isFuture,isToday:e.isToday},(function(e,t,n,r,o){return Qr("div",{ref:e,className:["fc-bg-event"].concat(t).join(" "),style:{backgroundColor:o.backgroundColor}},r)}))},e.Calendar=Ba,e.CalendarApi=sr,e.CalendarContent=Ui,e.CalendarDataManager=si,e.CalendarDataProvider=yi,e.CalendarRoot=Li,e.Component=Jr,e.ContentHook=Do,e.CustomContentRenderContext=Co,e.DateComponent=mo,e.DateEnv=mr,e.DateProfileGenerator=No,e.DayCellContent=wa,e.DayCellRoot=Ra,e.DayHeader=Ki,e.DaySeriesModel=$i,e.DayTableModel=Ji,e.DelayedRunner=oi,e.ElementDragging=Mi,e.ElementScrollController=Kr,e.Emitter=Zr,e.EventApi=lr,e.EventRoot=Ea,e.EventSourceApi=ye,e.Fragment=no,e.Interaction=Ri,e.MoreLinkRoot=Oa,e.MountHook=wo,e.NamedTimeZoneImpl=_i,e.NowIndicatorRoot=function(e){return Qr(lo.Consumer,null,(function(t){var n=t.options,r={isAxis:e.isAxis,date:t.dateEnv.toDate(e.date),view:t.viewApi};return Qr(Eo,{hookProps:r,classNames:n.nowIndicatorClassNames,content:n.nowIndicatorContent,didMount:n.nowIndicatorDidMount,willUnmount:n.nowIndicatorWillUnmount},e.children)}))},e.NowTimer=Gi,e.PositionCache=Gr,e.RefMap=ua,e.RenderHook=Eo,e.ScrollController=Yr,e.ScrollResponder=so,e.Scroller=la,e.SegHierarchy=bi,e.SimpleScrollGrid=ba,e.Slicer=Qi,e.Splitter=Mr,e.StandardEvent=Ca,e.TableDateCell=ji,e.TableDowCell=Zi,e.Theme=$r,e.ViewApi=nr,e.ViewContextType=lo,e.ViewRoot=Io,e.WeekNumberRoot=function(e){return Qr(lo.Consumer,null,(function(t){var n=t.dateEnv,r=t.options,o=e.date,i=r.weekNumberFormat||e.defaultFormat,a=n.computeWeekNumber(o),s=n.format(o,i);return Qr(Eo,{hookProps:{num:a,text:s,date:o},classNames:r.weekNumberClassNames,content:r.weekNumberContent,defaultContent:Ia,didMount:r.weekNumberDidMount,willUnmount:r.weekNumberWillUnmount},e.children)}))},e.WindowScrollController=Xr,e.addDays=qe,e.addDurations=function(e,t){return{years:e.years+t.years,months:e.months+t.months,days:e.days+t.days,milliseconds:e.milliseconds+t.milliseconds}},e.addMs=Ze,e.addWeeks=function(e,t){var n=nt(e);return n[2]+=7*t,rt(n)},e.allowContextMenu=function(e){e.removeEventListener("contextmenu",Ie)},e.allowSelection=function(e){e.classList.remove("fc-unselectable"),e.removeEventListener("selectstart",Ie)},e.applyMutationToEventStore=Qn,e.applyStyle=Se,e.applyStyleProp=we,e.asCleanDays=function(e){return e.years||e.months||e.milliseconds?0:e.days},e.asRoughMinutes=function(e){return wt(e)/6e4},e.asRoughMs=wt,e.asRoughSeconds=function(e){return wt(e)/1e3},e.binarySearch=Ti,e.buildClassNameNormalizer=To,e.buildEntryKey=Ci,e.buildEventApis=cr,e.buildEventRangeKey=function(e){return e.instance?e.instance.instanceId:e.def.defId+":"+e.range.start.toISOString()},e.buildHashFromArray=function(e,t){for(var n={},r=0;r<e.length;r+=1){var o=t(e[r],r);n[o[0]]=o[1]}return n},e.buildIsoString=Rt,e.buildNavLinkAttrs=Ar,e.buildSegCompareObj=Bn,e.buildSegTimeText=Fn,e.collectFromHash=gt,e.combineEventUis=hn,e.compareByFieldSpec=Be,e.compareByFieldSpecs=Ue,e.compareNumbers=function(e,t){return e-t},e.compareObjs=vt,e.computeEarliestSegStart=Ha,e.computeEdges=Fr,e.computeFallbackHeaderFormat=Wi,e.computeHeightAndMargins=function(e){return e.getBoundingClientRect().height+function(e){var t=window.getComputedStyle(e);return parseInt(t.marginTop,10)+parseInt(t.marginBottom,10)}(e)},e.computeInnerRect=function(e,t,n){void 0===t&&(t=!1);var r=n?e.getBoundingClientRect():zr(e),o=Fr(e,t),i={left:r.left+o.borderLeft+o.scrollbarLeft,right:r.right-o.borderRight-o.scrollbarRight,top:r.top+o.borderTop,bottom:r.bottom-o.borderBottom-o.scrollbarBottom};return t&&(i.left+=o.paddingLeft,i.right-=o.paddingRight,i.top+=o.paddingTop,i.bottom-=o.paddingBottom),i},e.computeRect=zr,e.computeSegDraggable=Vn,e.computeSegEndResizable=Wn,e.computeSegStartResizable=Ln,e.computeShrinkWidth=ca,e.computeSmallestCellWidth=ze,e.computeVisibleDayRange=Sn,e.config={},e.constrainPoint=function(e,t){return{left:Math.min(Math.max(e.left,t.left),t.right),top:Math.min(Math.max(e.top,t.top),t.bottom)}},e.createAriaClickAttrs=Oe,e.createContext=ro,e.createDuration=Ct,e.createElement=Qr,e.createEmptyEventStore=sn,e.createEventInstance=at,e.createEventUi=pn,e.createFormatter=Zt,e.createPlugin=go,e.createPortal=oo,e.createRef=to,e.diffDates=wn,e.diffDayAndTime=Ye,e.diffDays=Ge,e.diffPoints=function(e,t){return{left:e.left-t.left,top:e.top-t.top}},e.diffWeeks=function(e,t){return Ge(e,t)/7},e.diffWholeDays=Xe,e.diffWholeWeeks=Ke,e.disableCursor=function(){document.body.classList.add("fc-not-allowed")},e.elementClosest=be,e.elementMatches=Ee,e.enableCursor=function(){document.body.classList.remove("fc-not-allowed")},e.eventTupleToStore=on,e.filterEventStoreDefs=un,e.filterHash=ut,e.findDirectChildren=function(e,t){for(var n=e instanceof HTMLElement?[e]:e,r=[],o=0;o<n.length;o+=1)for(var i=n[o].children,a=0;a<i.length;a+=1){var s=i[a];t&&!Ee(s,t)||r.push(s)}return r},e.findElements=Ce,e.flexibleCompare=Ve,e.flushToDom=io,e.formatDate=function(e,t){void 0===t&&(t={});var n=Dr(t),r=Zt(t),o=n.createMarkerMeta(e);return o?n.format(o.marker,r,{forcedTzo:o.forcedTzo}):""},e.formatDayString=kt,e.formatIsoTimeString=function(e){return Le(e.getUTCHours(),2)+":"+Le(e.getUTCMinutes(),2)+":"+Le(e.getUTCSeconds(),2)},e.formatRange=function(e,t,n){var r=Dr("object"==typeof n&&n?n:{}),o=Zt(n),i=r.createMarkerMeta(e),a=r.createMarkerMeta(t);return i&&a?r.formatRange(i.marker,a.marker,o,{forcedStartTzo:i.forcedTzo,forcedEndTzo:a.forcedTzo,isEndExclusive:n.isEndExclusive,defaultSeparator:Yt.defaultRangeSeparator}):""},e.getAllowYScrolling=fa,e.getCanVGrowWithinCell=kr,e.getClippingParents=jr,e.getDateMeta=Or,e.getDayClassNames=Pr,e.getDefaultEventEnd=Jn,e.getElRoot=function(e){return e.getRootNode?e.getRootNode():document},e.getElSeg=Hn,e.getEntrySpanEnd=Ei,e.getEventClassNames=zn,e.getEventTargetViaRoot=Te,e.getIsRtlScrollbarOnLeft=Vr,e.getRectCenter=function(e){return{left:(e.left+e.right)/2,top:(e.top+e.bottom)/2}},e.getRelevantEvents=an,e.getScrollGridClassNames=ya,e.getScrollbarWidths=Lr,e.getSectionClassNames=_a,e.getSectionHasLiquidHeight=da,e.getSegAnchorAttrs=jn,e.getSegMeta=function(e,t,n){var r=e.eventRange.range;return{isPast:r.end<(n||t.start),isFuture:r.start>=(n||t.end),isToday:t&&On(t,r.start)}},e.getSlotClassNames=function(e,t){var n=["fc-slot","fc-slot-"+je[e.dow]];return e.isDisabled?n.push("fc-slot-disabled"):(e.isToday&&(n.push("fc-slot-today"),n.push(t.getClass("today"))),e.isPast&&n.push("fc-slot-past"),e.isFuture&&n.push("fc-slot-future")),n},e.getStickyFooterScrollbar=function(e){var t=e.stickyFooterScrollbar;return null!=t&&"auto"!==t||(t="auto"===e.height||"auto"===e.viewHeight),t},e.getStickyHeaderDates=function(e){var t=e.stickyHeaderDates;return null!=t&&"auto"!==t||(t="auto"===e.height||"auto"===e.viewHeight),t},e.getUnequalProps=ht,e.getUniqueDomId=ke,e.globalLocales=gr,e.globalPlugins=ri,e.greatestDurationDenominator=Tt,e.groupIntersectingEntries=function(e){for(var t=[],n=0,r=e;n<r.length;n++){for(var o=r[n],i=[],a={span:o.span,entries:[o]},s=0,l=t;s<l.length;s++){var u=l[s];Si(u.span,a.span)?a={entries:u.entries.concat(a.entries),span:Di(u.span,a.span)}:i.push(u)}i.push(a),t=i}return t},e.guid=He,e.hasBgRendering=function(e){return"background"===e.ui.display||"inverse-background"===e.ui.display},e.hasShrinkWidth=ga,e.identity=nn,e.interactionSettingsStore=Ii,e.interactionSettingsToStore=function(e){var t;return(t={})[e.component.uid]=e,t},e.intersectRanges=kn,e.intersectRects=Rr,e.intersectSpans=Si,e.isArraysEqual=Mt,e.isColPropsEqual=ha,e.isDateSelectionValid=function(e,t,n){return!!xn(t.validRange,e.range)&&ta({dateSelection:e},n)},e.isDateSpansEqual=function(e,t){return In(e.range,t.range)&&e.allDay===t.allDay&&function(e,t){for(var n in t)if("range"!==n&&"allDay"!==n&&e[n]!==t[n])return!1;for(var n in e)if(!(n in t))return!1;return!0}(e,t)},e.isInt=Fe,e.isInteractionValid=function(e,t,n){var r=e.mutatedEvents.instances;for(var o in r)if(!xn(t.validRange,r[o].range))return!1;return ta({eventDrag:e},n)},e.isMultiDayRange=function(e){var t=Sn(e);return Ge(t.start,t.end)>1},e.isPropsEqual=pt,e.isPropsValid=na,e.isValidDate=ot,e.joinSpans=Di,e.listenBySelector=Me,e.mapHash=ct,e.memoize=xt,e.memoizeArraylike=function(e,t,n){var r=this,o=[],i=[];return function(a){for(var s=o.length,l=a.length,u=0;u<s;u+=1)if(a[u]){if(!Mt(o[u],a[u])){n&&n(i[u]);var c=e.apply(r,a[u]);t&&t(c,i[u])||(i[u]=c)}}else n&&n(i[u]);for(;u<l;u+=1)i[u]=e.apply(r,a[u]);return o=a,i.splice(l),i}},e.memoizeHashlike=function(e,t,n){var r=this,o={},i={};return function(a){var s={};for(var l in a)if(i[l])if(Mt(o[l],a[l]))s[l]=i[l];else{n&&n(i[l]);var u=e.apply(r,a[l]);s[l]=t&&t(u,i[l])?i[l]:u}else s[l]=e.apply(r,a[l]);return o=a,i=s,s}},e.memoizeObjArg=Ot,e.mergeEventStores=ln,e.multiplyDuration=function(e,t){return{years:e.years*t,months:e.months*t,days:e.days*t,milliseconds:e.milliseconds*t}},e.padStart=Le,e.parseBusinessHours=Tr,e.parseClassNames=cn,e.parseDragMeta=function(e){var t=tn(e,xi),n=t.refined,r=t.extra;return{startTime:n.startTime||null,duration:n.duration||null,create:null==n.create||n.create,sourceId:n.sourceId,leftoverProps:r}},e.parseEventDef=Cn,e.parseFieldSpecs=Ae,e.parseMarker=vr,e.pointInsideRect=function(e,t){return e.left>=t.left&&e.left<t.right&&e.top>=t.top&&e.top<t.bottom},e.preventContextMenu=function(e){e.addEventListener("contextmenu",Ie)},e.preventDefault=Ie,e.preventSelection=function(e){e.classList.add("fc-unselectable"),e.addEventListener("selectstart",Ie)},e.rangeContainsMarker=On,e.rangeContainsRange=xn,e.rangesEqual=In,e.rangesIntersect=Mn,e.refineEventDef=bn,e.refineProps=tn,e.removeElement=_e,e.removeExact=function(e,t){for(var n=0,r=0;r<e.length;)e[r]===t?(e.splice(r,1),n+=1):r+=1;return n},e.render=eo,e.renderChunkContent=pa,e.renderFill=function(e){return Qr("div",{className:"fc-"+e})},e.renderMicroColGroup=va,e.renderScrollShim=function(e){return Qr("div",{className:"fc-scrollgrid-sticky-shim",style:{width:e.clientWidth,minWidth:e.tableMinWidth}})},e.requestJson=ei,e.sanitizeShrinkWidth=ma,e.setElSeg=Nn,e.setRef=vo,e.sliceEventStore=Pn,e.sliceEvents=function(e,t){return Pn(e.eventStore,e.eventUiBases,e.dateProfile.activeRange,t?e.nextDayThreshold:null).fg},e.sortEventSegs=function(e,t){var n=e.map(Bn);return n.sort((function(e,n){return Ue(e,n,t)})),n.map((function(e){return e._seg}))},e.startOfDay=$e,e.translateRect=function(e,t,n){return{left:e.left+t,right:e.right+t,top:e.top+n,bottom:e.bottom+n}},e.triggerDateSelect=Xn,e.unmountComponentAtNode=ao,e.unpromisify=qr,e.version="5.10.1",e.whenTransitionDone=function(e,t){var n=function(r){t(r),xe.forEach((function(t){e.removeEventListener(t,n)}))};xe.forEach((function(t){e.addEventListener(t,n)}))},e.wholeDivideDurations=function(e,t){for(var n=null,r=0;r<bt.length;r+=1){var o=bt[r];if(t[o]){var i=e[o]/t[o];if(!Fe(i)||null!==n&&n!==i)return null;n=i}else if(e[o])return null}return n},Object.defineProperty(e,"__esModule",{value:!0}),e}({});