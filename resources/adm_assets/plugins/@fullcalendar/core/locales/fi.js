'use strict';

Object.defineProperty(exports, '__esModule', { value: true });

var fi = {
  code: 'fi',
  week: {
    dow: 1, // Monday is the first day of the week.
    doy: 4, // The week that contains Jan 4th is the first week of the year.
  },
  buttonText: {
    prev: '<PERSON><PERSON><PERSON>',
    next: '<PERSON><PERSON><PERSON>',
    today: 'Tän<PERSON>än',
    month: '<PERSON>ukaus<PERSON>',
    week: 'Viik<PERSON>',
    day: '<PERSON><PERSON><PERSON><PERSON>',
    list: 'Ta<PERSON>htuma<PERSON>',
  },
  weekText: 'Vk',
  allDayText: '<PERSON><PERSON> päivä',
  moreLinkText: 'lisää',
  noEventsText: 'Ei näytettäviä tapahtumia',
};

exports.default = fi;
