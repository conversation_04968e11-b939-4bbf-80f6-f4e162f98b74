{"version": 3, "file": "main.js", "sources": ["src/main.ts"], "sourcesContent": ["import { Theme, createPlugin } from '@fullcalendar/common'\nimport './main.css'\n\nexport class BootstrapTheme extends Theme {\n}\n\nBootstrapTheme.prototype.classes = {\n  root: 'fc-theme-bootstrap', // TODO: compute this off of registered theme name\n  table: 'table-bordered', // don't attache the `table` class. we only want the borders, not any layout\n  tableCellShaded: 'table-active',\n  buttonGroup: 'btn-group',\n  button: 'btn btn-primary',\n  buttonActive: 'active',\n  popover: 'popover',\n  popoverHeader: 'popover-header',\n  popoverContent: 'popover-body',\n}\n\nBootstrapTheme.prototype.baseIconClass = 'fa'\nBootstrapTheme.prototype.iconClasses = {\n  close: 'fa-times',\n  prev: 'fa-chevron-left',\n  next: 'fa-chevron-right',\n  prevYear: 'fa-angle-double-left',\n  nextYear: 'fa-angle-double-right',\n}\nBootstrapTheme.prototype.rtlIconClasses = {\n  prev: 'fa-chevron-right',\n  next: 'fa-chevron-left',\n  prevYear: 'fa-angle-double-right',\n  nextYear: 'fa-angle-double-left',\n}\n\nBootstrapTheme.prototype.iconOverrideOption = 'bootstrapFontAwesome' // TODO: make TS-friendly. move the option-processing into this plugin\nBootstrapTheme.prototype.iconOverrideCustomButtonOption = 'bootstrapFontAwesome'\nBootstrapTheme.prototype.iconOverridePrefix = 'fa-'\n\nconst plugin = createPlugin({\n  themeClasses: {\n    bootstrap: BootstrapTheme,\n  },\n})\n\nexport default plugin\n"], "names": [], "mappings": ";;;;;;;;;;;IAGoC,kCAAK;IAAzC;;KACC;IAAD,qBAAC;AAAD,CADA,CAAoC,KAAK,GACxC;AAED,cAAc,CAAC,SAAS,CAAC,OAAO,GAAG;IACjC,IAAI,EAAE,oBAAoB;IAC1B,KAAK,EAAE,gBAAgB;IACvB,eAAe,EAAE,cAAc;IAC/B,WAAW,EAAE,WAAW;IACxB,MAAM,EAAE,iBAAiB;IACzB,YAAY,EAAE,QAAQ;IACtB,OAAO,EAAE,SAAS;IAClB,aAAa,EAAE,gBAAgB;IAC/B,cAAc,EAAE,cAAc;CAC/B,CAAA;AAED,cAAc,CAAC,SAAS,CAAC,aAAa,GAAG,IAAI,CAAA;AAC7C,cAAc,CAAC,SAAS,CAAC,WAAW,GAAG;IACrC,KAAK,EAAE,UAAU;IACjB,IAAI,EAAE,iBAAiB;IACvB,IAAI,EAAE,kBAAkB;IACxB,QAAQ,EAAE,sBAAsB;IAChC,QAAQ,EAAE,uBAAuB;CAClC,CAAA;AACD,cAAc,CAAC,SAAS,CAAC,cAAc,GAAG;IACxC,IAAI,EAAE,kBAAkB;IACxB,IAAI,EAAE,iBAAiB;IACvB,QAAQ,EAAE,uBAAuB;IACjC,QAAQ,EAAE,sBAAsB;CACjC,CAAA;AAED,cAAc,CAAC,SAAS,CAAC,kBAAkB,GAAG,sBAAsB,CAAA;AACpE,cAAc,CAAC,SAAS,CAAC,8BAA8B,GAAG,sBAAsB,CAAA;AAChF,cAAc,CAAC,SAAS,CAAC,kBAAkB,GAAG,KAAK,CAAA;IAE7C,MAAM,GAAG,YAAY,CAAC;IAC1B,YAAY,EAAE;QACZ,SAAS,EAAE,cAAc;KAC1B;CACF;;;;;"}