{"version": 3, "file": "main.js", "sources": ["src/TableView.tsx", "src/TableSeg.ts", "src/TableCellTop.tsx", "src/event-rendering.ts", "src/TableBlockEvent.tsx", "src/TableListItemEvent.tsx", "src/TableCellMoreLink.tsx", "src/TableCell.tsx", "src/event-placement.ts", "src/TableRow.tsx", "src/Table.tsx", "src/DayTableSlicer.tsx", "src/DayTable.tsx", "src/DayTableView.tsx", "src/TableDateProfileGenerator.ts", "src/main.ts"], "sourcesContent": ["import {\n  VNode, createElement,\n  SimpleScrollGrid,\n  SimpleScrollGridSection,\n  ChunkContentCallbackArgs,\n  createRef,\n  ScrollGridSectionConfig,\n  ViewRoot,\n  DateComponent,\n  ViewProps,\n  RefObject,\n  renderScrollShim,\n  getStickyHeaderDates,\n  getStickyFooterScrollbar,\n  ChunkConfigRowContent,\n  Dictionary,\n} from '@fullcalendar/common'\n\n/* An abstract class for the daygrid views, as well as month view. Renders one or more rows of day cells.\n----------------------------------------------------------------------------------------------------------------------*/\n// It is a manager for a Table subcomponent, which does most of the heavy lifting.\n// It is responsible for managing width/height.\n\nexport abstract class TableView<State=Dictionary> extends DateComponent<ViewProps, State> {\n  protected headerElRef: RefObject<HTMLTableCellElement> = createRef<HTMLTableCellElement>()\n\n  renderSimpleLayout(\n    headerRowContent: ChunkConfigRowContent,\n    bodyContent: (contentArg: ChunkContentCallbackArgs) => VNode,\n  ) {\n    let { props, context } = this\n    let sections: SimpleScrollGridSection[] = []\n    let stickyHeaderDates = getStickyHeaderDates(context.options)\n\n    if (headerRowContent) {\n      sections.push({\n        type: 'header',\n        key: 'header',\n        isSticky: stickyHeaderDates,\n        chunk: {\n          elRef: this.headerElRef,\n          tableClassName: 'fc-col-header',\n          rowContent: headerRowContent,\n        },\n      })\n    }\n\n    sections.push({\n      type: 'body',\n      key: 'body',\n      liquid: true,\n      chunk: { content: bodyContent },\n    })\n\n    return (\n      <ViewRoot viewSpec={context.viewSpec}>\n        {(rootElRef, classNames) => (\n          <div ref={rootElRef} className={['fc-daygrid'].concat(classNames).join(' ')}>\n            <SimpleScrollGrid\n              liquid={!props.isHeightAuto && !props.forPrint}\n              collapsibleWidth={props.forPrint}\n              cols={[] /* TODO: make optional? */}\n              sections={sections}\n            />\n          </div>\n        )}\n      </ViewRoot>\n    )\n  }\n\n  renderHScrollLayout(\n    headerRowContent: ChunkConfigRowContent,\n    bodyContent: (contentArg: ChunkContentCallbackArgs) => VNode,\n    colCnt: number,\n    dayMinWidth: number,\n  ) {\n    let ScrollGrid = this.context.pluginHooks.scrollGridImpl\n\n    if (!ScrollGrid) {\n      throw new Error('No ScrollGrid implementation')\n    }\n\n    let { props, context } = this\n    let stickyHeaderDates = !props.forPrint && getStickyHeaderDates(context.options)\n    let stickyFooterScrollbar = !props.forPrint && getStickyFooterScrollbar(context.options)\n    let sections: ScrollGridSectionConfig[] = []\n\n    if (headerRowContent) {\n      sections.push({\n        type: 'header',\n        key: 'header',\n        isSticky: stickyHeaderDates,\n        chunks: [{\n          key: 'main',\n          elRef: this.headerElRef,\n          tableClassName: 'fc-col-header',\n          rowContent: headerRowContent,\n        }],\n      })\n    }\n\n    sections.push({\n      type: 'body',\n      key: 'body',\n      liquid: true,\n      chunks: [{\n        key: 'main',\n        content: bodyContent,\n      }],\n    })\n\n    if (stickyFooterScrollbar) {\n      sections.push({\n        type: 'footer',\n        key: 'footer',\n        isSticky: true,\n        chunks: [{\n          key: 'main',\n          content: renderScrollShim,\n        }],\n      })\n    }\n\n    return (\n      <ViewRoot viewSpec={context.viewSpec}>\n        {(rootElRef, classNames) => (\n          <div ref={rootElRef} className={['fc-daygrid'].concat(classNames).join(' ')}>\n            <ScrollGrid\n              liquid={!props.isHeightAuto && !props.forPrint}\n              collapsibleWidth={props.forPrint}\n              colGroups={[{ cols: [{ span: colCnt, minWidth: dayMinWidth }] }]}\n              sections={sections}\n            />\n          </div>\n        )}\n      </ViewRoot>\n    )\n  }\n}\n", "import { EventSegUiInteractionState, Seg } from '@fullcalendar/common'\n\n// this is a DATA STRUCTURE, not a component\n\nexport interface TableSeg extends Seg {\n  row: number\n  firstCol: number\n  lastCol: number\n}\n\nexport function splitSegsByRow(segs: TableSeg[], rowCnt: number) {\n  let byRow: TableSeg[][] = []\n\n  for (let i = 0; i < rowCnt; i += 1) {\n    byRow[i] = []\n  }\n\n  for (let seg of segs) {\n    byRow[seg.row].push(seg)\n  }\n\n  return byRow\n}\n\nexport function splitSegsByFirstCol(segs: TableSeg[], colCnt: number) {\n  let byCol: TableSeg[][] = []\n\n  for (let i = 0; i < colCnt; i += 1) {\n    byCol[i] = []\n  }\n\n  for (let seg of segs) {\n    byCol[seg.firstCol].push(seg)\n  }\n\n  return byCol\n}\n\nexport function splitInteractionByRow(ui: EventSegUiInteractionState | null, rowCnt: number) {\n  let byRow: EventSegUiInteractionState[] = []\n\n  if (!ui) {\n    for (let i = 0; i < rowCnt; i += 1) {\n      byRow[i] = null\n    }\n  } else {\n    for (let i = 0; i < rowCnt; i += 1) {\n      byRow[i] = {\n        affectedInstances: ui.affectedInstances,\n        isEvent: ui.isEvent,\n        segs: [],\n      }\n    }\n\n    for (let seg of ui.segs) {\n      byRow[seg.row].segs.push(seg)\n    }\n  }\n\n  return byRow\n}\n", "import {\n  createElement,\n  Date<PERSON><PERSON><PERSON>,\n  <PERSON><PERSON><PERSON><PERSON>,\n  Day<PERSON><PERSON><PERSON>ontentArg,\n  <PERSON><PERSON><PERSON><PERSON>ontent,\n  BaseComponent,\n  DateProfile,\n  Dictionary,\n  Fragment,\n  buildNavLinkAttrs,\n} from '@fullcalendar/common'\n\ninterface TableCellTopProps {\n  date: DateMarker\n  dateProfile: DateProfile\n  showDayNumber: boolean\n  dayNumberId: string\n  forceDayTop: boolean // hack to force-create an element with height (created by a nbsp)\n  todayRange: DateRange\n  extraHookProps?: Dictionary\n}\n\nexport class TableCellTop extends BaseComponent<TableCellTopProps> {\n  render() {\n    let { props } = this\n    let navLinkAttrs = buildNavLinkAttrs(this.context, props.date)\n\n    return (\n      <DayCellContent\n        date={props.date}\n        dateProfile={props.dateProfile}\n        todayRange={props.todayRange}\n        showDayNumber={props.showDayNumber}\n        extraHookProps={props.extraHookProps}\n        defaultContent={renderTopInner}\n      >\n        {(innerElRef, innerContent) => (\n          (innerContent || props.forceDayTop) && (\n            <div className=\"fc-daygrid-day-top\" ref={innerElRef}>\n              <a\n                id={props.dayNumberId}\n                className=\"fc-daygrid-day-number\"\n                {...navLinkAttrs}\n              >\n                {innerContent || <Fragment>&nbsp;</Fragment>}\n              </a>\n            </div>\n          )\n        )}\n      </DayCellContent>\n    )\n  }\n}\n\nfunction renderTopInner(props: DayCellContentArg) {\n  return props.dayNumberText\n}\n", "import { createFormatter } from '@fullcalendar/common'\nimport { TableSeg } from './TableSeg'\n\nexport const DEFAULT_TABLE_EVENT_TIME_FORMAT = createFormatter({\n  hour: 'numeric',\n  minute: '2-digit',\n  omitZeroMinute: true,\n  meridiem: 'narrow',\n})\n\nexport function hasListItemDisplay(seg: TableSeg) {\n  let { display } = seg.eventRange.ui\n\n  return display === 'list-item' || (\n    display === 'auto' &&\n    !seg.eventRange.def.allDay &&\n    seg.firstCol === seg.lastCol && // can't be multi-day\n    seg.isStart && // \"\n    seg.isEnd // \"\n  )\n}\n", "import { createElement, StandardEvent, BaseComponent, MinimalEventProps } from '@fullcalendar/common'\nimport { DEFAULT_TABLE_EVENT_TIME_FORMAT } from './event-rendering'\n\nexport interface TableBlockEventProps extends MinimalEventProps {\n  defaultDisplayEventEnd: boolean\n}\n\nexport class TableBlockEvent extends BaseComponent<TableBlockEventProps> {\n  render() {\n    let { props } = this\n\n    return (\n      <StandardEvent\n        {...props}\n        extraClassNames={['fc-daygrid-event', 'fc-daygrid-block-event', 'fc-h-event']}\n        defaultTimeFormat={DEFAULT_TABLE_EVENT_TIME_FORMAT}\n        defaultDisplayEventEnd={props.defaultDisplayEventEnd}\n        disableResizing={!props.seg.eventRange.def.allDay}\n      />\n    )\n  }\n}\n", "import {\n  createElement,\n  BaseComponent,\n  Seg,\n  EventRoot,\n  buildSegTimeText,\n  EventContentArg,\n  Fragment,\n  getSegAnchorAttrs,\n} from '@fullcalendar/common'\nimport { DEFAULT_TABLE_EVENT_TIME_FORMAT } from './event-rendering'\n\nexport interface DotTableEventProps {\n  seg: Seg\n  isDragging: boolean\n  isSelected: boolean\n  isPast: boolean\n  isFuture: boolean\n  isToday: boolean\n  defaultDisplayEventEnd: boolean\n}\n\nexport class TableListItemEvent extends BaseComponent<DotTableEventProps> {\n  render() {\n    let { props, context } = this\n    let timeFormat = context.options.eventTimeFormat || DEFAULT_TABLE_EVENT_TIME_FORMAT\n    let timeText = buildSegTimeText(\n      props.seg,\n      timeFormat,\n      context,\n      true,\n      props.defaultDisplayEventEnd,\n    )\n\n    return (\n      <EventRoot\n        seg={props.seg}\n        timeText={timeText}\n        defaultContent={renderInnerContent}\n        isDragging={props.isDragging}\n        isResizing={false}\n        isDateSelecting={false}\n        isSelected={props.isSelected}\n        isPast={props.isPast}\n        isFuture={props.isFuture}\n        isToday={props.isToday}\n      >\n        {(rootElRef, classNames, innerElRef, innerContent) => ( // we don't use styles!\n          <a\n            className={['fc-daygrid-event', 'fc-daygrid-dot-event'].concat(classNames).join(' ')}\n            ref={rootElRef}\n            {...getSegAnchorAttrs(props.seg, context)}\n          >\n            {innerContent}\n          </a>\n        )}\n      </EventRoot>\n    )\n  }\n}\n\nfunction renderInnerContent(innerProps: EventContentArg) {\n  return (\n    <Fragment>\n      <div\n        className=\"fc-daygrid-event-dot\"\n        style={{ borderColor: innerProps.borderColor || innerProps.backgroundColor }}\n      />\n      {innerProps.timeText && (\n        <div className=\"fc-event-time\">{innerProps.timeText}</div>\n      )}\n      <div className=\"fc-event-title\">\n        {innerProps.event.title || <Fragment>&nbsp;</Fragment>}\n      </div>\n    </Fragment>\n  )\n}\n", "import {\n  createElement,\n  MoreLinkRoot,\n  RefObject,\n  BaseComponent,\n  memoize,\n  DateMarker,\n  Dictionary,\n  DatePro<PERSON>le,\n  DateRange,\n  EventSegUiInteractionState,\n  getSegMeta,\n  Fragment,\n  createAriaClickAttrs,\n} from '@fullcalendar/common'\nimport { TableSegPlacement } from './event-placement'\nimport { hasListItemDisplay } from './event-rendering'\nimport { TableBlockEvent } from './TableBlockEvent'\nimport { TableListItemEvent } from './TableListItemEvent'\nimport { TableSeg } from './TableSeg'\n\nexport interface TableCellMoreLinkProps {\n  allDayDate: DateMarker\n  singlePlacements: TableSegPlacement[]\n  moreCnt: number\n  alignmentElRef: RefObject<HTMLElement> // for popover\n  alignGridTop: boolean // for popover\n  extraDateSpan?: Dictionary\n  dateProfile: DateProfile\n  todayRange: DateRange\n  eventSelection: string\n  eventDrag: EventSegUiInteractionState | null\n  eventResize: EventSegUiInteractionState | null\n}\n\nexport class TableCellMoreLink extends BaseComponent<TableCellMoreLinkProps> {\n  compileSegs = memoize(compileSegs)\n\n  render() {\n    let { props } = this\n    let { allSegs, invisibleSegs } = this.compileSegs(props.singlePlacements)\n\n    return (\n      <MoreLinkRoot\n        dateProfile={props.dateProfile}\n        todayRange={props.todayRange}\n        allDayDate={props.allDayDate}\n        moreCnt={props.moreCnt}\n        allSegs={allSegs}\n        hiddenSegs={invisibleSegs}\n        alignmentElRef={props.alignmentElRef}\n        alignGridTop={props.alignGridTop}\n        extraDateSpan={props.extraDateSpan}\n        popoverContent={() => {\n          let isForcedInvisible =\n            (props.eventDrag ? props.eventDrag.affectedInstances : null) ||\n            (props.eventResize ? props.eventResize.affectedInstances : null) ||\n            {}\n          return (\n            <Fragment>\n              {allSegs.map((seg) => {\n                let instanceId = seg.eventRange.instance.instanceId\n                return (\n                  <div\n                    className=\"fc-daygrid-event-harness\"\n                    key={instanceId}\n                    style={{\n                      visibility: isForcedInvisible[instanceId] ? 'hidden' : ('' as any),\n                    }}\n                  >\n                    {hasListItemDisplay(seg) ? (\n                      <TableListItemEvent\n                        seg={seg}\n                        isDragging={false}\n                        isSelected={instanceId === props.eventSelection}\n                        defaultDisplayEventEnd={false}\n                        {...getSegMeta(seg, props.todayRange)}\n                      />\n                    ) : (\n                      <TableBlockEvent\n                        seg={seg}\n                        isDragging={false}\n                        isResizing={false}\n                        isDateSelecting={false}\n                        isSelected={instanceId === props.eventSelection}\n                        defaultDisplayEventEnd={false}\n                        {...getSegMeta(seg, props.todayRange)}\n                      />\n                    )}\n                  </div>\n                )\n              })}\n            </Fragment>\n          )\n        }}\n      >\n        {(rootElRef, classNames, innerElRef, innerContent, handleClick, title, isExpanded, popoverId) => (\n          <a\n            ref={rootElRef}\n            className={['fc-daygrid-more-link'].concat(classNames).join(' ')}\n            title={title}\n            aria-expanded={isExpanded}\n            aria-controls={popoverId}\n            {...createAriaClickAttrs(handleClick)}\n          >\n            {innerContent}\n          </a>\n        )}\n      </MoreLinkRoot>\n    )\n  }\n}\n\nfunction compileSegs(singlePlacements: TableSegPlacement[]): {\n  allSegs: TableSeg[]\n  invisibleSegs: TableSeg[]\n} {\n  let allSegs: TableSeg[] = []\n  let invisibleSegs: TableSeg[] = []\n\n  for (let placement of singlePlacements) {\n    allSegs.push(placement.seg)\n\n    if (!placement.isVisible) {\n      invisibleSegs.push(placement.seg)\n    }\n  }\n\n  return { allSegs, invisibleSegs }\n}\n", "import {\n  Ref,\n  ComponentChildren,\n  createElement,\n  Date<PERSON>arker,\n  DateC<PERSON>ponent,\n  DateR<PERSON><PERSON>,\n  buildNavLinkAttrs,\n  WeekNumberRoot,\n  DayCellRoot,\n  DateProfile,\n  setRef,\n  createFormatter,\n  Dictionary,\n  createRef,\n  EventSegUiInteractionState,\n  getUniqueDomId,\n} from '@fullcalendar/common'\nimport { TableCellTop } from './TableCellTop'\nimport { TableCellMoreLink } from './TableCellMoreLink'\nimport { TableSegPlacement } from './event-placement'\n\nexport interface TableCellProps {\n  date: DateMarker\n  dateProfile: DateProfile\n  extraHookProps?: Dictionary\n  extraDataAttrs?: Dictionary\n  extraClassNames?: string[]\n  extraDateSpan?: Dictionary\n  elRef?: Ref<HTMLTableCellElement>\n  innerElRef?: Ref<HTMLDivElement>\n  bgContent: ComponentChildren\n  fgContentElRef?: Ref<HTMLDivElement> // TODO: rename!!! classname confusion. is the \"event\" div\n  fgContent: ComponentChildren\n  moreCnt: number\n  moreMarginTop: number\n  showDayNumber: boolean\n  showWeekNumber: boolean\n  forceDayTop: boolean\n  todayRange: DateRange\n  eventSelection: string\n  eventDrag: EventSegUiInteractionState | null\n  eventResize: EventSegUiInteractionState | null\n  singlePlacements: TableSegPlacement[]\n}\n\nconst DEFAULT_WEEK_NUM_FORMAT = createFormatter({ week: 'narrow' })\n\nexport class TableCell extends DateComponent<TableCellProps> {\n  private rootElRef = createRef<HTMLElement>()\n  state = {\n    dayNumberId: getUniqueDomId(),\n  }\n\n  render() {\n    let { context, props, state, rootElRef } = this\n    let { date, dateProfile } = props\n    let navLinkAttrs = buildNavLinkAttrs(context, date, 'week')\n\n    return (\n      <DayCellRoot\n        date={date}\n        dateProfile={dateProfile}\n        todayRange={props.todayRange}\n        showDayNumber={props.showDayNumber}\n        extraHookProps={props.extraHookProps}\n        elRef={this.handleRootEl}\n      >\n        {(dayElRef, dayClassNames, rootDataAttrs, isDisabled) => (\n          <td\n            ref={dayElRef}\n            role=\"gridcell\"\n            className={['fc-daygrid-day'].concat(dayClassNames, props.extraClassNames || []).join(' ')}\n            {...rootDataAttrs}\n            {...props.extraDataAttrs}\n            {...(props.showDayNumber ? { 'aria-labelledby': state.dayNumberId } : {})}\n          >\n            <div className=\"fc-daygrid-day-frame fc-scrollgrid-sync-inner\" ref={props.innerElRef /* different from hook system! RENAME */}>\n              {props.showWeekNumber && (\n                <WeekNumberRoot date={date} defaultFormat={DEFAULT_WEEK_NUM_FORMAT}>\n                  {(weekElRef, weekClassNames, innerElRef, innerContent) => (\n                    <a\n                      ref={weekElRef}\n                      className={['fc-daygrid-week-number'].concat(weekClassNames).join(' ')}\n                      {...navLinkAttrs}\n                    >\n                      {innerContent}\n                    </a>\n                  )}\n                </WeekNumberRoot>\n              )}\n              {!isDisabled && (\n                <TableCellTop\n                  date={date}\n                  dateProfile={dateProfile}\n                  showDayNumber={props.showDayNumber}\n                  dayNumberId={state.dayNumberId}\n                  forceDayTop={props.forceDayTop}\n                  todayRange={props.todayRange}\n                  extraHookProps={props.extraHookProps}\n                />\n              )}\n              <div\n                className=\"fc-daygrid-day-events\"\n                ref={props.fgContentElRef}\n              >\n                {props.fgContent}\n                <div className=\"fc-daygrid-day-bottom\" style={{ marginTop: props.moreMarginTop }}>\n                  <TableCellMoreLink\n                    allDayDate={date}\n                    singlePlacements={props.singlePlacements}\n                    moreCnt={props.moreCnt}\n                    alignmentElRef={rootElRef}\n                    alignGridTop={!props.showDayNumber}\n                    extraDateSpan={props.extraDateSpan}\n                    dateProfile={props.dateProfile}\n                    eventSelection={props.eventSelection}\n                    eventDrag={props.eventDrag}\n                    eventResize={props.eventResize}\n                    todayRange={props.todayRange}\n                  />\n                </div>\n              </div>\n              <div className=\"fc-daygrid-day-bg\">\n                {props.bgContent}\n              </div>\n            </div>\n          </td>\n        )}\n      </DayCellRoot>\n    )\n  }\n\n  handleRootEl = (el: HTMLElement) => {\n    setRef(this.rootElRef, el)\n    setRef(this.props.elRef, el)\n  }\n}\n", "import {\n  SegHierarchy,\n  SegRect,\n  Seg<PERSON>ntry,\n  SegInsertion,\n  buildE<PERSON>ry<PERSON>ey,\n  EventRenderRange,\n  intersectRanges,\n  addDays,\n  DayTableCell,\n  intersectSpans,\n} from '@fullcalendar/common'\nimport { TableSeg } from './TableSeg'\n\nexport interface TableSegPlacement {\n  seg: TableSeg\n  isVisible: boolean\n  isAbsolute: boolean\n  absoluteTop: number // populated regardless of isAbsolute\n  marginTop: number\n}\n\nexport function computeFgSegPlacement(\n  segs: TableSeg[], // assumed already sorted\n  dayMaxEvents: boolean | number,\n  dayMaxEventRows: boolean | number,\n  strictOrder: boolean,\n  eventInstanceHeights: { [instanceId: string]: number },\n  maxContentHeight: number | null,\n  cells: DayTableCell[],\n) {\n  let hierarchy = new DayGridSegHierarchy()\n  hierarchy.allowReslicing = true\n  hierarchy.strictOrder = strictOrder\n\n  if (dayMaxEvents === true || dayMaxEventRows === true) {\n    hierarchy.maxCoord = maxContentHeight\n    hierarchy.hiddenConsumes = true\n  } else if (typeof dayMaxEvents === 'number') {\n    hierarchy.maxStackCnt = dayMaxEvents\n  } else if (typeof dayMaxEventRows === 'number') {\n    hierarchy.maxStackCnt = dayMaxEventRows\n    hierarchy.hiddenConsumes = true\n  }\n\n  // create segInputs only for segs with known heights\n  let segInputs: SegEntry[] = []\n  let unknownHeightSegs: TableSeg[] = []\n  for (let i = 0; i < segs.length; i += 1) {\n    let seg = segs[i]\n    let { instanceId } = seg.eventRange.instance\n    let eventHeight = eventInstanceHeights[instanceId]\n\n    if (eventHeight != null) {\n      segInputs.push({\n        index: i,\n        thickness: eventHeight,\n        span: {\n          start: seg.firstCol,\n          end: seg.lastCol + 1,\n        },\n      })\n    } else {\n      unknownHeightSegs.push(seg)\n    }\n  }\n\n  let hiddenEntries = hierarchy.addSegs(segInputs)\n  let segRects = hierarchy.toRects()\n  let { singleColPlacements, multiColPlacements, leftoverMargins } = placeRects(segRects, segs, cells)\n\n  let moreCnts: number[] = []\n  let moreMarginTops: number[] = []\n\n  // add segs with unknown heights\n  for (let seg of unknownHeightSegs) {\n    multiColPlacements[seg.firstCol].push({\n      seg,\n      isVisible: false,\n      isAbsolute: true,\n      absoluteTop: 0,\n      marginTop: 0,\n    })\n\n    for (let col = seg.firstCol; col <= seg.lastCol; col += 1) {\n      singleColPlacements[col].push({\n        seg: resliceSeg(seg, col, col + 1, cells),\n        isVisible: false,\n        isAbsolute: false,\n        absoluteTop: 0,\n        marginTop: 0,\n      })\n    }\n  }\n\n  // add the hidden entries\n  for (let col = 0; col < cells.length; col += 1) {\n    moreCnts.push(0)\n  }\n  for (let hiddenEntry of hiddenEntries) {\n    let seg = segs[hiddenEntry.index]\n    let hiddenSpan = hiddenEntry.span\n\n    multiColPlacements[hiddenSpan.start].push({\n      seg: resliceSeg(seg, hiddenSpan.start, hiddenSpan.end, cells),\n      isVisible: false,\n      isAbsolute: true,\n      absoluteTop: 0,\n      marginTop: 0,\n    })\n\n    for (let col = hiddenSpan.start; col < hiddenSpan.end; col += 1) {\n      moreCnts[col] += 1\n      singleColPlacements[col].push({\n        seg: resliceSeg(seg, col, col + 1, cells),\n        isVisible: false,\n        isAbsolute: false,\n        absoluteTop: 0,\n        marginTop: 0,\n      })\n    }\n  }\n\n  // deal with leftover margins\n  for (let col = 0; col < cells.length; col += 1) {\n    moreMarginTops.push(leftoverMargins[col])\n  }\n\n  return { singleColPlacements, multiColPlacements, moreCnts, moreMarginTops }\n}\n\n// rects ordered by top coord, then left\nfunction placeRects(allRects: SegRect[], segs: TableSeg[], cells: DayTableCell[]) {\n  let rectsByEachCol = groupRectsByEachCol(allRects, cells.length)\n  let singleColPlacements: TableSegPlacement[][] = []\n  let multiColPlacements: TableSegPlacement[][] = []\n  let leftoverMargins: number[] = []\n\n  for (let col = 0; col < cells.length; col += 1) {\n    let rects = rectsByEachCol[col]\n\n    // compute all static segs in singlePlacements\n    let singlePlacements: TableSegPlacement[] = []\n    let currentHeight = 0\n    let currentMarginTop = 0\n    for (let rect of rects) {\n      let seg = segs[rect.index]\n      singlePlacements.push({\n        seg: resliceSeg(seg, col, col + 1, cells),\n        isVisible: true,\n        isAbsolute: false,\n        absoluteTop: rect.levelCoord,\n        marginTop: rect.levelCoord - currentHeight,\n      })\n      currentHeight = rect.levelCoord + rect.thickness\n    }\n\n    // compute mixed static/absolute segs in multiPlacements\n    let multiPlacements: TableSegPlacement[] = []\n    currentHeight = 0\n    currentMarginTop = 0\n    for (let rect of rects) {\n      let seg = segs[rect.index]\n      let isAbsolute = rect.span.end - rect.span.start > 1 // multi-column?\n      let isFirstCol = rect.span.start === col\n\n      currentMarginTop += rect.levelCoord - currentHeight // amount of space since bottom of previous seg\n      currentHeight = rect.levelCoord + rect.thickness // height will now be bottom of current seg\n\n      if (isAbsolute) {\n        currentMarginTop += rect.thickness\n        if (isFirstCol) {\n          multiPlacements.push({\n            seg: resliceSeg(seg, rect.span.start, rect.span.end, cells),\n            isVisible: true,\n            isAbsolute: true,\n            absoluteTop: rect.levelCoord,\n            marginTop: 0,\n          })\n        }\n      } else if (isFirstCol) {\n        multiPlacements.push({\n          seg: resliceSeg(seg, rect.span.start, rect.span.end, cells),\n          isVisible: true,\n          isAbsolute: false,\n          absoluteTop: rect.levelCoord,\n          marginTop: currentMarginTop, // claim the margin\n        })\n        currentMarginTop = 0\n      }\n    }\n\n    singleColPlacements.push(singlePlacements)\n    multiColPlacements.push(multiPlacements)\n    leftoverMargins.push(currentMarginTop)\n  }\n\n  return { singleColPlacements, multiColPlacements, leftoverMargins }\n}\n\nfunction groupRectsByEachCol(rects: SegRect[], colCnt: number): SegRect[][] {\n  let rectsByEachCol: SegRect[][] = []\n\n  for (let col = 0; col < colCnt; col += 1) {\n    rectsByEachCol.push([])\n  }\n\n  for (let rect of rects) {\n    for (let col = rect.span.start; col < rect.span.end; col += 1) {\n      rectsByEachCol[col].push(rect)\n    }\n  }\n\n  return rectsByEachCol\n}\n\nfunction resliceSeg(seg: TableSeg, spanStart: number, spanEnd: number, cells: DayTableCell[]): TableSeg {\n  if (seg.firstCol === spanStart && seg.lastCol === spanEnd - 1) {\n    return seg\n  }\n\n  let eventRange = seg.eventRange\n  let origRange = eventRange.range\n  let slicedRange = intersectRanges(origRange, {\n    start: cells[spanStart].date,\n    end: addDays(cells[spanEnd - 1].date, 1),\n  })\n\n  return {\n    ...seg,\n    firstCol: spanStart,\n    lastCol: spanEnd - 1,\n    eventRange: {\n      def: eventRange.def,\n      ui: { ...eventRange.ui, durationEditable: false }, // hack to disable resizing\n      instance: eventRange.instance,\n      range: slicedRange,\n    } as EventRenderRange,\n    isStart: seg.isStart && slicedRange.start.valueOf() === origRange.start.valueOf(),\n    isEnd: seg.isEnd && slicedRange.end.valueOf() === origRange.end.valueOf(),\n  }\n}\n\nclass DayGridSegHierarchy extends SegHierarchy {\n  // config\n  hiddenConsumes: boolean = false\n\n  // allows us to keep hidden entries in the hierarchy so they take up space\n  forceHidden: { [entryId: string]: true } = {}\n\n  addSegs(segInputs: SegEntry[]): SegEntry[] {\n    const hiddenSegs = super.addSegs(segInputs)\n    const { entriesByLevel } = this\n    const excludeHidden = (entry: SegEntry) => !this.forceHidden[buildEntryKey(entry)]\n\n    // remove the forced-hidden segs\n    for (let level = 0; level < entriesByLevel.length; level += 1) {\n      entriesByLevel[level] = entriesByLevel[level].filter(excludeHidden)\n    }\n\n    return hiddenSegs\n  }\n\n  handleInvalidInsertion(insertion: SegInsertion, entry: SegEntry, hiddenEntries: SegEntry[]) {\n    const { entriesByLevel, forceHidden } = this\n    const { touchingEntry, touchingLevel, touchingLateral } = insertion\n\n    if (this.hiddenConsumes && touchingEntry) {\n      const touchingEntryId = buildEntryKey(touchingEntry)\n      // if not already hidden\n      if (!forceHidden[touchingEntryId]) {\n        if (this.allowReslicing) {\n          const placeholderEntry: SegEntry = { // placeholder of the \"more\" link\n            ...touchingEntry,\n            span: intersectSpans(touchingEntry.span, entry.span),\n          }\n          const placeholderEntryId = buildEntryKey(placeholderEntry)\n          forceHidden[placeholderEntryId] = true\n          entriesByLevel[touchingLevel][touchingLateral] = placeholderEntry // replace touchingEntry with our placeholder\n          this.splitEntry(touchingEntry, entry, hiddenEntries) // split up the touchingEntry, reinsert it\n        } else {\n          forceHidden[touchingEntryId] = true\n          hiddenEntries.push(touchingEntry)\n        }\n      }\n    }\n\n    return super.handleInvalidInsertion(insertion, entry, hiddenEntries)\n  }\n}\n", "import {\n  EventSegUiInteractionState,\n  VNode,\n  DateComponent,\n  createElement,\n  PositionCache,\n  RefMap,\n  CssDimValue,\n  DateRange,\n  getSegMeta,\n  DateProfile,\n  Fragment,\n  BgEvent,\n  renderFill,\n  isPropsEqual,\n  createRef,\n  buildEventRangeKey,\n  sortEventSegs,\n  DayTableCell,\n} from '@fullcalendar/common'\nimport { TableSeg, splitSegsByFirstCol } from './TableSeg'\nimport { TableCell } from './TableCell'\nimport { TableListItemEvent } from './TableListItemEvent'\nimport { TableBlockEvent } from './TableBlockEvent'\nimport { computeFgSegPlacement, TableSegPlacement } from './event-placement'\nimport { hasListItemDisplay } from './event-rendering'\n\n// TODO: attach to window resize?\n\nexport interface TableRowProps {\n  cells: DayTableCell[]\n  renderIntro?: () => VNode\n  businessHourSegs: TableSeg[]\n  bgEventSegs: TableSeg[]\n  fgEventSegs: TableSeg[]\n  dateSelectionSegs: TableSeg[]\n  eventSelection: string\n  eventDrag: EventSegUiInteractionState | null\n  eventResize: EventSegUiInteractionState | null\n  dayMaxEvents: boolean | number\n  dayMaxEventRows: boolean | number\n  clientWidth: number | null\n  clientHeight: number | null // simply for causing an updateSize, for when liquid height\n  dateProfile: DateProfile\n  todayRange: DateRange\n  showDayNumbers: boolean\n  showWeekNumbers: boolean\n  forPrint: boolean\n}\n\ninterface TableRowState {\n  framePositions: PositionCache\n  maxContentHeight: number | null\n  eventInstanceHeights: { [instanceId: string]: number } // integers\n}\n\nexport class TableRow extends DateComponent<TableRowProps, TableRowState> {\n  private cellElRefs = new RefMap<HTMLTableCellElement>() // the <td>\n  private frameElRefs = new RefMap<HTMLElement>() // the fc-daygrid-day-frame\n  private fgElRefs = new RefMap<HTMLDivElement>() // the fc-daygrid-day-events\n  private segHarnessRefs = new RefMap<HTMLDivElement>() // indexed by \"instanceId:firstCol\"\n  private rootElRef = createRef<HTMLTableRowElement>()\n\n  state: TableRowState = {\n    framePositions: null,\n    maxContentHeight: null,\n    eventInstanceHeights: {},\n  }\n\n  render() {\n    let { props, state, context } = this\n    let { options } = context\n    let colCnt = props.cells.length\n\n    let businessHoursByCol = splitSegsByFirstCol(props.businessHourSegs, colCnt)\n    let bgEventSegsByCol = splitSegsByFirstCol(props.bgEventSegs, colCnt)\n    let highlightSegsByCol = splitSegsByFirstCol(this.getHighlightSegs(), colCnt)\n    let mirrorSegsByCol = splitSegsByFirstCol(this.getMirrorSegs(), colCnt)\n\n    let { singleColPlacements, multiColPlacements, moreCnts, moreMarginTops } = computeFgSegPlacement(\n      sortEventSegs(props.fgEventSegs, options.eventOrder) as TableSeg[],\n      props.dayMaxEvents,\n      props.dayMaxEventRows,\n      options.eventOrderStrict,\n      state.eventInstanceHeights,\n      state.maxContentHeight,\n      props.cells,\n    )\n\n    let isForcedInvisible = // TODO: messy way to compute this\n      (props.eventDrag && props.eventDrag.affectedInstances) ||\n      (props.eventResize && props.eventResize.affectedInstances) ||\n      {}\n\n    return (\n      <tr ref={this.rootElRef} role=\"row\">\n        {props.renderIntro && props.renderIntro()}\n        {props.cells.map((cell, col) => {\n          let normalFgNodes = this.renderFgSegs(\n            col,\n            props.forPrint ? singleColPlacements[col] : multiColPlacements[col],\n            props.todayRange,\n            isForcedInvisible,\n          )\n\n          let mirrorFgNodes = this.renderFgSegs(\n            col,\n            buildMirrorPlacements(mirrorSegsByCol[col], multiColPlacements),\n            props.todayRange,\n            {},\n            Boolean(props.eventDrag),\n            Boolean(props.eventResize),\n            false, // date-selecting (because mirror is never drawn for date selection)\n          )\n\n          return (\n            <TableCell\n              key={cell.key}\n              elRef={this.cellElRefs.createRef(cell.key)}\n              innerElRef={this.frameElRefs.createRef(cell.key) /* FF <td> problem, but okay to use for left/right. TODO: rename prop */}\n              dateProfile={props.dateProfile}\n              date={cell.date}\n              showDayNumber={props.showDayNumbers}\n              showWeekNumber={props.showWeekNumbers && col === 0}\n              forceDayTop={props.showWeekNumbers /* even displaying weeknum for row, not necessarily day */}\n              todayRange={props.todayRange}\n              eventSelection={props.eventSelection}\n              eventDrag={props.eventDrag}\n              eventResize={props.eventResize}\n              extraHookProps={cell.extraHookProps}\n              extraDataAttrs={cell.extraDataAttrs}\n              extraClassNames={cell.extraClassNames}\n              extraDateSpan={cell.extraDateSpan}\n              moreCnt={moreCnts[col]}\n              moreMarginTop={moreMarginTops[col]}\n              singlePlacements={singleColPlacements[col]}\n              fgContentElRef={this.fgElRefs.createRef(cell.key)}\n              fgContent={( // Fragment scopes the keys\n                <Fragment>\n                  <Fragment>{normalFgNodes}</Fragment>\n                  <Fragment>{mirrorFgNodes}</Fragment>\n                </Fragment>\n              )}\n              bgContent={( // Fragment scopes the keys\n                <Fragment>\n                  {this.renderFillSegs(highlightSegsByCol[col], 'highlight')}\n                  {this.renderFillSegs(businessHoursByCol[col], 'non-business')}\n                  {this.renderFillSegs(bgEventSegsByCol[col], 'bg-event')}\n                </Fragment>\n              )}\n            />\n          )\n        })}\n      </tr>\n    )\n  }\n\n  componentDidMount() {\n    this.updateSizing(true)\n  }\n\n  componentDidUpdate(prevProps: TableRowProps, prevState: TableRowState) {\n    let currentProps = this.props\n\n    this.updateSizing(\n      !isPropsEqual(prevProps, currentProps),\n    )\n  }\n\n  getHighlightSegs(): TableSeg[] {\n    let { props } = this\n\n    if (props.eventDrag && props.eventDrag.segs.length) { // messy check\n      return props.eventDrag.segs as TableSeg[]\n    }\n\n    if (props.eventResize && props.eventResize.segs.length) { // messy check\n      return props.eventResize.segs as TableSeg[]\n    }\n\n    return props.dateSelectionSegs\n  }\n\n  getMirrorSegs(): TableSeg[] {\n    let { props } = this\n\n    if (props.eventResize && props.eventResize.segs.length) { // messy check\n      return props.eventResize.segs as TableSeg[]\n    }\n\n    return []\n  }\n\n  renderFgSegs(\n    col: number,\n    segPlacements: TableSegPlacement[],\n    todayRange: DateRange,\n    isForcedInvisible: { [instanceId: string]: any },\n    isDragging?: boolean,\n    isResizing?: boolean,\n    isDateSelecting?: boolean,\n  ): VNode[] {\n    let { context } = this\n    let { eventSelection } = this.props\n    let { framePositions } = this.state\n    let defaultDisplayEventEnd = this.props.cells.length === 1 // colCnt === 1\n    let isMirror = isDragging || isResizing || isDateSelecting\n    let nodes: VNode[] = []\n\n    if (framePositions) {\n      for (let placement of segPlacements) {\n        let { seg } = placement\n        let { instanceId } = seg.eventRange.instance\n        let key = instanceId + ':' + col\n        let isVisible = placement.isVisible && !isForcedInvisible[instanceId]\n        let isAbsolute = placement.isAbsolute\n        let left: CssDimValue = ''\n        let right: CssDimValue = ''\n\n        if (isAbsolute) {\n          if (context.isRtl) {\n            right = 0\n            left = framePositions.lefts[seg.lastCol] - framePositions.lefts[seg.firstCol]\n          } else {\n            left = 0\n            right = framePositions.rights[seg.firstCol] - framePositions.rights[seg.lastCol]\n          }\n        }\n\n        /*\n        known bug: events that are force to be list-item but span multiple days still take up space in later columns\n        todo: in print view, for multi-day events, don't display title within non-start/end segs\n        */\n        nodes.push(\n          <div\n            className={'fc-daygrid-event-harness' + (isAbsolute ? ' fc-daygrid-event-harness-abs' : '')}\n            key={key}\n            ref={isMirror ? null : this.segHarnessRefs.createRef(key)}\n            style={{\n              visibility: isVisible ? ('' as any) : 'hidden',\n              marginTop: isAbsolute ? '' : placement.marginTop,\n              top: isAbsolute ? placement.absoluteTop : '',\n              left,\n              right,\n            }}\n          >\n            {hasListItemDisplay(seg) ? (\n              <TableListItemEvent\n                seg={seg}\n                isDragging={isDragging}\n                isSelected={instanceId === eventSelection}\n                defaultDisplayEventEnd={defaultDisplayEventEnd}\n                {...getSegMeta(seg, todayRange)}\n              />\n            ) : (\n              <TableBlockEvent\n                seg={seg}\n                isDragging={isDragging}\n                isResizing={isResizing}\n                isDateSelecting={isDateSelecting}\n                isSelected={instanceId === eventSelection}\n                defaultDisplayEventEnd={defaultDisplayEventEnd}\n                {...getSegMeta(seg, todayRange)}\n              />\n            )}\n          </div>,\n        )\n      }\n    }\n\n    return nodes\n  }\n\n  renderFillSegs(segs: TableSeg[], fillType: string): VNode {\n    let { isRtl } = this.context\n    let { todayRange } = this.props\n    let { framePositions } = this.state\n    let nodes: VNode[] = []\n\n    if (framePositions) {\n      for (let seg of segs) {\n        let leftRightCss = isRtl ? {\n          right: 0,\n          left: framePositions.lefts[seg.lastCol] - framePositions.lefts[seg.firstCol],\n        } : {\n          left: 0,\n          right: framePositions.rights[seg.firstCol] - framePositions.rights[seg.lastCol],\n        }\n\n        nodes.push(\n          <div\n            key={buildEventRangeKey(seg.eventRange)}\n            className=\"fc-daygrid-bg-harness\"\n            style={leftRightCss}\n          >\n            {fillType === 'bg-event' ?\n              <BgEvent seg={seg} {...getSegMeta(seg, todayRange)} /> :\n              renderFill(fillType)}\n          </div>,\n        )\n      }\n    }\n\n    return createElement(Fragment, {}, ...nodes)\n  }\n\n  updateSizing(isExternalSizingChange) {\n    let { props, frameElRefs } = this\n\n    if (\n      !props.forPrint &&\n      props.clientWidth !== null // positioning ready?\n    ) {\n      if (isExternalSizingChange) {\n        let frameEls = props.cells.map((cell) => frameElRefs.currentMap[cell.key])\n\n        if (frameEls.length) {\n          let originEl = this.rootElRef.current\n\n          this.setState({ // will trigger isCellPositionsChanged...\n            framePositions: new PositionCache(\n              originEl,\n              frameEls,\n              true, // isHorizontal\n              false,\n            ),\n          })\n        }\n      }\n\n      const oldInstanceHeights = this.state.eventInstanceHeights\n      const newInstanceHeights = this.queryEventInstanceHeights()\n      const limitByContentHeight = props.dayMaxEvents === true || props.dayMaxEventRows === true\n\n      this.setState({\n        // HACK to prevent oscillations of events being shown/hidden from max-event-rows\n        // Essentially, once you compute an element's height, never null-out.\n        // TODO: always display all events, as visibility:hidden?\n        eventInstanceHeights: { ...oldInstanceHeights, ...newInstanceHeights },\n\n        maxContentHeight: limitByContentHeight ? this.computeMaxContentHeight() : null,\n      })\n    }\n  }\n\n  queryEventInstanceHeights() {\n    let segElMap = this.segHarnessRefs.currentMap\n    let eventInstanceHeights: { [key: string]: number } = {}\n\n    // get the max height amongst instance segs\n    for (let key in segElMap) {\n      let height = Math.round(segElMap[key].getBoundingClientRect().height)\n      let instanceId = key.split(':')[0] // deconstruct how renderFgSegs makes the key\n      eventInstanceHeights[instanceId] = Math.max(eventInstanceHeights[instanceId] || 0, height)\n    }\n\n    return eventInstanceHeights\n  }\n\n  computeMaxContentHeight() {\n    let firstKey = this.props.cells[0].key\n    let cellEl = this.cellElRefs.currentMap[firstKey]\n    let fcContainerEl = this.fgElRefs.currentMap[firstKey]\n\n    return cellEl.getBoundingClientRect().bottom - fcContainerEl.getBoundingClientRect().top\n  }\n\n  public getCellEls() {\n    let elMap = this.cellElRefs.currentMap\n\n    return this.props.cells.map((cell) => elMap[cell.key])\n  }\n}\n\nTableRow.addStateEquality({\n  eventInstanceHeights: isPropsEqual,\n})\n\nfunction buildMirrorPlacements(mirrorSegs: TableSeg[], colPlacements: TableSegPlacement[][]): TableSegPlacement[] {\n  if (!mirrorSegs.length) {\n    return []\n  }\n  let topsByInstanceId = buildAbsoluteTopHash(colPlacements) // TODO: cache this at first render?\n  return mirrorSegs.map((seg: TableSeg) => ({\n    seg,\n    isVisible: true,\n    isAbsolute: true,\n    absoluteTop: topsByInstanceId[seg.eventRange.instance.instanceId],\n    marginTop: 0,\n  }))\n}\n\nfunction buildAbsoluteTopHash(colPlacements: TableSegPlacement[][]): { [instanceId: string]: number } {\n  let topsByInstanceId: { [instanceId: string]: number } = {}\n\n  for (let placements of colPlacements) {\n    for (let placement of placements) {\n      topsByInstanceId[placement.seg.eventRange.instance.instanceId] = placement.absoluteTop\n    }\n  }\n\n  return topsByInstanceId\n}\n", "import {\n  EventSegUiInteractionState,\n  VNode,\n  DateComponent,\n  RefObject,\n  CssDimValue,\n  createElement,\n  PositionCache,\n  memoize,\n  addDays,\n  RefMap,\n  DateRange,\n  NowTimer,\n  DateMarker,\n  DateProfile,\n  Fragment,\n  Hit,\n  DayTableCell,\n} from '@fullcalendar/common'\nimport { TableSeg, splitSegsByRow, splitInteractionByRow } from './TableSeg'\nimport { TableRow } from './TableRow'\n\nexport interface TableProps {\n  dateProfile: DateProfile\n  cells: DayTableCell[][] // cells-BY-ROW\n  renderRowIntro?: () => VNode\n  colGroupNode: VNode\n  tableMinWidth: CssDimValue\n  expandRows: boolean\n  showWeekNumbers: boolean\n  clientWidth: number | null\n  clientHeight: number | null\n  businessHourSegs: TableSeg[]\n  bgEventSegs: TableSeg[]\n  fgEventSegs: TableSeg[]\n  dateSelectionSegs: TableSeg[]\n  eventSelection: string\n  eventDrag: EventSegUiInteractionState | null\n  eventResize: EventSegUiInteractionState | null\n  dayMaxEvents: boolean | number\n  dayMaxEventRows: boolean | number\n  headerAlignElRef?: RefObject<HTMLElement>\n  forPrint: boolean\n  isHitComboAllowed?: (hit0: Hit, hit1: Hit) => boolean\n}\n\nexport class Table extends DateComponent<TableProps> {\n  private splitBusinessHourSegs = memoize(splitSegsByRow)\n  private splitBgEventSegs = memoize(splitSegsByRow)\n  private splitFgEventSegs = memoize(splitSegsByRow)\n  private splitDateSelectionSegs = memoize(splitSegsByRow)\n  private splitEventDrag = memoize(splitInteractionByRow)\n  private splitEventResize = memoize(splitInteractionByRow)\n  private rootEl: HTMLElement\n  private rowRefs = new RefMap<TableRow>()\n  private rowPositions: PositionCache\n  private colPositions: PositionCache\n\n  render() {\n    let { props } = this\n    let { dateProfile, dayMaxEventRows, dayMaxEvents, expandRows } = props\n    let rowCnt = props.cells.length\n\n    let businessHourSegsByRow = this.splitBusinessHourSegs(props.businessHourSegs, rowCnt)\n    let bgEventSegsByRow = this.splitBgEventSegs(props.bgEventSegs, rowCnt)\n    let fgEventSegsByRow = this.splitFgEventSegs(props.fgEventSegs, rowCnt)\n    let dateSelectionSegsByRow = this.splitDateSelectionSegs(props.dateSelectionSegs, rowCnt)\n    let eventDragByRow = this.splitEventDrag(props.eventDrag, rowCnt)\n    let eventResizeByRow = this.splitEventResize(props.eventResize, rowCnt)\n\n    let limitViaBalanced = dayMaxEvents === true || dayMaxEventRows === true\n\n    // if rows can't expand to fill fixed height, can't do balanced-height event limit\n    // TODO: best place to normalize these options?\n    if (limitViaBalanced && !expandRows) {\n      limitViaBalanced = false\n      dayMaxEventRows = null\n      dayMaxEvents = null\n    }\n\n    let classNames = [\n      'fc-daygrid-body',\n      limitViaBalanced ? 'fc-daygrid-body-balanced' : 'fc-daygrid-body-unbalanced', // will all row heights be equal?\n      expandRows ? '' : 'fc-daygrid-body-natural', // will height of one row depend on the others?\n    ]\n\n    return (\n      <div\n        className={classNames.join(' ')}\n        ref={this.handleRootEl}\n        style={{\n          // these props are important to give this wrapper correct dimensions for interactions\n          // TODO: if we set it here, can we avoid giving to inner tables?\n          width: props.clientWidth,\n          minWidth: props.tableMinWidth,\n        }}\n      >\n        <NowTimer unit=\"day\">\n          {(nowDate: DateMarker, todayRange: DateRange) => (\n            <Fragment>\n              <table\n                role=\"presentation\"\n                className=\"fc-scrollgrid-sync-table\"\n                style={{\n                  width: props.clientWidth,\n                  minWidth: props.tableMinWidth,\n                  height: expandRows ? props.clientHeight : '',\n                }}\n              >\n                {props.colGroupNode}\n                <tbody role=\"presentation\">\n                  {props.cells.map((cells, row) => (\n                    <TableRow\n                      ref={this.rowRefs.createRef(row)}\n                      key={\n                        cells.length\n                          ? cells[0].date.toISOString() /* best? or put key on cell? or use diff formatter? */\n                          : row // in case there are no cells (like when resource view is loading)\n                      }\n                      showDayNumbers={rowCnt > 1}\n                      showWeekNumbers={props.showWeekNumbers}\n                      todayRange={todayRange}\n                      dateProfile={dateProfile}\n                      cells={cells}\n                      renderIntro={props.renderRowIntro}\n                      businessHourSegs={businessHourSegsByRow[row]}\n                      eventSelection={props.eventSelection}\n                      bgEventSegs={bgEventSegsByRow[row].filter(isSegAllDay) /* hack */}\n                      fgEventSegs={fgEventSegsByRow[row]}\n                      dateSelectionSegs={dateSelectionSegsByRow[row]}\n                      eventDrag={eventDragByRow[row]}\n                      eventResize={eventResizeByRow[row]}\n                      dayMaxEvents={dayMaxEvents}\n                      dayMaxEventRows={dayMaxEventRows}\n                      clientWidth={props.clientWidth}\n                      clientHeight={props.clientHeight}\n                      forPrint={props.forPrint}\n                    />\n                  ))}\n                </tbody>\n              </table>\n            </Fragment>\n          )}\n        </NowTimer>\n      </div>\n    )\n  }\n\n  handleRootEl = (rootEl: HTMLElement | null) => {\n    this.rootEl = rootEl\n\n    if (rootEl) {\n      this.context.registerInteractiveComponent(this, {\n        el: rootEl,\n        isHitComboAllowed: this.props.isHitComboAllowed,\n      })\n    } else {\n      this.context.unregisterInteractiveComponent(this)\n    }\n  }\n\n  // Hit System\n  // ----------------------------------------------------------------------------------------------------\n\n  prepareHits() {\n    this.rowPositions = new PositionCache(\n      this.rootEl,\n      this.rowRefs.collect().map((rowObj) => rowObj.getCellEls()[0]), // first cell el in each row. TODO: not optimal\n      false,\n      true, // vertical\n    )\n\n    this.colPositions = new PositionCache(\n      this.rootEl,\n      this.rowRefs.currentMap[0].getCellEls(), // cell els in first row\n      true, // horizontal\n      false,\n    )\n  }\n\n  queryHit(positionLeft: number, positionTop: number): Hit {\n    let { colPositions, rowPositions } = this\n    let col = colPositions.leftToIndex(positionLeft)\n    let row = rowPositions.topToIndex(positionTop)\n\n    if (row != null && col != null) {\n      let cell = this.props.cells[row][col]\n\n      return {\n        dateProfile: this.props.dateProfile,\n        dateSpan: {\n          range: this.getCellRange(row, col),\n          allDay: true,\n          ...cell.extraDateSpan,\n        },\n        dayEl: this.getCellEl(row, col),\n        rect: {\n          left: colPositions.lefts[col],\n          right: colPositions.rights[col],\n          top: rowPositions.tops[row],\n          bottom: rowPositions.bottoms[row],\n        },\n        layer: 0,\n      }\n    }\n\n    return null\n  }\n\n  private getCellEl(row, col) {\n    return this.rowRefs.currentMap[row].getCellEls()[col] // TODO: not optimal\n  }\n\n  private getCellRange(row, col) {\n    let start = this.props.cells[row][col].date\n    let end = addDays(start, 1)\n    return { start, end }\n  }\n}\n\nfunction isSegAllDay(seg: TableSeg) {\n  return seg.eventRange.def.allDay\n}\n", "import { DayTableModel, DateRange, Slicer } from '@fullcalendar/common'\nimport { TableSeg } from './TableSeg'\n\nexport class DayTableSlicer extends Slicer<TableSeg, [DayTableModel]> {\n  forceDayIfListItem = true\n\n  sliceRange(dateRange: DateRange, dayTableModel: DayTableModel): TableSeg[] {\n    return dayTableModel.sliceRange(dateRange)\n  }\n}\n", "import {\n  createElement, createRef, VNode,\n  EventStore,\n  EventUiHash,\n  DateSpan,\n  EventInteractionState,\n  DayTableModel,\n  Duration,\n  DateComponent,\n  ViewContext,\n  RefObject,\n  CssDimValue,\n  DateProfile,\n} from '@fullcalendar/common'\nimport { Table } from './Table'\nimport { DayTableSlicer } from './DayTableSlicer'\n\nexport interface DayTableProps {\n  dateProfile: DateProfile,\n  dayTableModel: DayTableModel\n  nextDayThreshold: Duration\n  businessHours: EventStore\n  eventStore: EventStore\n  eventUiBases: EventUiHash\n  dateSelection: DateSpan | null\n  eventSelection: string\n  eventDrag: EventInteractionState | null\n  eventResize: EventInteractionState | null\n  colGroupNode: VNode\n  tableMinWidth: CssDimValue\n  renderRowIntro?: () => VNode\n  dayMaxEvents: boolean | number\n  dayMaxEventRows: boolean | number\n  expandRows: boolean\n  showWeekNumbers: boolean\n  headerAlignElRef?: RefObject<HTMLElement> // for more popover alignment\n  clientWidth: number | null\n  clientHeight: number | null\n  forPrint: boolean\n}\n\nexport class DayTable extends DateComponent<DayTableProps, ViewContext> {\n  private slicer = new DayTableSlicer()\n  private tableRef = createRef<Table>()\n\n  render() {\n    let { props, context } = this\n\n    return (\n      <Table\n        ref={this.tableRef}\n        {...this.slicer.sliceProps(props, props.dateProfile, props.nextDayThreshold, context, props.dayTableModel)}\n        dateProfile={props.dateProfile}\n        cells={props.dayTableModel.cells}\n        colGroupNode={props.colGroupNode}\n        tableMinWidth={props.tableMinWidth}\n        renderRowIntro={props.renderRowIntro}\n        dayMaxEvents={props.dayMaxEvents}\n        dayMaxEventRows={props.dayMaxEventRows}\n        showWeekNumbers={props.showWeekNumbers}\n        expandRows={props.expandRows}\n        headerAlignElRef={props.headerAlignElRef}\n        clientWidth={props.clientWidth}\n        clientHeight={props.clientHeight}\n        forPrint={props.forPrint}\n      />\n    )\n  }\n}\n", "import {\n  createElement, createRef,\n  DayHeader,\n  DateProfileGenerator,\n  DateProfile,\n  memoize,\n  DaySeriesModel,\n  DayTableModel,\n  ChunkContentCallbackArgs,\n} from '@fullcalendar/common'\nimport { TableView } from './TableView'\nimport { DayTable } from './DayTable'\n\nexport class DayTableView extends TableView {\n  private buildDayTableModel = memoize(buildDayTableModel)\n  private headerRef = createRef<DayHeader>()\n  private tableRef = createRef<DayTable>()\n\n  render() {\n    let { options, dateProfileGenerator } = this.context\n    let { props } = this\n    let dayTableModel = this.buildDayTableModel(props.dateProfile, dateProfileGenerator)\n\n    let headerContent = options.dayHeaders && (\n      <DayHeader\n        ref={this.headerRef}\n        dateProfile={props.dateProfile}\n        dates={dayTableModel.headerDates}\n        datesRepDistinctDays={dayTableModel.rowCnt === 1}\n      />\n    )\n\n    let bodyContent = (contentArg: ChunkContentCallbackArgs) => (\n      <DayTable\n        ref={this.tableRef}\n        dateProfile={props.dateProfile}\n        dayTableModel={dayTableModel}\n        businessHours={props.businessHours}\n        dateSelection={props.dateSelection}\n        eventStore={props.eventStore}\n        eventUiBases={props.eventUiBases}\n        eventSelection={props.eventSelection}\n        eventDrag={props.eventDrag}\n        eventResize={props.eventResize}\n        nextDayThreshold={options.nextDayThreshold}\n        colGroupNode={contentArg.tableColGroupNode}\n        tableMinWidth={contentArg.tableMinWidth}\n        dayMaxEvents={options.dayMaxEvents}\n        dayMaxEventRows={options.dayMaxEventRows}\n        showWeekNumbers={options.weekNumbers}\n        expandRows={!props.isHeightAuto}\n        headerAlignElRef={this.headerElRef}\n        clientWidth={contentArg.clientWidth}\n        clientHeight={contentArg.clientHeight}\n        forPrint={props.forPrint}\n      />\n    )\n\n    return options.dayMinWidth\n      ? this.renderHScrollLayout(headerContent, bodyContent, dayTableModel.colCnt, options.dayMinWidth)\n      : this.renderSimpleLayout(headerContent, bodyContent)\n  }\n}\n\nexport function buildDayTableModel(dateProfile: DateProfile, dateProfileGenerator: DateProfileGenerator) {\n  let daySeries = new DaySeriesModel(dateProfile.renderRange, dateProfileGenerator)\n\n  return new DayTableModel(\n    daySeries,\n    /year|month|week/.test(dateProfile.currentRangeUnit),\n  )\n}\n", "import {\n  DateProfileGenerator,\n  addWeeks, diffWeeks,\n  DateRange,\n} from '@fullcalendar/common'\n\nexport class TableDateProfileGenerator extends DateProfileGenerator {\n  // Computes the date range that will be rendered.\n  buildRenderRange(currentRange, currentRangeUnit, isRangeAllDay): DateRange {\n    let { dateEnv } = this.props\n    let renderRange = super.buildRenderRange(currentRange, currentRangeUnit, isRangeAllDay)\n    let start = renderRange.start\n    let end = renderRange.end\n    let endOfWeek\n\n    // year and month views should be aligned with weeks. this is already done for week\n    if (/^(year|month)$/.test(currentRangeUnit)) {\n      start = dateEnv.startOfWeek(start)\n\n      // make end-of-week if not already\n      endOfWeek = dateEnv.startOfWeek(end)\n      if (endOfWeek.valueOf() !== end.valueOf()) {\n        end = addWeeks(endOfWeek, 1)\n      }\n    }\n\n    // ensure 6 weeks\n    if (\n      this.props.monthMode &&\n      this.props.fixedWeekCount\n    ) {\n      let rowCnt = Math.ceil( // could be partial weeks due to hiddenDays\n        diffWeeks(start, end),\n      )\n      end = addWeeks(end, 6 - rowCnt)\n    }\n\n    return { start, end }\n  }\n}\n", "import { createPlugin } from '@fullcalendar/common'\nimport { DayTableView } from './DayTableView'\nimport { TableDateProfileGenerator } from './TableDateProfileGenerator'\nimport './main.css'\n\nexport { DayTable } from './DayTable'\nexport { DayTableSlicer } from './DayTableSlicer'\nexport { Table } from './Table'\nexport { TableSeg } from './TableSeg'\nexport { TableView } from './TableView'\nexport { buildDayTableModel } from './DayTableView'\nexport { DayTableView as DayGridView } // export as old name!\n\nexport default createPlugin({\n  initialView: 'dayGridMonth',\n  views: {\n\n    dayGrid: {\n      component: DayTableView,\n      dateProfileGeneratorClass: TableDateProfileGenerator,\n    },\n\n    dayGridDay: {\n      type: 'dayGrid',\n      duration: { days: 1 },\n    },\n\n    dayGridWeek: {\n      type: 'dayGrid',\n      duration: { weeks: 1 },\n    },\n\n    dayGridMonth: {\n      type: 'dayGrid',\n      duration: { months: 1 },\n      monthMode: true,\n      fixedWeekCount: true,\n    },\n\n  },\n})\n"], "names": [], "mappings": ";;;;;;;;;;AAkBA;;AAEA;AACA;;IAE0D,6BAA+B;IAAzF;QAAA,qEAmHC;QAlHW,iBAAW,GAAoC,SAAS,EAAwB,CAAA;;KAkH3F;IAhHC,sCAAkB,GAAlB,UACE,gBAAuC,EACvC,WAA4D;QAExD,IAAA,KAAqB,IAAI,EAAvB,KAAK,WAAA,EAAE,OAAO,aAAS,CAAA;QAC7B,IAAI,QAAQ,GAA8B,EAAE,CAAA;QAC5C,IAAI,iBAAiB,GAAG,oBAAoB,CAAC,OAAO,CAAC,OAAO,CAAC,CAAA;QAE7D,IAAI,gBAAgB,EAAE;YACpB,QAAQ,CAAC,IAAI,CAAC;gBACZ,IAAI,EAAE,QAAQ;gBACd,GAAG,EAAE,QAAQ;gBACb,QAAQ,EAAE,iBAAiB;gBAC3B,KAAK,EAAE;oBACL,KAAK,EAAE,IAAI,CAAC,WAAW;oBACvB,cAAc,EAAE,eAAe;oBAC/B,UAAU,EAAE,gBAAgB;iBAC7B;aACF,CAAC,CAAA;SACH;QAED,QAAQ,CAAC,IAAI,CAAC;YACZ,IAAI,EAAE,MAAM;YACZ,GAAG,EAAE,MAAM;YACX,MAAM,EAAE,IAAI;YACZ,KAAK,EAAE,EAAE,OAAO,EAAE,WAAW,EAAE;SAChC,CAAC,CAAA;QAEF,QACE,cAAC,QAAQ,IAAC,QAAQ,EAAE,OAAO,CAAC,QAAQ,IACjC,UAAC,SAAS,EAAE,UAAU,IAAK,QAC1B,uBAAK,GAAG,EAAE,SAAS,EAAE,SAAS,EAAE,CAAC,YAAY,CAAC,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC;YACzE,cAAC,gBAAgB,IACf,MAAM,EAAE,CAAC,KAAK,CAAC,YAAY,IAAI,CAAC,KAAK,CAAC,QAAQ,EAC9C,gBAAgB,EAAE,KAAK,CAAC,QAAQ,EAChC,IAAI,EAAE,EAAE,6BACR,QAAQ,EAAE,QAAQ,GAClB,CACE,IACP,CACQ,EACZ;KACF;IAED,uCAAmB,GAAnB,UACE,gBAAuC,EACvC,WAA4D,EAC5D,MAAc,EACd,WAAmB;QAEnB,IAAI,UAAU,GAAG,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC,cAAc,CAAA;QAExD,IAAI,CAAC,UAAU,EAAE;YACf,MAAM,IAAI,KAAK,CAAC,8BAA8B,CAAC,CAAA;SAChD;QAEG,IAAA,KAAqB,IAAI,EAAvB,KAAK,WAAA,EAAE,OAAO,aAAS,CAAA;QAC7B,IAAI,iBAAiB,GAAG,CAAC,KAAK,CAAC,QAAQ,IAAI,oBAAoB,CAAC,OAAO,CAAC,OAAO,CAAC,CAAA;QAChF,IAAI,qBAAqB,GAAG,CAAC,KAAK,CAAC,QAAQ,IAAI,wBAAwB,CAAC,OAAO,CAAC,OAAO,CAAC,CAAA;QACxF,IAAI,QAAQ,GAA8B,EAAE,CAAA;QAE5C,IAAI,gBAAgB,EAAE;YACpB,QAAQ,CAAC,IAAI,CAAC;gBACZ,IAAI,EAAE,QAAQ;gBACd,GAAG,EAAE,QAAQ;gBACb,QAAQ,EAAE,iBAAiB;gBAC3B,MAAM,EAAE,CAAC;wBACP,GAAG,EAAE,MAAM;wBACX,KAAK,EAAE,IAAI,CAAC,WAAW;wBACvB,cAAc,EAAE,eAAe;wBAC/B,UAAU,EAAE,gBAAgB;qBAC7B,CAAC;aACH,CAAC,CAAA;SACH;QAED,QAAQ,CAAC,IAAI,CAAC;YACZ,IAAI,EAAE,MAAM;YACZ,GAAG,EAAE,MAAM;YACX,MAAM,EAAE,IAAI;YACZ,MAAM,EAAE,CAAC;oBACP,GAAG,EAAE,MAAM;oBACX,OAAO,EAAE,WAAW;iBACrB,CAAC;SACH,CAAC,CAAA;QAEF,IAAI,qBAAqB,EAAE;YACzB,QAAQ,CAAC,IAAI,CAAC;gBACZ,IAAI,EAAE,QAAQ;gBACd,GAAG,EAAE,QAAQ;gBACb,QAAQ,EAAE,IAAI;gBACd,MAAM,EAAE,CAAC;wBACP,GAAG,EAAE,MAAM;wBACX,OAAO,EAAE,gBAAgB;qBAC1B,CAAC;aACH,CAAC,CAAA;SACH;QAED,QACE,cAAC,QAAQ,IAAC,QAAQ,EAAE,OAAO,CAAC,QAAQ,IACjC,UAAC,SAAS,EAAE,UAAU,IAAK,QAC1B,uBAAK,GAAG,EAAE,SAAS,EAAE,SAAS,EAAE,CAAC,YAAY,CAAC,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC;YACzE,cAAC,UAAU,IACT,MAAM,EAAE,CAAC,KAAK,CAAC,YAAY,IAAI,CAAC,KAAK,CAAC,QAAQ,EAC9C,gBAAgB,EAAE,KAAK,CAAC,QAAQ,EAChC,SAAS,EAAE,CAAC,EAAE,IAAI,EAAE,CAAC,EAAE,IAAI,EAAE,MAAM,EAAE,QAAQ,EAAE,WAAW,EAAE,CAAC,EAAE,CAAC,EAChE,QAAQ,EAAE,QAAQ,GAClB,CACE,IACP,CACQ,EACZ;KACF;IACH,gBAAC;AAAD,CAnHA,CAA0D,aAAa;;SCbvD,cAAc,CAAC,IAAgB,EAAE,MAAc;IAC7D,IAAI,KAAK,GAAiB,EAAE,CAAA;IAE5B,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,MAAM,EAAE,CAAC,IAAI,CAAC,EAAE;QAClC,KAAK,CAAC,CAAC,CAAC,GAAG,EAAE,CAAA;KACd;IAED,KAAgB,UAAI,EAAJ,aAAI,EAAJ,kBAAI,EAAJ,IAAI,EAAE;QAAjB,IAAI,GAAG,aAAA;QACV,KAAK,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAA;KACzB;IAED,OAAO,KAAK,CAAA;AACd,CAAC;SAEe,mBAAmB,CAAC,IAAgB,EAAE,MAAc;IAClE,IAAI,KAAK,GAAiB,EAAE,CAAA;IAE5B,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,MAAM,EAAE,CAAC,IAAI,CAAC,EAAE;QAClC,KAAK,CAAC,CAAC,CAAC,GAAG,EAAE,CAAA;KACd;IAED,KAAgB,UAAI,EAAJ,aAAI,EAAJ,kBAAI,EAAJ,IAAI,EAAE;QAAjB,IAAI,GAAG,aAAA;QACV,KAAK,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAA;KAC9B;IAED,OAAO,KAAK,CAAA;AACd,CAAC;SAEe,qBAAqB,CAAC,EAAqC,EAAE,MAAc;IACzF,IAAI,KAAK,GAAiC,EAAE,CAAA;IAE5C,IAAI,CAAC,EAAE,EAAE;QACP,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,MAAM,EAAE,CAAC,IAAI,CAAC,EAAE;YAClC,KAAK,CAAC,CAAC,CAAC,GAAG,IAAI,CAAA;SAChB;KACF;SAAM;QACL,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,MAAM,EAAE,CAAC,IAAI,CAAC,EAAE;YAClC,KAAK,CAAC,CAAC,CAAC,GAAG;gBACT,iBAAiB,EAAE,EAAE,CAAC,iBAAiB;gBACvC,OAAO,EAAE,EAAE,CAAC,OAAO;gBACnB,IAAI,EAAE,EAAE;aACT,CAAA;SACF;QAED,KAAgB,UAAO,EAAP,KAAA,EAAE,CAAC,IAAI,EAAP,cAAO,EAAP,IAAO,EAAE;YAApB,IAAI,GAAG,SAAA;YACV,KAAK,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,CAAA;SAC9B;KACF;IAED,OAAO,KAAK,CAAA;AACd;;ACrCA;IAAkC,gCAAgC;IAAlE;;KA8BC;IA7BC,6BAAM,GAAN;QACQ,IAAA,KAAK,GAAK,IAAI,MAAT,CAAS;QACpB,IAAI,YAAY,GAAG,iBAAiB,CAAC,IAAI,CAAC,OAAO,EAAE,KAAK,CAAC,IAAI,CAAC,CAAA;QAE9D,QACE,cAAC,cAAc,IACb,IAAI,EAAE,KAAK,CAAC,IAAI,EAChB,WAAW,EAAE,KAAK,CAAC,WAAW,EAC9B,UAAU,EAAE,KAAK,CAAC,UAAU,EAC5B,aAAa,EAAE,KAAK,CAAC,aAAa,EAClC,cAAc,EAAE,KAAK,CAAC,cAAc,EACpC,cAAc,EAAE,cAAc,IAE7B,UAAC,UAAU,EAAE,YAAY,IAAK,QAC7B,CAAC,YAAY,IAAI,KAAK,CAAC,WAAW,MAChC,uBAAK,SAAS,EAAC,oBAAoB,EAAC,GAAG,EAAE,UAAU;YACjD,8BACE,EAAE,EAAE,KAAK,CAAC,WAAW,EACrB,SAAS,EAAC,uBAAuB,IAC7B,YAAY,GAEf,YAAY,IAAI,cAAC,QAAQ,iBAAkB,CAC1C,CACA,CACP,IACF,CACc,EAClB;KACF;IACH,mBAAC;AAAD,CA9BA,CAAkC,aAAa,GA8B9C;AAED,SAAS,cAAc,CAAC,KAAwB;IAC9C,OAAO,KAAK,CAAC,aAAa,CAAA;AAC5B;;ACtDO,IAAM,+BAA+B,GAAG,eAAe,CAAC;IAC7D,IAAI,EAAE,SAAS;IACf,MAAM,EAAE,SAAS;IACjB,cAAc,EAAE,IAAI;IACpB,QAAQ,EAAE,QAAQ;CACnB,CAAC,CAAA;SAEc,kBAAkB,CAAC,GAAa;IACxC,IAAA,OAAO,GAAK,GAAG,CAAC,UAAU,CAAC,EAAE,QAAtB,CAAsB;IAEnC,OAAO,OAAO,KAAK,WAAW,KAC5B,OAAO,KAAK,MAAM;QAClB,CAAC,GAAG,CAAC,UAAU,CAAC,GAAG,CAAC,MAAM;QAC1B,GAAG,CAAC,QAAQ,KAAK,GAAG,CAAC,OAAO;QAC5B,GAAG,CAAC,OAAO;QACX,GAAG,CAAC,KAAK;KACV,CAAA;AACH;;ACbA;IAAqC,mCAAmC;IAAxE;;KAcC;IAbC,gCAAM,GAAN;QACQ,IAAA,KAAK,GAAK,IAAI,MAAT,CAAS;QAEpB,QACE,cAAC,aAAa,eACR,KAAK,IACT,eAAe,EAAE,CAAC,kBAAkB,EAAE,wBAAwB,EAAE,YAAY,CAAC,EAC7E,iBAAiB,EAAE,+BAA+B,EAClD,sBAAsB,EAAE,KAAK,CAAC,sBAAsB,EACpD,eAAe,EAAE,CAAC,KAAK,CAAC,GAAG,CAAC,UAAU,CAAC,GAAG,CAAC,MAAM,IACjD,EACH;KACF;IACH,sBAAC;AAAD,CAdA,CAAqC,aAAa;;ACelD;IAAwC,sCAAiC;IAAzE;;KAqCC;IApCC,mCAAM,GAAN;QACM,IAAA,KAAqB,IAAI,EAAvB,KAAK,WAAA,EAAE,OAAO,aAAS,CAAA;QAC7B,IAAI,UAAU,GAAG,OAAO,CAAC,OAAO,CAAC,eAAe,IAAI,+BAA+B,CAAA;QACnF,IAAI,QAAQ,GAAG,gBAAgB,CAC7B,KAAK,CAAC,GAAG,EACT,UAAU,EACV,OAAO,EACP,IAAI,EACJ,KAAK,CAAC,sBAAsB,CAC7B,CAAA;QAED,QACE,cAAC,SAAS,IACR,GAAG,EAAE,KAAK,CAAC,GAAG,EACd,QAAQ,EAAE,QAAQ,EAClB,cAAc,EAAE,kBAAkB,EAClC,UAAU,EAAE,KAAK,CAAC,UAAU,EAC5B,UAAU,EAAE,KAAK,EACjB,eAAe,EAAE,KAAK,EACtB,UAAU,EAAE,KAAK,CAAC,UAAU,EAC5B,MAAM,EAAE,KAAK,CAAC,MAAM,EACpB,QAAQ,EAAE,KAAK,CAAC,QAAQ,EACxB,OAAO,EAAE,KAAK,CAAC,OAAO,IAErB,UAAC,SAAS,EAAE,UAAU,EAAE,UAAU,EAAE,YAAY,IAAK;QACpD,8BACE,SAAS,EAAE,CAAC,kBAAkB,EAAE,sBAAsB,CAAC,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,EACpF,GAAG,EAAE,SAAS,IACV,iBAAiB,CAAC,KAAK,CAAC,GAAG,EAAE,OAAO,CAAC,GAExC,YAAY,CACX,IACL,CACS,EACb;KACF;IACH,yBAAC;AAAD,CArCA,CAAwC,aAAa,GAqCpD;AAED,SAAS,kBAAkB,CAAC,UAA2B;IACrD,QACE,cAAC,QAAQ;QACP,uBACE,SAAS,EAAC,sBAAsB,EAChC,KAAK,EAAE,EAAE,WAAW,EAAE,UAAU,CAAC,WAAW,IAAI,UAAU,CAAC,eAAe,EAAE,GAC5E;QACD,UAAU,CAAC,QAAQ,KAClB,uBAAK,SAAS,EAAC,eAAe,IAAE,UAAU,CAAC,QAAQ,CAAO,CAC3D;QACD,uBAAK,SAAS,EAAC,gBAAgB,IAC5B,UAAU,CAAC,KAAK,CAAC,KAAK,IAAI,cAAC,QAAQ,iBAAkB,CAClD,CACG,EACZ;AACH;;ACzCA;IAAuC,qCAAqC;IAA5E;QAAA,qEA4EC;QA3EC,iBAAW,GAAG,OAAO,CAAC,WAAW,CAAC,CAAA;;KA2EnC;IAzEC,kCAAM,GAAN;QACQ,IAAA,KAAK,GAAK,IAAI,MAAT,CAAS;QAChB,IAAA,KAA6B,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC,gBAAgB,CAAC,EAAnE,OAAO,aAAA,EAAE,aAAa,mBAA6C,CAAA;QAEzE,QACE,cAAC,YAAY,IACX,WAAW,EAAE,KAAK,CAAC,WAAW,EAC9B,UAAU,EAAE,KAAK,CAAC,UAAU,EAC5B,UAAU,EAAE,KAAK,CAAC,UAAU,EAC5B,OAAO,EAAE,KAAK,CAAC,OAAO,EACtB,OAAO,EAAE,OAAO,EAChB,UAAU,EAAE,aAAa,EACzB,cAAc,EAAE,KAAK,CAAC,cAAc,EACpC,YAAY,EAAE,KAAK,CAAC,YAAY,EAChC,aAAa,EAAE,KAAK,CAAC,aAAa,EAClC,cAAc,EAAE;gBACd,IAAI,iBAAiB,GACnB,CAAC,KAAK,CAAC,SAAS,GAAG,KAAK,CAAC,SAAS,CAAC,iBAAiB,GAAG,IAAI;qBAC1D,KAAK,CAAC,WAAW,GAAG,KAAK,CAAC,WAAW,CAAC,iBAAiB,GAAG,IAAI,CAAC;oBAChE,EAAE,CAAA;gBACJ,QACE,cAAC,QAAQ,QACN,OAAO,CAAC,GAAG,CAAC,UAAC,GAAG;oBACf,IAAI,UAAU,GAAG,GAAG,CAAC,UAAU,CAAC,QAAQ,CAAC,UAAU,CAAA;oBACnD,QACE,uBACE,SAAS,EAAC,0BAA0B,EACpC,GAAG,EAAE,UAAU,EACf,KAAK,EAAE;4BACL,UAAU,EAAE,iBAAiB,CAAC,UAAU,CAAC,GAAG,QAAQ,GAAI,EAAU;yBACnE,IAEA,kBAAkB,CAAC,GAAG,CAAC,IACtB,cAAC,kBAAkB,aACjB,GAAG,EAAE,GAAG,EACR,UAAU,EAAE,KAAK,EACjB,UAAU,EAAE,UAAU,KAAK,KAAK,CAAC,cAAc,EAC/C,sBAAsB,EAAE,KAAK,IACzB,UAAU,CAAC,GAAG,EAAE,KAAK,CAAC,UAAU,CAAC,EACrC,KAEF,cAAC,eAAe,aACd,GAAG,EAAE,GAAG,EACR,UAAU,EAAE,KAAK,EACjB,UAAU,EAAE,KAAK,EACjB,eAAe,EAAE,KAAK,EACtB,UAAU,EAAE,UAAU,KAAK,KAAK,CAAC,cAAc,EAC/C,sBAAsB,EAAE,KAAK,IACzB,UAAU,CAAC,GAAG,EAAE,KAAK,CAAC,UAAU,CAAC,EACrC,CACH,CACG,EACP;iBACF,CAAC,CACO,EACZ;aACF,IAEA,UAAC,SAAS,EAAE,UAAU,EAAE,UAAU,EAAE,YAAY,EAAE,WAAW,EAAE,KAAK,EAAE,UAAU,EAAE,SAAS,IAAK,QAC/F,8BACE,GAAG,EAAE,SAAS,EACd,SAAS,EAAE,CAAC,sBAAsB,CAAC,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,EAChE,KAAK,EAAE,KAAK,mBACG,UAAU,mBACV,SAAS,IACpB,oBAAoB,CAAC,WAAW,CAAC,GAEpC,YAAY,CACX,IACL,CACY,EAChB;KACF;IACH,wBAAC;AAAD,CA5EA,CAAuC,aAAa,GA4EnD;AAED,SAAS,WAAW,CAAC,gBAAqC;IAIxD,IAAI,OAAO,GAAe,EAAE,CAAA;IAC5B,IAAI,aAAa,GAAe,EAAE,CAAA;IAElC,KAAsB,UAAgB,EAAhB,qCAAgB,EAAhB,8BAAgB,EAAhB,IAAgB,EAAE;QAAnC,IAAI,SAAS,yBAAA;QAChB,OAAO,CAAC,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,CAAA;QAE3B,IAAI,CAAC,SAAS,CAAC,SAAS,EAAE;YACxB,aAAa,CAAC,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,CAAA;SAClC;KACF;IAED,OAAO,EAAE,OAAO,SAAA,EAAE,aAAa,eAAA,EAAE,CAAA;AACnC;;ACnFA,IAAM,uBAAuB,GAAG,eAAe,CAAC,EAAE,IAAI,EAAE,QAAQ,EAAE,CAAC,CAAA;AAEnE;IAA+B,6BAA6B;IAA5D;QAAA,qEAyFC;QAxFS,eAAS,GAAG,SAAS,EAAe,CAAA;QAC5C,WAAK,GAAG;YACN,WAAW,EAAE,cAAc,EAAE;SAC9B,CAAA;QAiFD,kBAAY,GAAG,UAAC,EAAe;YAC7B,MAAM,CAAC,KAAI,CAAC,SAAS,EAAE,EAAE,CAAC,CAAA;YAC1B,MAAM,CAAC,KAAI,CAAC,KAAK,CAAC,KAAK,EAAE,EAAE,CAAC,CAAA;SAC7B,CAAA;;KACF;IAnFC,0BAAM,GAAN;QACM,IAAA,KAAuC,IAAI,EAAzC,OAAO,aAAA,EAAE,KAAK,WAAA,EAAE,KAAK,WAAA,EAAE,SAAS,eAAS,CAAA;QACzC,IAAA,IAAI,GAAkB,KAAK,KAAvB,EAAE,WAAW,GAAK,KAAK,YAAV,CAAU;QACjC,IAAI,YAAY,GAAG,iBAAiB,CAAC,OAAO,EAAE,IAAI,EAAE,MAAM,CAAC,CAAA;QAE3D,QACE,cAAC,WAAW,IACV,IAAI,EAAE,IAAI,EACV,WAAW,EAAE,WAAW,EACxB,UAAU,EAAE,KAAK,CAAC,UAAU,EAC5B,aAAa,EAAE,KAAK,CAAC,aAAa,EAClC,cAAc,EAAE,KAAK,CAAC,cAAc,EACpC,KAAK,EAAE,IAAI,CAAC,YAAY,IAEvB,UAAC,QAAQ,EAAE,aAAa,EAAE,aAAa,EAAE,UAAU,IAAK,QACvD,+BACE,GAAG,EAAE,QAAQ,EACb,IAAI,EAAC,UAAU,EACf,SAAS,EAAE,CAAC,gBAAgB,CAAC,CAAC,MAAM,CAAC,aAAa,EAAE,KAAK,CAAC,eAAe,IAAI,EAAE,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,IACtF,aAAa,EACb,KAAK,CAAC,cAAc,GACnB,KAAK,CAAC,aAAa,GAAG,EAAE,iBAAiB,EAAE,KAAK,CAAC,WAAW,EAAE,GAAG,EAAE;YAExE,uBAAK,SAAS,EAAC,+CAA+C,EAAC,GAAG,EAAE,KAAK,CAAC,UAAU;gBACjF,KAAK,CAAC,cAAc,KACnB,cAAC,cAAc,IAAC,IAAI,EAAE,IAAI,EAAE,aAAa,EAAE,uBAAuB,IAC/D,UAAC,SAAS,EAAE,cAAc,EAAE,UAAU,EAAE,YAAY,IAAK,QACxD,8BACE,GAAG,EAAE,SAAS,EACd,SAAS,EAAE,CAAC,wBAAwB,CAAC,CAAC,MAAM,CAAC,cAAc,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,IAClE,YAAY,GAEf,YAAY,CACX,IACL,CACc,CAClB;gBACA,CAAC,UAAU,KACV,cAAC,YAAY,IACX,IAAI,EAAE,IAAI,EACV,WAAW,EAAE,WAAW,EACxB,aAAa,EAAE,KAAK,CAAC,aAAa,EAClC,WAAW,EAAE,KAAK,CAAC,WAAW,EAC9B,WAAW,EAAE,KAAK,CAAC,WAAW,EAC9B,UAAU,EAAE,KAAK,CAAC,UAAU,EAC5B,cAAc,EAAE,KAAK,CAAC,cAAc,GACpC,CACH;gBACD,uBACE,SAAS,EAAC,uBAAuB,EACjC,GAAG,EAAE,KAAK,CAAC,cAAc;oBAExB,KAAK,CAAC,SAAS;oBAChB,uBAAK,SAAS,EAAC,uBAAuB,EAAC,KAAK,EAAE,EAAE,SAAS,EAAE,KAAK,CAAC,aAAa,EAAE;wBAC9E,cAAC,iBAAiB,IAChB,UAAU,EAAE,IAAI,EAChB,gBAAgB,EAAE,KAAK,CAAC,gBAAgB,EACxC,OAAO,EAAE,KAAK,CAAC,OAAO,EACtB,cAAc,EAAE,SAAS,EACzB,YAAY,EAAE,CAAC,KAAK,CAAC,aAAa,EAClC,aAAa,EAAE,KAAK,CAAC,aAAa,EAClC,WAAW,EAAE,KAAK,CAAC,WAAW,EAC9B,cAAc,EAAE,KAAK,CAAC,cAAc,EACpC,SAAS,EAAE,KAAK,CAAC,SAAS,EAC1B,WAAW,EAAE,KAAK,CAAC,WAAW,EAC9B,UAAU,EAAE,KAAK,CAAC,UAAU,GAC5B,CACE,CACF;gBACN,uBAAK,SAAS,EAAC,mBAAmB,IAC/B,KAAK,CAAC,SAAS,CACZ,CACF,CACH,IACN,CACW,EACf;KACF;IAMH,gBAAC;AAAD,CAzFA,CAA+B,aAAa;;SC1B5B,qBAAqB,CACnC,IAAgB;AAChB,YAA8B,EAC9B,eAAiC,EACjC,WAAoB,EACpB,oBAAsD,EACtD,gBAA+B,EAC/B,KAAqB;IAErB,IAAI,SAAS,GAAG,IAAI,mBAAmB,EAAE,CAAA;IACzC,SAAS,CAAC,cAAc,GAAG,IAAI,CAAA;IAC/B,SAAS,CAAC,WAAW,GAAG,WAAW,CAAA;IAEnC,IAAI,YAAY,KAAK,IAAI,IAAI,eAAe,KAAK,IAAI,EAAE;QACrD,SAAS,CAAC,QAAQ,GAAG,gBAAgB,CAAA;QACrC,SAAS,CAAC,cAAc,GAAG,IAAI,CAAA;KAChC;SAAM,IAAI,OAAO,YAAY,KAAK,QAAQ,EAAE;QAC3C,SAAS,CAAC,WAAW,GAAG,YAAY,CAAA;KACrC;SAAM,IAAI,OAAO,eAAe,KAAK,QAAQ,EAAE;QAC9C,SAAS,CAAC,WAAW,GAAG,eAAe,CAAA;QACvC,SAAS,CAAC,cAAc,GAAG,IAAI,CAAA;KAChC;;IAGD,IAAI,SAAS,GAAe,EAAE,CAAA;IAC9B,IAAI,iBAAiB,GAAe,EAAE,CAAA;IACtC,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC,IAAI,CAAC,EAAE;QACvC,IAAI,GAAG,GAAG,IAAI,CAAC,CAAC,CAAC,CAAA;QACX,IAAA,UAAU,GAAK,GAAG,CAAC,UAAU,CAAC,QAAQ,WAA5B,CAA4B;QAC5C,IAAI,WAAW,GAAG,oBAAoB,CAAC,UAAU,CAAC,CAAA;QAElD,IAAI,WAAW,IAAI,IAAI,EAAE;YACvB,SAAS,CAAC,IAAI,CAAC;gBACb,KAAK,EAAE,CAAC;gBACR,SAAS,EAAE,WAAW;gBACtB,IAAI,EAAE;oBACJ,KAAK,EAAE,GAAG,CAAC,QAAQ;oBACnB,GAAG,EAAE,GAAG,CAAC,OAAO,GAAG,CAAC;iBACrB;aACF,CAAC,CAAA;SACH;aAAM;YACL,iBAAiB,CAAC,IAAI,CAAC,GAAG,CAAC,CAAA;SAC5B;KACF;IAED,IAAI,aAAa,GAAG,SAAS,CAAC,OAAO,CAAC,SAAS,CAAC,CAAA;IAChD,IAAI,QAAQ,GAAG,SAAS,CAAC,OAAO,EAAE,CAAA;IAC9B,IAAA,KAA+D,UAAU,CAAC,QAAQ,EAAE,IAAI,EAAE,KAAK,CAAC,EAA9F,mBAAmB,yBAAA,EAAE,kBAAkB,wBAAA,EAAE,eAAe,qBAAsC,CAAA;IAEpG,IAAI,QAAQ,GAAa,EAAE,CAAA;IAC3B,IAAI,cAAc,GAAa,EAAE,CAAA;;IAGjC,KAAgB,UAAiB,EAAjB,uCAAiB,EAAjB,+BAAiB,EAAjB,IAAiB,EAAE;QAA9B,IAAI,GAAG,0BAAA;QACV,kBAAkB,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC;YACpC,GAAG,KAAA;YACH,SAAS,EAAE,KAAK;YAChB,UAAU,EAAE,IAAI;YAChB,WAAW,EAAE,CAAC;YACd,SAAS,EAAE,CAAC;SACb,CAAC,CAAA;QAEF,KAAK,IAAI,GAAG,GAAG,GAAG,CAAC,QAAQ,EAAE,GAAG,IAAI,GAAG,CAAC,OAAO,EAAE,GAAG,IAAI,CAAC,EAAE;YACzD,mBAAmB,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;gBAC5B,GAAG,EAAE,UAAU,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,GAAG,CAAC,EAAE,KAAK,CAAC;gBACzC,SAAS,EAAE,KAAK;gBAChB,UAAU,EAAE,KAAK;gBACjB,WAAW,EAAE,CAAC;gBACd,SAAS,EAAE,CAAC;aACb,CAAC,CAAA;SACH;KACF;;IAGD,KAAK,IAAI,GAAG,GAAG,CAAC,EAAE,GAAG,GAAG,KAAK,CAAC,MAAM,EAAE,GAAG,IAAI,CAAC,EAAE;QAC9C,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC,CAAA;KACjB;IACD,KAAwB,UAAa,EAAb,+BAAa,EAAb,2BAAa,EAAb,IAAa,EAAE;QAAlC,IAAI,WAAW,sBAAA;QAClB,IAAI,GAAG,GAAG,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC,CAAA;QACjC,IAAI,UAAU,GAAG,WAAW,CAAC,IAAI,CAAA;QAEjC,kBAAkB,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC;YACxC,GAAG,EAAE,UAAU,CAAC,GAAG,EAAE,UAAU,CAAC,KAAK,EAAE,UAAU,CAAC,GAAG,EAAE,KAAK,CAAC;YAC7D,SAAS,EAAE,KAAK;YAChB,UAAU,EAAE,IAAI;YAChB,WAAW,EAAE,CAAC;YACd,SAAS,EAAE,CAAC;SACb,CAAC,CAAA;QAEF,KAAK,IAAI,GAAG,GAAG,UAAU,CAAC,KAAK,EAAE,GAAG,GAAG,UAAU,CAAC,GAAG,EAAE,GAAG,IAAI,CAAC,EAAE;YAC/D,QAAQ,CAAC,GAAG,CAAC,IAAI,CAAC,CAAA;YAClB,mBAAmB,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;gBAC5B,GAAG,EAAE,UAAU,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,GAAG,CAAC,EAAE,KAAK,CAAC;gBACzC,SAAS,EAAE,KAAK;gBAChB,UAAU,EAAE,KAAK;gBACjB,WAAW,EAAE,CAAC;gBACd,SAAS,EAAE,CAAC;aACb,CAAC,CAAA;SACH;KACF;;IAGD,KAAK,IAAI,GAAG,GAAG,CAAC,EAAE,GAAG,GAAG,KAAK,CAAC,MAAM,EAAE,GAAG,IAAI,CAAC,EAAE;QAC9C,cAAc,CAAC,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,CAAC,CAAA;KAC1C;IAED,OAAO,EAAE,mBAAmB,qBAAA,EAAE,kBAAkB,oBAAA,EAAE,QAAQ,UAAA,EAAE,cAAc,gBAAA,EAAE,CAAA;AAC9E,CAAC;AAED;AACA,SAAS,UAAU,CAAC,QAAmB,EAAE,IAAgB,EAAE,KAAqB;IAC9E,IAAI,cAAc,GAAG,mBAAmB,CAAC,QAAQ,EAAE,KAAK,CAAC,MAAM,CAAC,CAAA;IAChE,IAAI,mBAAmB,GAA0B,EAAE,CAAA;IACnD,IAAI,kBAAkB,GAA0B,EAAE,CAAA;IAClD,IAAI,eAAe,GAAa,EAAE,CAAA;IAElC,KAAK,IAAI,GAAG,GAAG,CAAC,EAAE,GAAG,GAAG,KAAK,CAAC,MAAM,EAAE,GAAG,IAAI,CAAC,EAAE;QAC9C,IAAI,KAAK,GAAG,cAAc,CAAC,GAAG,CAAC,CAAA;;QAG/B,IAAI,gBAAgB,GAAwB,EAAE,CAAA;QAC9C,IAAI,aAAa,GAAG,CAAC,CAAA;QACrB,IAAI,gBAAgB,GAAG,CAAC,CAAA;QACxB,KAAiB,UAAK,EAAL,eAAK,EAAL,mBAAK,EAAL,IAAK,EAAE;YAAnB,IAAI,IAAI,cAAA;YACX,IAAI,GAAG,GAAG,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,CAAA;YAC1B,gBAAgB,CAAC,IAAI,CAAC;gBACpB,GAAG,EAAE,UAAU,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,GAAG,CAAC,EAAE,KAAK,CAAC;gBACzC,SAAS,EAAE,IAAI;gBACf,UAAU,EAAE,KAAK;gBACjB,WAAW,EAAE,IAAI,CAAC,UAAU;gBAC5B,SAAS,EAAE,IAAI,CAAC,UAAU,GAAG,aAAa;aAC3C,CAAC,CAAA;YACF,aAAa,GAAG,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,SAAS,CAAA;SACjD;;QAGD,IAAI,eAAe,GAAwB,EAAE,CAAA;QAC7C,aAAa,GAAG,CAAC,CAAA;QACjB,gBAAgB,GAAG,CAAC,CAAA;QACpB,KAAiB,UAAK,EAAL,eAAK,EAAL,mBAAK,EAAL,IAAK,EAAE;YAAnB,IAAI,IAAI,cAAA;YACX,IAAI,GAAG,GAAG,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,CAAA;YAC1B,IAAI,UAAU,GAAG,IAAI,CAAC,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC,IAAI,CAAC,KAAK,GAAG,CAAC,CAAA;YACpD,IAAI,UAAU,GAAG,IAAI,CAAC,IAAI,CAAC,KAAK,KAAK,GAAG,CAAA;YAExC,gBAAgB,IAAI,IAAI,CAAC,UAAU,GAAG,aAAa,CAAA;YACnD,aAAa,GAAG,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,SAAS,CAAA;YAEhD,IAAI,UAAU,EAAE;gBACd,gBAAgB,IAAI,IAAI,CAAC,SAAS,CAAA;gBAClC,IAAI,UAAU,EAAE;oBACd,eAAe,CAAC,IAAI,CAAC;wBACnB,GAAG,EAAE,UAAU,CAAC,GAAG,EAAE,IAAI,CAAC,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE,KAAK,CAAC;wBAC3D,SAAS,EAAE,IAAI;wBACf,UAAU,EAAE,IAAI;wBAChB,WAAW,EAAE,IAAI,CAAC,UAAU;wBAC5B,SAAS,EAAE,CAAC;qBACb,CAAC,CAAA;iBACH;aACF;iBAAM,IAAI,UAAU,EAAE;gBACrB,eAAe,CAAC,IAAI,CAAC;oBACnB,GAAG,EAAE,UAAU,CAAC,GAAG,EAAE,IAAI,CAAC,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE,KAAK,CAAC;oBAC3D,SAAS,EAAE,IAAI;oBACf,UAAU,EAAE,KAAK;oBACjB,WAAW,EAAE,IAAI,CAAC,UAAU;oBAC5B,SAAS,EAAE,gBAAgB;iBAC5B,CAAC,CAAA;gBACF,gBAAgB,GAAG,CAAC,CAAA;aACrB;SACF;QAED,mBAAmB,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAA;QAC1C,kBAAkB,CAAC,IAAI,CAAC,eAAe,CAAC,CAAA;QACxC,eAAe,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAA;KACvC;IAED,OAAO,EAAE,mBAAmB,qBAAA,EAAE,kBAAkB,oBAAA,EAAE,eAAe,iBAAA,EAAE,CAAA;AACrE,CAAC;AAED,SAAS,mBAAmB,CAAC,KAAgB,EAAE,MAAc;IAC3D,IAAI,cAAc,GAAgB,EAAE,CAAA;IAEpC,KAAK,IAAI,GAAG,GAAG,CAAC,EAAE,GAAG,GAAG,MAAM,EAAE,GAAG,IAAI,CAAC,EAAE;QACxC,cAAc,CAAC,IAAI,CAAC,EAAE,CAAC,CAAA;KACxB;IAED,KAAiB,UAAK,EAAL,eAAK,EAAL,mBAAK,EAAL,IAAK,EAAE;QAAnB,IAAI,IAAI,cAAA;QACX,KAAK,IAAI,GAAG,GAAG,IAAI,CAAC,IAAI,CAAC,KAAK,EAAE,GAAG,GAAG,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,IAAI,CAAC,EAAE;YAC7D,cAAc,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;SAC/B;KACF;IAED,OAAO,cAAc,CAAA;AACvB,CAAC;AAED,SAAS,UAAU,CAAC,GAAa,EAAE,SAAiB,EAAE,OAAe,EAAE,KAAqB;IAC1F,IAAI,GAAG,CAAC,QAAQ,KAAK,SAAS,IAAI,GAAG,CAAC,OAAO,KAAK,OAAO,GAAG,CAAC,EAAE;QAC7D,OAAO,GAAG,CAAA;KACX;IAED,IAAI,UAAU,GAAG,GAAG,CAAC,UAAU,CAAA;IAC/B,IAAI,SAAS,GAAG,UAAU,CAAC,KAAK,CAAA;IAChC,IAAI,WAAW,GAAG,eAAe,CAAC,SAAS,EAAE;QAC3C,KAAK,EAAE,KAAK,CAAC,SAAS,CAAC,CAAC,IAAI;QAC5B,GAAG,EAAE,OAAO,CAAC,KAAK,CAAC,OAAO,GAAG,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC,CAAC;KACzC,CAAC,CAAA;IAEF,6BACK,GAAG,KACN,QAAQ,EAAE,SAAS,EACnB,OAAO,EAAE,OAAO,GAAG,CAAC,EACpB,UAAU,EAAE;YACV,GAAG,EAAE,UAAU,CAAC,GAAG;YACnB,EAAE,wBAAO,UAAU,CAAC,EAAE,KAAE,gBAAgB,EAAE,KAAK,GAAE;YACjD,QAAQ,EAAE,UAAU,CAAC,QAAQ;YAC7B,KAAK,EAAE,WAAW;SACC,EACrB,OAAO,EAAE,GAAG,CAAC,OAAO,IAAI,WAAW,CAAC,KAAK,CAAC,OAAO,EAAE,KAAK,SAAS,CAAC,KAAK,CAAC,OAAO,EAAE,EACjF,KAAK,EAAE,GAAG,CAAC,KAAK,IAAI,WAAW,CAAC,GAAG,CAAC,OAAO,EAAE,KAAK,SAAS,CAAC,GAAG,CAAC,OAAO,EAAE,IAC1E;AACH,CAAC;AAED;IAAkC,uCAAY;IAA9C;QAAA,qEA8CC;;QA5CC,oBAAc,GAAY,KAAK,CAAA;;QAG/B,iBAAW,GAAgC,EAAE,CAAA;;KAyC9C;IAvCC,qCAAO,GAAP,UAAQ,SAAqB;QAA7B,iBAWC;QAVC,IAAM,UAAU,GAAG,iBAAM,OAAO,YAAC,SAAS,CAAC,CAAA;QACnC,IAAA,cAAc,GAAK,IAAI,eAAT,CAAS;QAC/B,IAAM,aAAa,GAAG,UAAC,KAAe,IAAK,OAAA,CAAC,KAAI,CAAC,WAAW,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC,GAAA,CAAA;;QAGlF,KAAK,IAAI,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,cAAc,CAAC,MAAM,EAAE,KAAK,IAAI,CAAC,EAAE;YAC7D,cAAc,CAAC,KAAK,CAAC,GAAG,cAAc,CAAC,KAAK,CAAC,CAAC,MAAM,CAAC,aAAa,CAAC,CAAA;SACpE;QAED,OAAO,UAAU,CAAA;KAClB;IAED,oDAAsB,GAAtB,UAAuB,SAAuB,EAAE,KAAe,EAAE,aAAyB;QAClF,IAAA,KAAkC,IAAI,EAApC,cAAc,oBAAA,EAAE,WAAW,iBAAS,CAAA;QACpC,IAAA,aAAa,GAAqC,SAAS,cAA9C,EAAE,aAAa,GAAsB,SAAS,cAA/B,EAAE,eAAe,GAAK,SAAS,gBAAd,CAAc;QAEnE,IAAI,IAAI,CAAC,cAAc,IAAI,aAAa,EAAE;YACxC,IAAM,eAAe,GAAG,aAAa,CAAC,aAAa,CAAC,CAAA;;YAEpD,IAAI,CAAC,WAAW,CAAC,eAAe,CAAC,EAAE;gBACjC,IAAI,IAAI,CAAC,cAAc,EAAE;oBACvB,IAAM,gBAAgB,yBACjB,aAAa,KAChB,IAAI,EAAE,cAAc,CAAC,aAAa,CAAC,IAAI,EAAE,KAAK,CAAC,IAAI,CAAC,GACrD,CAAA;oBACD,IAAM,kBAAkB,GAAG,aAAa,CAAC,gBAAgB,CAAC,CAAA;oBAC1D,WAAW,CAAC,kBAAkB,CAAC,GAAG,IAAI,CAAA;oBACtC,cAAc,CAAC,aAAa,CAAC,CAAC,eAAe,CAAC,GAAG,gBAAgB,CAAA;oBACjE,IAAI,CAAC,UAAU,CAAC,aAAa,EAAE,KAAK,EAAE,aAAa,CAAC,CAAA;iBACrD;qBAAM;oBACL,WAAW,CAAC,eAAe,CAAC,GAAG,IAAI,CAAA;oBACnC,aAAa,CAAC,IAAI,CAAC,aAAa,CAAC,CAAA;iBAClC;aACF;SACF;QAED,OAAO,iBAAM,sBAAsB,YAAC,SAAS,EAAE,KAAK,EAAE,aAAa,CAAC,CAAA;KACrE;IACH,0BAAC;AAAD,CA9CA,CAAkC,YAAY;;AC3L9C;IAA8B,4BAA2C;IAAzE;QAAA,qEA4TC;QA3TS,gBAAU,GAAG,IAAI,MAAM,EAAwB,CAAA;QAC/C,iBAAW,GAAG,IAAI,MAAM,EAAe,CAAA;QACvC,cAAQ,GAAG,IAAI,MAAM,EAAkB,CAAA;QACvC,oBAAc,GAAG,IAAI,MAAM,EAAkB,CAAA;QAC7C,eAAS,GAAG,SAAS,EAAuB,CAAA;QAEpD,WAAK,GAAkB;YACrB,cAAc,EAAE,IAAI;YACpB,gBAAgB,EAAE,IAAI;YACtB,oBAAoB,EAAE,EAAE;SACzB,CAAA;;KAiTF;IA/SC,yBAAM,GAAN;QAAA,iBAsFC;QArFK,IAAA,KAA4B,IAAI,EAA9B,KAAK,WAAA,EAAE,KAAK,WAAA,EAAE,OAAO,aAAS,CAAA;QAC9B,IAAA,OAAO,GAAK,OAAO,QAAZ,CAAY;QACzB,IAAI,MAAM,GAAG,KAAK,CAAC,KAAK,CAAC,MAAM,CAAA;QAE/B,IAAI,kBAAkB,GAAG,mBAAmB,CAAC,KAAK,CAAC,gBAAgB,EAAE,MAAM,CAAC,CAAA;QAC5E,IAAI,gBAAgB,GAAG,mBAAmB,CAAC,KAAK,CAAC,WAAW,EAAE,MAAM,CAAC,CAAA;QACrE,IAAI,kBAAkB,GAAG,mBAAmB,CAAC,IAAI,CAAC,gBAAgB,EAAE,EAAE,MAAM,CAAC,CAAA;QAC7E,IAAI,eAAe,GAAG,mBAAmB,CAAC,IAAI,CAAC,aAAa,EAAE,EAAE,MAAM,CAAC,CAAA;QAEnE,IAAA,KAAwE,qBAAqB,CAC/F,aAAa,CAAC,KAAK,CAAC,WAAW,EAAE,OAAO,CAAC,UAAU,CAAe,EAClE,KAAK,CAAC,YAAY,EAClB,KAAK,CAAC,eAAe,EACrB,OAAO,CAAC,gBAAgB,EACxB,KAAK,CAAC,oBAAoB,EAC1B,KAAK,CAAC,gBAAgB,EACtB,KAAK,CAAC,KAAK,CACZ,EARK,mBAAmB,yBAAA,EAAE,kBAAkB,wBAAA,EAAE,QAAQ,cAAA,EAAE,cAAc,oBAQtE,CAAA;QAED,IAAI,iBAAiB;SACnB,CAAC,KAAK,CAAC,SAAS,IAAI,KAAK,CAAC,SAAS,CAAC,iBAAiB;aACpD,KAAK,CAAC,WAAW,IAAI,KAAK,CAAC,WAAW,CAAC,iBAAiB,CAAC;YAC1D,EAAE,CAAA;QAEJ,QACE,sBAAI,GAAG,EAAE,IAAI,CAAC,SAAS,EAAE,IAAI,EAAC,KAAK;YAChC,KAAK,CAAC,WAAW,IAAI,KAAK,CAAC,WAAW,EAAE;YACxC,KAAK,CAAC,KAAK,CAAC,GAAG,CAAC,UAAC,IAAI,EAAE,GAAG;gBACzB,IAAI,aAAa,GAAG,KAAI,CAAC,YAAY,CACnC,GAAG,EACH,KAAK,CAAC,QAAQ,GAAG,mBAAmB,CAAC,GAAG,CAAC,GAAG,kBAAkB,CAAC,GAAG,CAAC,EACnE,KAAK,CAAC,UAAU,EAChB,iBAAiB,CAClB,CAAA;gBAED,IAAI,aAAa,GAAG,KAAI,CAAC,YAAY,CACnC,GAAG,EACH,qBAAqB,CAAC,eAAe,CAAC,GAAG,CAAC,EAAE,kBAAkB,CAAC,EAC/D,KAAK,CAAC,UAAU,EAChB,EAAE,EACF,OAAO,CAAC,KAAK,CAAC,SAAS,CAAC,EACxB,OAAO,CAAC,KAAK,CAAC,WAAW,CAAC,EAC1B,KAAK,CACN,CAAA;gBAED,QACE,cAAC,SAAS,IACR,GAAG,EAAE,IAAI,CAAC,GAAG,EACb,KAAK,EAAE,KAAI,CAAC,UAAU,CAAC,SAAS,CAAC,IAAI,CAAC,GAAG,CAAC,EAC1C,UAAU,EAAE,KAAI,CAAC,WAAW,CAAC,SAAS,CAAC,IAAI,CAAC,GAAG,CAAC,2EAChD,WAAW,EAAE,KAAK,CAAC,WAAW,EAC9B,IAAI,EAAE,IAAI,CAAC,IAAI,EACf,aAAa,EAAE,KAAK,CAAC,cAAc,EACnC,cAAc,EAAE,KAAK,CAAC,eAAe,IAAI,GAAG,KAAK,CAAC,EAClD,WAAW,EAAE,KAAK,CAAC,eAAe,6DAClC,UAAU,EAAE,KAAK,CAAC,UAAU,EAC5B,cAAc,EAAE,KAAK,CAAC,cAAc,EACpC,SAAS,EAAE,KAAK,CAAC,SAAS,EAC1B,WAAW,EAAE,KAAK,CAAC,WAAW,EAC9B,cAAc,EAAE,IAAI,CAAC,cAAc,EACnC,cAAc,EAAE,IAAI,CAAC,cAAc,EACnC,eAAe,EAAE,IAAI,CAAC,eAAe,EACrC,aAAa,EAAE,IAAI,CAAC,aAAa,EACjC,OAAO,EAAE,QAAQ,CAAC,GAAG,CAAC,EACtB,aAAa,EAAE,cAAc,CAAC,GAAG,CAAC,EAClC,gBAAgB,EAAE,mBAAmB,CAAC,GAAG,CAAC,EAC1C,cAAc,EAAE,KAAI,CAAC,QAAQ,CAAC,SAAS,CAAC,IAAI,CAAC,GAAG,CAAC,EACjD,SAAS;oBACP,cAAC,QAAQ;wBACP,cAAC,QAAQ,QAAE,aAAa,CAAY;wBACpC,cAAC,QAAQ,QAAE,aAAa,CAAY,CAC3B,CACZ,EACD,SAAS;oBACP,cAAC,QAAQ;wBACN,KAAI,CAAC,cAAc,CAAC,kBAAkB,CAAC,GAAG,CAAC,EAAE,WAAW,CAAC;wBACzD,KAAI,CAAC,cAAc,CAAC,kBAAkB,CAAC,GAAG,CAAC,EAAE,cAAc,CAAC;wBAC5D,KAAI,CAAC,cAAc,CAAC,gBAAgB,CAAC,GAAG,CAAC,EAAE,UAAU,CAAC,CAC9C,CACZ,GACD,EACH;aACF,CAAC,CACC,EACN;KACF;IAED,oCAAiB,GAAjB;QACE,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,CAAA;KACxB;IAED,qCAAkB,GAAlB,UAAmB,SAAwB,EAAE,SAAwB;QACnE,IAAI,YAAY,GAAG,IAAI,CAAC,KAAK,CAAA;QAE7B,IAAI,CAAC,YAAY,CACf,CAAC,YAAY,CAAC,SAAS,EAAE,YAAY,CAAC,CACvC,CAAA;KACF;IAED,mCAAgB,GAAhB;QACQ,IAAA,KAAK,GAAK,IAAI,MAAT,CAAS;QAEpB,IAAI,KAAK,CAAC,SAAS,IAAI,KAAK,CAAC,SAAS,CAAC,IAAI,CAAC,MAAM,EAAE;YAClD,OAAO,KAAK,CAAC,SAAS,CAAC,IAAkB,CAAA;SAC1C;QAED,IAAI,KAAK,CAAC,WAAW,IAAI,KAAK,CAAC,WAAW,CAAC,IAAI,CAAC,MAAM,EAAE;YACtD,OAAO,KAAK,CAAC,WAAW,CAAC,IAAkB,CAAA;SAC5C;QAED,OAAO,KAAK,CAAC,iBAAiB,CAAA;KAC/B;IAED,gCAAa,GAAb;QACQ,IAAA,KAAK,GAAK,IAAI,MAAT,CAAS;QAEpB,IAAI,KAAK,CAAC,WAAW,IAAI,KAAK,CAAC,WAAW,CAAC,IAAI,CAAC,MAAM,EAAE;YACtD,OAAO,KAAK,CAAC,WAAW,CAAC,IAAkB,CAAA;SAC5C;QAED,OAAO,EAAE,CAAA;KACV;IAED,+BAAY,GAAZ,UACE,GAAW,EACX,aAAkC,EAClC,UAAqB,EACrB,iBAAgD,EAChD,UAAoB,EACpB,UAAoB,EACpB,eAAyB;QAEnB,IAAA,OAAO,GAAK,IAAI,QAAT,CAAS;QAChB,IAAA,cAAc,GAAK,IAAI,CAAC,KAAK,eAAf,CAAe;QAC7B,IAAA,cAAc,GAAK,IAAI,CAAC,KAAK,eAAf,CAAe;QACnC,IAAI,sBAAsB,GAAG,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,MAAM,KAAK,CAAC,CAAA;QAC1D,IAAI,QAAQ,GAAG,UAAU,IAAI,UAAU,IAAI,eAAe,CAAA;QAC1D,IAAI,KAAK,GAAY,EAAE,CAAA;QAEvB,IAAI,cAAc,EAAE;YAClB,KAAsB,UAAa,EAAb,+BAAa,EAAb,2BAAa,EAAb,IAAa,EAAE;gBAAhC,IAAI,SAAS,sBAAA;gBACV,IAAA,GAAG,GAAK,SAAS,IAAd,CAAc;gBACjB,IAAA,UAAU,GAAK,GAAG,CAAC,UAAU,CAAC,QAAQ,WAA5B,CAA4B;gBAC5C,IAAI,GAAG,GAAG,UAAU,GAAG,GAAG,GAAG,GAAG,CAAA;gBAChC,IAAI,SAAS,GAAG,SAAS,CAAC,SAAS,IAAI,CAAC,iBAAiB,CAAC,UAAU,CAAC,CAAA;gBACrE,IAAI,UAAU,GAAG,SAAS,CAAC,UAAU,CAAA;gBACrC,IAAI,IAAI,GAAgB,EAAE,CAAA;gBAC1B,IAAI,KAAK,GAAgB,EAAE,CAAA;gBAE3B,IAAI,UAAU,EAAE;oBACd,IAAI,OAAO,CAAC,KAAK,EAAE;wBACjB,KAAK,GAAG,CAAC,CAAA;wBACT,IAAI,GAAG,cAAc,CAAC,KAAK,CAAC,GAAG,CAAC,OAAO,CAAC,GAAG,cAAc,CAAC,KAAK,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAA;qBAC9E;yBAAM;wBACL,IAAI,GAAG,CAAC,CAAA;wBACR,KAAK,GAAG,cAAc,CAAC,MAAM,CAAC,GAAG,CAAC,QAAQ,CAAC,GAAG,cAAc,CAAC,MAAM,CAAC,GAAG,CAAC,OAAO,CAAC,CAAA;qBACjF;iBACF;;;;;gBAMD,KAAK,CAAC,IAAI,CACR,uBACE,SAAS,EAAE,0BAA0B,IAAI,UAAU,GAAG,+BAA+B,GAAG,EAAE,CAAC,EAC3F,GAAG,EAAE,GAAG,EACR,GAAG,EAAE,QAAQ,GAAG,IAAI,GAAG,IAAI,CAAC,cAAc,CAAC,SAAS,CAAC,GAAG,CAAC,EACzD,KAAK,EAAE;wBACL,UAAU,EAAE,SAAS,GAAI,EAAU,GAAG,QAAQ;wBAC9C,SAAS,EAAE,UAAU,GAAG,EAAE,GAAG,SAAS,CAAC,SAAS;wBAChD,GAAG,EAAE,UAAU,GAAG,SAAS,CAAC,WAAW,GAAG,EAAE;wBAC5C,IAAI,MAAA;wBACJ,KAAK,OAAA;qBACN,IAEA,kBAAkB,CAAC,GAAG,CAAC,IACtB,cAAC,kBAAkB,aACjB,GAAG,EAAE,GAAG,EACR,UAAU,EAAE,UAAU,EACtB,UAAU,EAAE,UAAU,KAAK,cAAc,EACzC,sBAAsB,EAAE,sBAAsB,IAC1C,UAAU,CAAC,GAAG,EAAE,UAAU,CAAC,EAC/B,KAEF,cAAC,eAAe,aACd,GAAG,EAAE,GAAG,EACR,UAAU,EAAE,UAAU,EACtB,UAAU,EAAE,UAAU,EACtB,eAAe,EAAE,eAAe,EAChC,UAAU,EAAE,UAAU,KAAK,cAAc,EACzC,sBAAsB,EAAE,sBAAsB,IAC1C,UAAU,CAAC,GAAG,EAAE,UAAU,CAAC,EAC/B,CACH,CACG,CACP,CAAA;aACF;SACF;QAED,OAAO,KAAK,CAAA;KACb;IAED,iCAAc,GAAd,UAAe,IAAgB,EAAE,QAAgB;QACzC,IAAA,KAAK,GAAK,IAAI,CAAC,OAAO,MAAjB,CAAiB;QACtB,IAAA,UAAU,GAAK,IAAI,CAAC,KAAK,WAAf,CAAe;QACzB,IAAA,cAAc,GAAK,IAAI,CAAC,KAAK,eAAf,CAAe;QACnC,IAAI,KAAK,GAAY,EAAE,CAAA;QAEvB,IAAI,cAAc,EAAE;YAClB,KAAgB,UAAI,EAAJ,aAAI,EAAJ,kBAAI,EAAJ,IAAI,EAAE;gBAAjB,IAAI,GAAG,aAAA;gBACV,IAAI,YAAY,GAAG,KAAK,GAAG;oBACzB,KAAK,EAAE,CAAC;oBACR,IAAI,EAAE,cAAc,CAAC,KAAK,CAAC,GAAG,CAAC,OAAO,CAAC,GAAG,cAAc,CAAC,KAAK,CAAC,GAAG,CAAC,QAAQ,CAAC;iBAC7E,GAAG;oBACF,IAAI,EAAE,CAAC;oBACP,KAAK,EAAE,cAAc,CAAC,MAAM,CAAC,GAAG,CAAC,QAAQ,CAAC,GAAG,cAAc,CAAC,MAAM,CAAC,GAAG,CAAC,OAAO,CAAC;iBAChF,CAAA;gBAED,KAAK,CAAC,IAAI,CACR,uBACE,GAAG,EAAE,kBAAkB,CAAC,GAAG,CAAC,UAAU,CAAC,EACvC,SAAS,EAAC,uBAAuB,EACjC,KAAK,EAAE,YAAY,IAElB,QAAQ,KAAK,UAAU;oBACtB,cAAC,OAAO,aAAC,GAAG,EAAE,GAAG,IAAM,UAAU,CAAC,GAAG,EAAE,UAAU,CAAC,EAAI;oBACtD,UAAU,CAAC,QAAQ,CAAC,CAClB,CACP,CAAA;aACF;SACF;QAED,OAAO,aAAa,8BAAC,QAAQ,EAAE,EAAE,GAAK,KAAK,GAAC;KAC7C;IAED,+BAAY,GAAZ,UAAa,sBAAsB;QAC7B,IAAA,KAAyB,IAAI,EAA3B,KAAK,WAAA,EAAE,WAAW,iBAAS,CAAA;QAEjC,IACE,CAAC,KAAK,CAAC,QAAQ;YACf,KAAK,CAAC,WAAW,KAAK,IAAI;UAC1B;YACA,IAAI,sBAAsB,EAAE;gBAC1B,IAAI,QAAQ,GAAG,KAAK,CAAC,KAAK,CAAC,GAAG,CAAC,UAAC,IAAI,IAAK,OAAA,WAAW,CAAC,UAAU,CAAC,IAAI,CAAC,GAAG,CAAC,GAAA,CAAC,CAAA;gBAE1E,IAAI,QAAQ,CAAC,MAAM,EAAE;oBACnB,IAAI,QAAQ,GAAG,IAAI,CAAC,SAAS,CAAC,OAAO,CAAA;oBAErC,IAAI,CAAC,QAAQ,CAAC;wBACZ,cAAc,EAAE,IAAI,aAAa,CAC/B,QAAQ,EACR,QAAQ,EACR,IAAI;wBACJ,KAAK,CACN;qBACF,CAAC,CAAA;iBACH;aACF;YAED,IAAM,kBAAkB,GAAG,IAAI,CAAC,KAAK,CAAC,oBAAoB,CAAA;YAC1D,IAAM,kBAAkB,GAAG,IAAI,CAAC,yBAAyB,EAAE,CAAA;YAC3D,IAAM,oBAAoB,GAAG,KAAK,CAAC,YAAY,KAAK,IAAI,IAAI,KAAK,CAAC,eAAe,KAAK,IAAI,CAAA;YAE1F,IAAI,CAAC,QAAQ,CAAC;;;;gBAIZ,oBAAoB,wBAAO,kBAAkB,GAAK,kBAAkB,CAAE;gBAEtE,gBAAgB,EAAE,oBAAoB,GAAG,IAAI,CAAC,uBAAuB,EAAE,GAAG,IAAI;aAC/E,CAAC,CAAA;SACH;KACF;IAED,4CAAyB,GAAzB;QACE,IAAI,QAAQ,GAAG,IAAI,CAAC,cAAc,CAAC,UAAU,CAAA;QAC7C,IAAI,oBAAoB,GAA8B,EAAE,CAAA;;QAGxD,KAAK,IAAI,GAAG,IAAI,QAAQ,EAAE;YACxB,IAAI,MAAM,GAAG,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,qBAAqB,EAAE,CAAC,MAAM,CAAC,CAAA;YACrE,IAAI,UAAU,GAAG,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAA;YAClC,oBAAoB,CAAC,UAAU,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,oBAAoB,CAAC,UAAU,CAAC,IAAI,CAAC,EAAE,MAAM,CAAC,CAAA;SAC3F;QAED,OAAO,oBAAoB,CAAA;KAC5B;IAED,0CAAuB,GAAvB;QACE,IAAI,QAAQ,GAAG,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,GAAG,CAAA;QACtC,IAAI,MAAM,GAAG,IAAI,CAAC,UAAU,CAAC,UAAU,CAAC,QAAQ,CAAC,CAAA;QACjD,IAAI,aAAa,GAAG,IAAI,CAAC,QAAQ,CAAC,UAAU,CAAC,QAAQ,CAAC,CAAA;QAEtD,OAAO,MAAM,CAAC,qBAAqB,EAAE,CAAC,MAAM,GAAG,aAAa,CAAC,qBAAqB,EAAE,CAAC,GAAG,CAAA;KACzF;IAEM,6BAAU,GAAjB;QACE,IAAI,KAAK,GAAG,IAAI,CAAC,UAAU,CAAC,UAAU,CAAA;QAEtC,OAAO,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,GAAG,CAAC,UAAC,IAAI,IAAK,OAAA,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,GAAA,CAAC,CAAA;KACvD;IACH,eAAC;AAAD,CA5TA,CAA8B,aAAa,GA4T1C;AAED,QAAQ,CAAC,gBAAgB,CAAC;IACxB,oBAAoB,EAAE,YAAY;CACnC,CAAC,CAAA;AAEF,SAAS,qBAAqB,CAAC,UAAsB,EAAE,aAAoC;IACzF,IAAI,CAAC,UAAU,CAAC,MAAM,EAAE;QACtB,OAAO,EAAE,CAAA;KACV;IACD,IAAI,gBAAgB,GAAG,oBAAoB,CAAC,aAAa,CAAC,CAAA;IAC1D,OAAO,UAAU,CAAC,GAAG,CAAC,UAAC,GAAa,IAAK,QAAC;QACxC,GAAG,KAAA;QACH,SAAS,EAAE,IAAI;QACf,UAAU,EAAE,IAAI;QAChB,WAAW,EAAE,gBAAgB,CAAC,GAAG,CAAC,UAAU,CAAC,QAAQ,CAAC,UAAU,CAAC;QACjE,SAAS,EAAE,CAAC;KACb,IAAC,CAAC,CAAA;AACL,CAAC;AAED,SAAS,oBAAoB,CAAC,aAAoC;IAChE,IAAI,gBAAgB,GAAqC,EAAE,CAAA;IAE3D,KAAuB,UAAa,EAAb,+BAAa,EAAb,2BAAa,EAAb,IAAa,EAAE;QAAjC,IAAI,UAAU,sBAAA;QACjB,KAAsB,UAAU,EAAV,yBAAU,EAAV,wBAAU,EAAV,IAAU,EAAE;YAA7B,IAAI,SAAS,mBAAA;YAChB,gBAAgB,CAAC,SAAS,CAAC,GAAG,CAAC,UAAU,CAAC,QAAQ,CAAC,UAAU,CAAC,GAAG,SAAS,CAAC,WAAW,CAAA;SACvF;KACF;IAED,OAAO,gBAAgB,CAAA;AACzB;;;ICpW2B,yBAAyB;IAApD;QAAA,qEA4KC;QA3KS,2BAAqB,GAAG,OAAO,CAAC,cAAc,CAAC,CAAA;QAC/C,sBAAgB,GAAG,OAAO,CAAC,cAAc,CAAC,CAAA;QAC1C,sBAAgB,GAAG,OAAO,CAAC,cAAc,CAAC,CAAA;QAC1C,4BAAsB,GAAG,OAAO,CAAC,cAAc,CAAC,CAAA;QAChD,oBAAc,GAAG,OAAO,CAAC,qBAAqB,CAAC,CAAA;QAC/C,sBAAgB,GAAG,OAAO,CAAC,qBAAqB,CAAC,CAAA;QAEjD,aAAO,GAAG,IAAI,MAAM,EAAY,CAAA;QA8FxC,kBAAY,GAAG,UAAC,MAA0B;YACxC,KAAI,CAAC,MAAM,GAAG,MAAM,CAAA;YAEpB,IAAI,MAAM,EAAE;gBACV,KAAI,CAAC,OAAO,CAAC,4BAA4B,CAAC,KAAI,EAAE;oBAC9C,EAAE,EAAE,MAAM;oBACV,iBAAiB,EAAE,KAAI,CAAC,KAAK,CAAC,iBAAiB;iBAChD,CAAC,CAAA;aACH;iBAAM;gBACL,KAAI,CAAC,OAAO,CAAC,8BAA8B,CAAC,KAAI,CAAC,CAAA;aAClD;SACF,CAAA;;KA2DF;IAhKC,sBAAM,GAAN;QAAA,iBAwFC;QAvFO,IAAA,KAAK,GAAK,IAAI,MAAT,CAAS;QACd,IAAA,WAAW,GAAgD,KAAK,YAArD,EAAE,eAAe,GAA+B,KAAK,gBAApC,EAAE,YAAY,GAAiB,KAAK,aAAtB,EAAE,UAAU,GAAK,KAAK,WAAV,CAAU;QACtE,IAAI,MAAM,GAAG,KAAK,CAAC,KAAK,CAAC,MAAM,CAAA;QAE/B,IAAI,qBAAqB,GAAG,IAAI,CAAC,qBAAqB,CAAC,KAAK,CAAC,gBAAgB,EAAE,MAAM,CAAC,CAAA;QACtF,IAAI,gBAAgB,GAAG,IAAI,CAAC,gBAAgB,CAAC,KAAK,CAAC,WAAW,EAAE,MAAM,CAAC,CAAA;QACvE,IAAI,gBAAgB,GAAG,IAAI,CAAC,gBAAgB,CAAC,KAAK,CAAC,WAAW,EAAE,MAAM,CAAC,CAAA;QACvE,IAAI,sBAAsB,GAAG,IAAI,CAAC,sBAAsB,CAAC,KAAK,CAAC,iBAAiB,EAAE,MAAM,CAAC,CAAA;QACzF,IAAI,cAAc,GAAG,IAAI,CAAC,cAAc,CAAC,KAAK,CAAC,SAAS,EAAE,MAAM,CAAC,CAAA;QACjE,IAAI,gBAAgB,GAAG,IAAI,CAAC,gBAAgB,CAAC,KAAK,CAAC,WAAW,EAAE,MAAM,CAAC,CAAA;QAEvE,IAAI,gBAAgB,GAAG,YAAY,KAAK,IAAI,IAAI,eAAe,KAAK,IAAI,CAAA;;;QAIxE,IAAI,gBAAgB,IAAI,CAAC,UAAU,EAAE;YACnC,gBAAgB,GAAG,KAAK,CAAA;YACxB,eAAe,GAAG,IAAI,CAAA;YACtB,YAAY,GAAG,IAAI,CAAA;SACpB;QAED,IAAI,UAAU,GAAG;YACf,iBAAiB;YACjB,gBAAgB,GAAG,0BAA0B,GAAG,4BAA4B;YAC5E,UAAU,GAAG,EAAE,GAAG,yBAAyB;SAC5C,CAAA;QAED,QACE,uBACE,SAAS,EAAE,UAAU,CAAC,IAAI,CAAC,GAAG,CAAC,EAC/B,GAAG,EAAE,IAAI,CAAC,YAAY,EACtB,KAAK,EAAE;;;gBAGL,KAAK,EAAE,KAAK,CAAC,WAAW;gBACxB,QAAQ,EAAE,KAAK,CAAC,aAAa;aAC9B;YAED,cAAC,QAAQ,IAAC,IAAI,EAAC,KAAK,IACjB,UAAC,OAAmB,EAAE,UAAqB,IAAK,QAC/C,cAAC,QAAQ;gBACP,yBACE,IAAI,EAAC,cAAc,EACnB,SAAS,EAAC,0BAA0B,EACpC,KAAK,EAAE;wBACL,KAAK,EAAE,KAAK,CAAC,WAAW;wBACxB,QAAQ,EAAE,KAAK,CAAC,aAAa;wBAC7B,MAAM,EAAE,UAAU,GAAG,KAAK,CAAC,YAAY,GAAG,EAAE;qBAC7C;oBAEA,KAAK,CAAC,YAAY;oBACnB,yBAAO,IAAI,EAAC,cAAc,IACvB,KAAK,CAAC,KAAK,CAAC,GAAG,CAAC,UAAC,KAAK,EAAE,GAAG,IAAK,QAC/B,cAAC,QAAQ,IACP,GAAG,EAAE,KAAI,CAAC,OAAO,CAAC,SAAS,CAAC,GAAG,CAAC,EAChC,GAAG,EACD,KAAK,CAAC,MAAM;8BACR,KAAK,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,WAAW,EAAE;8BAC3B,GAAG;0BAET,cAAc,EAAE,MAAM,GAAG,CAAC,EAC1B,eAAe,EAAE,KAAK,CAAC,eAAe,EACtC,UAAU,EAAE,UAAU,EACtB,WAAW,EAAE,WAAW,EACxB,KAAK,EAAE,KAAK,EACZ,WAAW,EAAE,KAAK,CAAC,cAAc,EACjC,gBAAgB,EAAE,qBAAqB,CAAC,GAAG,CAAC,EAC5C,cAAc,EAAE,KAAK,CAAC,cAAc,EACpC,WAAW,EAAE,gBAAgB,CAAC,GAAG,CAAC,CAAC,MAAM,CAAC,WAAW,CAAC,aACtD,WAAW,EAAE,gBAAgB,CAAC,GAAG,CAAC,EAClC,iBAAiB,EAAE,sBAAsB,CAAC,GAAG,CAAC,EAC9C,SAAS,EAAE,cAAc,CAAC,GAAG,CAAC,EAC9B,WAAW,EAAE,gBAAgB,CAAC,GAAG,CAAC,EAClC,YAAY,EAAE,YAAY,EAC1B,eAAe,EAAE,eAAe,EAChC,WAAW,EAAE,KAAK,CAAC,WAAW,EAC9B,YAAY,EAAE,KAAK,CAAC,YAAY,EAChC,QAAQ,EAAE,KAAK,CAAC,QAAQ,GACxB,IACH,CAAC,CACI,CACF,CACC,IACZ,CACQ,CACP,EACP;KACF;;;IAkBD,2BAAW,GAAX;QACE,IAAI,CAAC,YAAY,GAAG,IAAI,aAAa,CACnC,IAAI,CAAC,MAAM,EACX,IAAI,CAAC,OAAO,CAAC,OAAO,EAAE,CAAC,GAAG,CAAC,UAAC,MAAM,IAAK,OAAA,MAAM,CAAC,UAAU,EAAE,CAAC,CAAC,CAAC,GAAA,CAAC;QAC9D,KAAK,EACL,IAAI,CACL,CAAA;QAED,IAAI,CAAC,YAAY,GAAG,IAAI,aAAa,CACnC,IAAI,CAAC,MAAM,EACX,IAAI,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,UAAU,EAAE;QACvC,IAAI;QACJ,KAAK,CACN,CAAA;KACF;IAED,wBAAQ,GAAR,UAAS,YAAoB,EAAE,WAAmB;QAC5C,IAAA,KAAiC,IAAI,EAAnC,YAAY,kBAAA,EAAE,YAAY,kBAAS,CAAA;QACzC,IAAI,GAAG,GAAG,YAAY,CAAC,WAAW,CAAC,YAAY,CAAC,CAAA;QAChD,IAAI,GAAG,GAAG,YAAY,CAAC,UAAU,CAAC,WAAW,CAAC,CAAA;QAE9C,IAAI,GAAG,IAAI,IAAI,IAAI,GAAG,IAAI,IAAI,EAAE;YAC9B,IAAI,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAA;YAErC,OAAO;gBACL,WAAW,EAAE,IAAI,CAAC,KAAK,CAAC,WAAW;gBACnC,QAAQ,aACN,KAAK,EAAE,IAAI,CAAC,YAAY,CAAC,GAAG,EAAE,GAAG,CAAC,EAClC,MAAM,EAAE,IAAI,IACT,IAAI,CAAC,aAAa,CACtB;gBACD,KAAK,EAAE,IAAI,CAAC,SAAS,CAAC,GAAG,EAAE,GAAG,CAAC;gBAC/B,IAAI,EAAE;oBACJ,IAAI,EAAE,YAAY,CAAC,KAAK,CAAC,GAAG,CAAC;oBAC7B,KAAK,EAAE,YAAY,CAAC,MAAM,CAAC,GAAG,CAAC;oBAC/B,GAAG,EAAE,YAAY,CAAC,IAAI,CAAC,GAAG,CAAC;oBAC3B,MAAM,EAAE,YAAY,CAAC,OAAO,CAAC,GAAG,CAAC;iBAClC;gBACD,KAAK,EAAE,CAAC;aACT,CAAA;SACF;QAED,OAAO,IAAI,CAAA;KACZ;IAEO,yBAAS,GAAjB,UAAkB,GAAG,EAAE,GAAG;QACxB,OAAO,IAAI,CAAC,OAAO,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC,UAAU,EAAE,CAAC,GAAG,CAAC,CAAA;KACtD;IAEO,4BAAY,GAApB,UAAqB,GAAG,EAAE,GAAG;QAC3B,IAAI,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,IAAI,CAAA;QAC3C,IAAI,GAAG,GAAG,OAAO,CAAC,KAAK,EAAE,CAAC,CAAC,CAAA;QAC3B,OAAO,EAAE,KAAK,OAAA,EAAE,GAAG,KAAA,EAAE,CAAA;KACtB;IACH,YAAC;AAAD,CA5KA,CAA2B,aAAa,GA4KvC;AAED,SAAS,WAAW,CAAC,GAAa;IAChC,OAAO,GAAG,CAAC,UAAU,CAAC,GAAG,CAAC,MAAM,CAAA;AAClC;;;IC3NoC,kCAAiC;IAArE;QAAA,qEAMC;QALC,wBAAkB,GAAG,IAAI,CAAA;;KAK1B;IAHC,mCAAU,GAAV,UAAW,SAAoB,EAAE,aAA4B;QAC3D,OAAO,aAAa,CAAC,UAAU,CAAC,SAAS,CAAC,CAAA;KAC3C;IACH,qBAAC;AAAD,CANA,CAAoC,MAAM;;;ICsCZ,4BAAyC;IAAvE;QAAA,qEA2BC;QA1BS,YAAM,GAAG,IAAI,cAAc,EAAE,CAAA;QAC7B,cAAQ,GAAG,SAAS,EAAS,CAAA;;KAyBtC;IAvBC,yBAAM,GAAN;QACM,IAAA,KAAqB,IAAI,EAAvB,KAAK,WAAA,EAAE,OAAO,aAAS,CAAA;QAE7B,QACE,cAAC,KAAK,aACJ,GAAG,EAAE,IAAI,CAAC,QAAQ,IACd,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC,KAAK,EAAE,KAAK,CAAC,WAAW,EAAE,KAAK,CAAC,gBAAgB,EAAE,OAAO,EAAE,KAAK,CAAC,aAAa,CAAC,IAC1G,WAAW,EAAE,KAAK,CAAC,WAAW,EAC9B,KAAK,EAAE,KAAK,CAAC,aAAa,CAAC,KAAK,EAChC,YAAY,EAAE,KAAK,CAAC,YAAY,EAChC,aAAa,EAAE,KAAK,CAAC,aAAa,EAClC,cAAc,EAAE,KAAK,CAAC,cAAc,EACpC,YAAY,EAAE,KAAK,CAAC,YAAY,EAChC,eAAe,EAAE,KAAK,CAAC,eAAe,EACtC,eAAe,EAAE,KAAK,CAAC,eAAe,EACtC,UAAU,EAAE,KAAK,CAAC,UAAU,EAC5B,gBAAgB,EAAE,KAAK,CAAC,gBAAgB,EACxC,WAAW,EAAE,KAAK,CAAC,WAAW,EAC9B,YAAY,EAAE,KAAK,CAAC,YAAY,EAChC,QAAQ,EAAE,KAAK,CAAC,QAAQ,IACxB,EACH;KACF;IACH,eAAC;AAAD,CA3BA,CAA8B,aAAa;;;IC5BT,gCAAS;IAA3C;QAAA,qEAiDC;QAhDS,wBAAkB,GAAG,OAAO,CAAC,kBAAkB,CAAC,CAAA;QAChD,eAAS,GAAG,SAAS,EAAa,CAAA;QAClC,cAAQ,GAAG,SAAS,EAAY,CAAA;;KA8CzC;IA5CC,6BAAM,GAAN;QAAA,iBA2CC;QA1CK,IAAA,KAAoC,IAAI,CAAC,OAAO,EAA9C,OAAO,aAAA,EAAE,oBAAoB,0BAAiB,CAAA;QAC9C,IAAA,KAAK,GAAK,IAAI,MAAT,CAAS;QACpB,IAAI,aAAa,GAAG,IAAI,CAAC,kBAAkB,CAAC,KAAK,CAAC,WAAW,EAAE,oBAAoB,CAAC,CAAA;QAEpF,IAAI,aAAa,GAAG,OAAO,CAAC,UAAU,KACpC,cAAC,SAAS,IACR,GAAG,EAAE,IAAI,CAAC,SAAS,EACnB,WAAW,EAAE,KAAK,CAAC,WAAW,EAC9B,KAAK,EAAE,aAAa,CAAC,WAAW,EAChC,oBAAoB,EAAE,aAAa,CAAC,MAAM,KAAK,CAAC,GAChD,CACH,CAAA;QAED,IAAI,WAAW,GAAG,UAAC,UAAoC,IAAK,QAC1D,cAAC,QAAQ,IACP,GAAG,EAAE,KAAI,CAAC,QAAQ,EAClB,WAAW,EAAE,KAAK,CAAC,WAAW,EAC9B,aAAa,EAAE,aAAa,EAC5B,aAAa,EAAE,KAAK,CAAC,aAAa,EAClC,aAAa,EAAE,KAAK,CAAC,aAAa,EAClC,UAAU,EAAE,KAAK,CAAC,UAAU,EAC5B,YAAY,EAAE,KAAK,CAAC,YAAY,EAChC,cAAc,EAAE,KAAK,CAAC,cAAc,EACpC,SAAS,EAAE,KAAK,CAAC,SAAS,EAC1B,WAAW,EAAE,KAAK,CAAC,WAAW,EAC9B,gBAAgB,EAAE,OAAO,CAAC,gBAAgB,EAC1C,YAAY,EAAE,UAAU,CAAC,iBAAiB,EAC1C,aAAa,EAAE,UAAU,CAAC,aAAa,EACvC,YAAY,EAAE,OAAO,CAAC,YAAY,EAClC,eAAe,EAAE,OAAO,CAAC,eAAe,EACxC,eAAe,EAAE,OAAO,CAAC,WAAW,EACpC,UAAU,EAAE,CAAC,KAAK,CAAC,YAAY,EAC/B,gBAAgB,EAAE,KAAI,CAAC,WAAW,EAClC,WAAW,EAAE,UAAU,CAAC,WAAW,EACnC,YAAY,EAAE,UAAU,CAAC,YAAY,EACrC,QAAQ,EAAE,KAAK,CAAC,QAAQ,GACxB,IACH,CAAA;QAED,OAAO,OAAO,CAAC,WAAW;cACtB,IAAI,CAAC,mBAAmB,CAAC,aAAa,EAAE,WAAW,EAAE,aAAa,CAAC,MAAM,EAAE,OAAO,CAAC,WAAW,CAAC;cAC/F,IAAI,CAAC,kBAAkB,CAAC,aAAa,EAAE,WAAW,CAAC,CAAA;KACxD;IACH,mBAAC;AAAD,CAjDA,CAAkC,SAAS,GAiD1C;SAEe,kBAAkB,CAAC,WAAwB,EAAE,oBAA0C;IACrG,IAAI,SAAS,GAAG,IAAI,cAAc,CAAC,WAAW,CAAC,WAAW,EAAE,oBAAoB,CAAC,CAAA;IAEjF,OAAO,IAAI,aAAa,CACtB,SAAS,EACT,iBAAiB,CAAC,IAAI,CAAC,WAAW,CAAC,gBAAgB,CAAC,CACrD,CAAA;AACH;;ACjEA;IAA+C,6CAAoB;IAAnE;;KAiCC;;IA/BC,oDAAgB,GAAhB,UAAiB,YAAY,EAAE,gBAAgB,EAAE,aAAa;QACtD,IAAA,OAAO,GAAK,IAAI,CAAC,KAAK,QAAf,CAAe;QAC5B,IAAI,WAAW,GAAG,iBAAM,gBAAgB,YAAC,YAAY,EAAE,gBAAgB,EAAE,aAAa,CAAC,CAAA;QACvF,IAAI,KAAK,GAAG,WAAW,CAAC,KAAK,CAAA;QAC7B,IAAI,GAAG,GAAG,WAAW,CAAC,GAAG,CAAA;QACzB,IAAI,SAAS,CAAA;;QAGb,IAAI,gBAAgB,CAAC,IAAI,CAAC,gBAAgB,CAAC,EAAE;YAC3C,KAAK,GAAG,OAAO,CAAC,WAAW,CAAC,KAAK,CAAC,CAAA;;YAGlC,SAAS,GAAG,OAAO,CAAC,WAAW,CAAC,GAAG,CAAC,CAAA;YACpC,IAAI,SAAS,CAAC,OAAO,EAAE,KAAK,GAAG,CAAC,OAAO,EAAE,EAAE;gBACzC,GAAG,GAAG,QAAQ,CAAC,SAAS,EAAE,CAAC,CAAC,CAAA;aAC7B;SACF;;QAGD,IACE,IAAI,CAAC,KAAK,CAAC,SAAS;YACpB,IAAI,CAAC,KAAK,CAAC,cAAc,EACzB;YACA,IAAI,MAAM,GAAG,IAAI,CAAC,IAAI;YACpB,SAAS,CAAC,KAAK,EAAE,GAAG,CAAC,CACtB,CAAA;YACD,GAAG,GAAG,QAAQ,CAAC,GAAG,EAAE,CAAC,GAAG,MAAM,CAAC,CAAA;SAChC;QAED,OAAO,EAAE,KAAK,OAAA,EAAE,GAAG,KAAA,EAAE,CAAA;KACtB;IACH,gCAAC;AAAD,CAjCA,CAA+C,oBAAoB;;ACOnE,WAAe,YAAY,CAAC;IAC1B,WAAW,EAAE,cAAc;IAC3B,KAAK,EAAE;QAEL,OAAO,EAAE;YACP,SAAS,EAAE,YAAY;YACvB,yBAAyB,EAAE,yBAAyB;SACrD;QAED,UAAU,EAAE;YACV,IAAI,EAAE,SAAS;YACf,QAAQ,EAAE,EAAE,IAAI,EAAE,CAAC,EAAE;SACtB;QAED,WAAW,EAAE;YACX,IAAI,EAAE,SAAS;YACf,QAAQ,EAAE,EAAE,KAAK,EAAE,CAAC,EAAE;SACvB;QAED,YAAY,EAAE;YACZ,IAAI,EAAE,SAAS;YACf,QAAQ,EAAE,EAAE,MAAM,EAAE,CAAC,EAAE;YACvB,SAAS,EAAE,IAAI;YACf,cAAc,EAAE,IAAI;SACrB;KAEF;CACF,CAAC;;;;;"}