/*!
FullCalendar v5.10.1
Docs & License: https://fullcalendar.io/
(c) 2021 <PERSON>
*/
var FullCalendarInteraction=function(e,t){"use strict";var n=function(e,t){return(n=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n])})(e,t)};function r(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Class extends value "+String(t)+" is not a constructor or null");function r(){this.constructor=e}n(e,t),e.prototype=null===t?Object.create(t):(r.prototype=t.prototype,new r)}var i=function(){return(i=Object.assign||function(e){for(var t,n=1,r=arguments.length;n<r;n++)for(var i in t=arguments[n])Object.prototype.hasOwnProperty.call(t,i)&&(e[i]=t[i]);return e}).apply(this,arguments)};t.config.touchMouseIgnoreWait=500;var o=0,a=0,l=!1,s=function(){function e(e){var n=this;this.subjectEl=null,this.selector="",this.handleSelector="",this.shouldIgnoreMove=!1,this.shouldWatchScroll=!0,this.isDragging=!1,this.isTouchDragging=!1,this.wasTouchScroll=!1,this.handleMouseDown=function(e){if(!n.shouldIgnoreMouse()&&function(e){return 0===e.button&&!e.ctrlKey}(e)&&n.tryStart(e)){var t=n.createEventFromMouse(e,!0);n.emitter.trigger("pointerdown",t),n.initScrollWatch(t),n.shouldIgnoreMove||document.addEventListener("mousemove",n.handleMouseMove),document.addEventListener("mouseup",n.handleMouseUp)}},this.handleMouseMove=function(e){var t=n.createEventFromMouse(e);n.recordCoords(t),n.emitter.trigger("pointermove",t)},this.handleMouseUp=function(e){document.removeEventListener("mousemove",n.handleMouseMove),document.removeEventListener("mouseup",n.handleMouseUp),n.emitter.trigger("pointerup",n.createEventFromMouse(e)),n.cleanup()},this.handleTouchStart=function(e){if(n.tryStart(e)){n.isTouchDragging=!0;var t=n.createEventFromTouch(e,!0);n.emitter.trigger("pointerdown",t),n.initScrollWatch(t);var r=e.target;n.shouldIgnoreMove||r.addEventListener("touchmove",n.handleTouchMove),r.addEventListener("touchend",n.handleTouchEnd),r.addEventListener("touchcancel",n.handleTouchEnd),window.addEventListener("scroll",n.handleTouchScroll,!0)}},this.handleTouchMove=function(e){var t=n.createEventFromTouch(e);n.recordCoords(t),n.emitter.trigger("pointermove",t)},this.handleTouchEnd=function(e){if(n.isDragging){var r=e.target;r.removeEventListener("touchmove",n.handleTouchMove),r.removeEventListener("touchend",n.handleTouchEnd),r.removeEventListener("touchcancel",n.handleTouchEnd),window.removeEventListener("scroll",n.handleTouchScroll,!0),n.emitter.trigger("pointerup",n.createEventFromTouch(e)),n.cleanup(),n.isTouchDragging=!1,o+=1,setTimeout((function(){o-=1}),t.config.touchMouseIgnoreWait)}},this.handleTouchScroll=function(){n.wasTouchScroll=!0},this.handleScroll=function(e){if(!n.shouldIgnoreMove){var t=window.pageXOffset-n.prevScrollX+n.prevPageX,r=window.pageYOffset-n.prevScrollY+n.prevPageY;n.emitter.trigger("pointermove",{origEvent:e,isTouch:n.isTouchDragging,subjectEl:n.subjectEl,pageX:t,pageY:r,deltaX:t-n.origPageX,deltaY:r-n.origPageY})}},this.containerEl=e,this.emitter=new t.Emitter,e.addEventListener("mousedown",this.handleMouseDown),e.addEventListener("touchstart",this.handleTouchStart,{passive:!0}),1===(a+=1)&&window.addEventListener("touchmove",c,{passive:!1})}return e.prototype.destroy=function(){this.containerEl.removeEventListener("mousedown",this.handleMouseDown),this.containerEl.removeEventListener("touchstart",this.handleTouchStart,{passive:!0}),(a-=1)||window.removeEventListener("touchmove",c,{passive:!1})},e.prototype.tryStart=function(e){var n=this.querySubjectEl(e),r=e.target;return!(!n||this.handleSelector&&!t.elementClosest(r,this.handleSelector))&&(this.subjectEl=n,this.isDragging=!0,this.wasTouchScroll=!1,!0)},e.prototype.cleanup=function(){l=!1,this.isDragging=!1,this.subjectEl=null,this.destroyScrollWatch()},e.prototype.querySubjectEl=function(e){return this.selector?t.elementClosest(e.target,this.selector):this.containerEl},e.prototype.shouldIgnoreMouse=function(){return o||this.isTouchDragging},e.prototype.cancelTouchScroll=function(){this.isDragging&&(l=!0)},e.prototype.initScrollWatch=function(e){this.shouldWatchScroll&&(this.recordCoords(e),window.addEventListener("scroll",this.handleScroll,!0))},e.prototype.recordCoords=function(e){this.shouldWatchScroll&&(this.prevPageX=e.pageX,this.prevPageY=e.pageY,this.prevScrollX=window.pageXOffset,this.prevScrollY=window.pageYOffset)},e.prototype.destroyScrollWatch=function(){this.shouldWatchScroll&&window.removeEventListener("scroll",this.handleScroll,!0)},e.prototype.createEventFromMouse=function(e,t){var n=0,r=0;return t?(this.origPageX=e.pageX,this.origPageY=e.pageY):(n=e.pageX-this.origPageX,r=e.pageY-this.origPageY),{origEvent:e,isTouch:!1,subjectEl:this.subjectEl,pageX:e.pageX,pageY:e.pageY,deltaX:n,deltaY:r}},e.prototype.createEventFromTouch=function(e,t){var n,r,i=e.touches,o=0,a=0;return i&&i.length?(n=i[0].pageX,r=i[0].pageY):(n=e.pageX,r=e.pageY),t?(this.origPageX=n,this.origPageY=r):(o=n-this.origPageX,a=r-this.origPageY),{origEvent:e,isTouch:!0,subjectEl:this.subjectEl,pageX:n,pageY:r,deltaX:o,deltaY:a}},e}();function c(e){l&&e.preventDefault()}var g=function(){function e(){this.isVisible=!1,this.sourceEl=null,this.mirrorEl=null,this.sourceElRect=null,this.parentNode=document.body,this.zIndex=9999,this.revertDuration=0}return e.prototype.start=function(e,t,n){this.sourceEl=e,this.sourceElRect=this.sourceEl.getBoundingClientRect(),this.origScreenX=t-window.pageXOffset,this.origScreenY=n-window.pageYOffset,this.deltaX=0,this.deltaY=0,this.updateElPosition()},e.prototype.handleMove=function(e,t){this.deltaX=e-window.pageXOffset-this.origScreenX,this.deltaY=t-window.pageYOffset-this.origScreenY,this.updateElPosition()},e.prototype.setIsVisible=function(e){e?this.isVisible||(this.mirrorEl&&(this.mirrorEl.style.display=""),this.isVisible=e,this.updateElPosition()):this.isVisible&&(this.mirrorEl&&(this.mirrorEl.style.display="none"),this.isVisible=e)},e.prototype.stop=function(e,t){var n=this,r=function(){n.cleanup(),t()};e&&this.mirrorEl&&this.isVisible&&this.revertDuration&&(this.deltaX||this.deltaY)?this.doRevertAnimation(r,this.revertDuration):setTimeout(r,0)},e.prototype.doRevertAnimation=function(e,n){var r=this.mirrorEl,i=this.sourceEl.getBoundingClientRect();r.style.transition="top "+n+"ms,left "+n+"ms",t.applyStyle(r,{left:i.left,top:i.top}),t.whenTransitionDone(r,(function(){r.style.transition="",e()}))},e.prototype.cleanup=function(){this.mirrorEl&&(t.removeElement(this.mirrorEl),this.mirrorEl=null),this.sourceEl=null},e.prototype.updateElPosition=function(){this.sourceEl&&this.isVisible&&t.applyStyle(this.getMirrorEl(),{left:this.sourceElRect.left+this.deltaX,top:this.sourceElRect.top+this.deltaY})},e.prototype.getMirrorEl=function(){var e=this.sourceElRect,n=this.mirrorEl;return n||((n=this.mirrorEl=this.sourceEl.cloneNode(!0)).classList.add("fc-unselectable"),n.classList.add("fc-event-dragging"),t.applyStyle(n,{position:"fixed",zIndex:this.zIndex,visibility:"",boxSizing:"border-box",width:e.right-e.left,height:e.bottom-e.top,right:"auto",bottom:"auto",margin:0}),this.parentNode.appendChild(n)),n},e}(),d=function(e){function t(t,n){var r=e.call(this)||this;return r.handleScroll=function(){r.scrollTop=r.scrollController.getScrollTop(),r.scrollLeft=r.scrollController.getScrollLeft(),r.handleScrollChange()},r.scrollController=t,r.doesListening=n,r.scrollTop=r.origScrollTop=t.getScrollTop(),r.scrollLeft=r.origScrollLeft=t.getScrollLeft(),r.scrollWidth=t.getScrollWidth(),r.scrollHeight=t.getScrollHeight(),r.clientWidth=t.getClientWidth(),r.clientHeight=t.getClientHeight(),r.clientRect=r.computeClientRect(),r.doesListening&&r.getEventTarget().addEventListener("scroll",r.handleScroll),r}return r(t,e),t.prototype.destroy=function(){this.doesListening&&this.getEventTarget().removeEventListener("scroll",this.handleScroll)},t.prototype.getScrollTop=function(){return this.scrollTop},t.prototype.getScrollLeft=function(){return this.scrollLeft},t.prototype.setScrollTop=function(e){this.scrollController.setScrollTop(e),this.doesListening||(this.scrollTop=Math.max(Math.min(e,this.getMaxScrollTop()),0),this.handleScrollChange())},t.prototype.setScrollLeft=function(e){this.scrollController.setScrollLeft(e),this.doesListening||(this.scrollLeft=Math.max(Math.min(e,this.getMaxScrollLeft()),0),this.handleScrollChange())},t.prototype.getClientWidth=function(){return this.clientWidth},t.prototype.getClientHeight=function(){return this.clientHeight},t.prototype.getScrollWidth=function(){return this.scrollWidth},t.prototype.getScrollHeight=function(){return this.scrollHeight},t.prototype.handleScrollChange=function(){},t}(t.ScrollController),u=function(e){function n(n,r){return e.call(this,new t.ElementScrollController(n),r)||this}return r(n,e),n.prototype.getEventTarget=function(){return this.scrollController.el},n.prototype.computeClientRect=function(){return t.computeInnerRect(this.scrollController.el)},n}(d),h=function(e){function n(n){return e.call(this,new t.WindowScrollController,n)||this}return r(n,e),n.prototype.getEventTarget=function(){return window},n.prototype.computeClientRect=function(){return{left:this.scrollLeft,right:this.scrollLeft+this.clientWidth,top:this.scrollTop,bottom:this.scrollTop+this.clientHeight}},n.prototype.handleScrollChange=function(){this.clientRect=this.computeClientRect()},n}(d),p="function"==typeof performance?performance.now:Date.now,v=function(){function e(){var e=this;this.isEnabled=!0,this.scrollQuery=[window,".fc-scroller"],this.edgeThreshold=50,this.maxVelocity=300,this.pointerScreenX=null,this.pointerScreenY=null,this.isAnimating=!1,this.scrollCaches=null,this.everMovedUp=!1,this.everMovedDown=!1,this.everMovedLeft=!1,this.everMovedRight=!1,this.animate=function(){if(e.isAnimating){var t=e.computeBestEdge(e.pointerScreenX+window.pageXOffset,e.pointerScreenY+window.pageYOffset);if(t){var n=p();e.handleSide(t,(n-e.msSinceRequest)/1e3),e.requestAnimation(n)}else e.isAnimating=!1}}}return e.prototype.start=function(e,t,n){this.isEnabled&&(this.scrollCaches=this.buildCaches(n),this.pointerScreenX=null,this.pointerScreenY=null,this.everMovedUp=!1,this.everMovedDown=!1,this.everMovedLeft=!1,this.everMovedRight=!1,this.handleMove(e,t))},e.prototype.handleMove=function(e,t){if(this.isEnabled){var n=e-window.pageXOffset,r=t-window.pageYOffset,i=null===this.pointerScreenY?0:r-this.pointerScreenY,o=null===this.pointerScreenX?0:n-this.pointerScreenX;i<0?this.everMovedUp=!0:i>0&&(this.everMovedDown=!0),o<0?this.everMovedLeft=!0:o>0&&(this.everMovedRight=!0),this.pointerScreenX=n,this.pointerScreenY=r,this.isAnimating||(this.isAnimating=!0,this.requestAnimation(p()))}},e.prototype.stop=function(){if(this.isEnabled){this.isAnimating=!1;for(var e=0,t=this.scrollCaches;e<t.length;e++){t[e].destroy()}this.scrollCaches=null}},e.prototype.requestAnimation=function(e){this.msSinceRequest=e,requestAnimationFrame(this.animate)},e.prototype.handleSide=function(e,t){var n=e.scrollCache,r=this.edgeThreshold,i=r-e.distance,o=i*i/(r*r)*this.maxVelocity*t,a=1;switch(e.name){case"left":a=-1;case"right":n.setScrollLeft(n.getScrollLeft()+o*a);break;case"top":a=-1;case"bottom":n.setScrollTop(n.getScrollTop()+o*a)}},e.prototype.computeBestEdge=function(e,t){for(var n=this.edgeThreshold,r=null,i=0,o=this.scrollCaches;i<o.length;i++){var a=o[i],l=a.clientRect,s=e-l.left,c=l.right-e,g=t-l.top,d=l.bottom-t;s>=0&&c>=0&&g>=0&&d>=0&&(g<=n&&this.everMovedUp&&a.canScrollUp()&&(!r||r.distance>g)&&(r={scrollCache:a,name:"top",distance:g}),d<=n&&this.everMovedDown&&a.canScrollDown()&&(!r||r.distance>d)&&(r={scrollCache:a,name:"bottom",distance:d}),s<=n&&this.everMovedLeft&&a.canScrollLeft()&&(!r||r.distance>s)&&(r={scrollCache:a,name:"left",distance:s}),c<=n&&this.everMovedRight&&a.canScrollRight()&&(!r||r.distance>c)&&(r={scrollCache:a,name:"right",distance:c}))}return r},e.prototype.buildCaches=function(e){return this.queryScrollEls(e).map((function(e){return e===window?new h(!1):new u(e,!1)}))},e.prototype.queryScrollEls=function(e){for(var n=[],r=0,i=this.scrollQuery;r<i.length;r++){var o=i[r];"object"==typeof o?n.push(o):n.push.apply(n,Array.prototype.slice.call(t.getElRoot(e).querySelectorAll(o)))}return n},e}(),f=function(e){function n(n,r){var i=e.call(this,n)||this;i.containerEl=n,i.delay=null,i.minDistance=0,i.touchScrollAllowed=!0,i.mirrorNeedsRevert=!1,i.isInteracting=!1,i.isDragging=!1,i.isDelayEnded=!1,i.isDistanceSurpassed=!1,i.delayTimeoutId=null,i.onPointerDown=function(e){i.isDragging||(i.isInteracting=!0,i.isDelayEnded=!1,i.isDistanceSurpassed=!1,t.preventSelection(document.body),t.preventContextMenu(document.body),e.isTouch||e.origEvent.preventDefault(),i.emitter.trigger("pointerdown",e),i.isInteracting&&!i.pointer.shouldIgnoreMove&&(i.mirror.setIsVisible(!1),i.mirror.start(e.subjectEl,e.pageX,e.pageY),i.startDelay(e),i.minDistance||i.handleDistanceSurpassed(e)))},i.onPointerMove=function(e){if(i.isInteracting){if(i.emitter.trigger("pointermove",e),!i.isDistanceSurpassed){var t=i.minDistance,n=e.deltaX,r=e.deltaY;n*n+r*r>=t*t&&i.handleDistanceSurpassed(e)}i.isDragging&&("scroll"!==e.origEvent.type&&(i.mirror.handleMove(e.pageX,e.pageY),i.autoScroller.handleMove(e.pageX,e.pageY)),i.emitter.trigger("dragmove",e))}},i.onPointerUp=function(e){i.isInteracting&&(i.isInteracting=!1,t.allowSelection(document.body),t.allowContextMenu(document.body),i.emitter.trigger("pointerup",e),i.isDragging&&(i.autoScroller.stop(),i.tryStopDrag(e)),i.delayTimeoutId&&(clearTimeout(i.delayTimeoutId),i.delayTimeoutId=null))};var o=i.pointer=new s(n);return o.emitter.on("pointerdown",i.onPointerDown),o.emitter.on("pointermove",i.onPointerMove),o.emitter.on("pointerup",i.onPointerUp),r&&(o.selector=r),i.mirror=new g,i.autoScroller=new v,i}return r(n,e),n.prototype.destroy=function(){this.pointer.destroy(),this.onPointerUp({})},n.prototype.startDelay=function(e){var t=this;"number"==typeof this.delay?this.delayTimeoutId=setTimeout((function(){t.delayTimeoutId=null,t.handleDelayEnd(e)}),this.delay):this.handleDelayEnd(e)},n.prototype.handleDelayEnd=function(e){this.isDelayEnded=!0,this.tryStartDrag(e)},n.prototype.handleDistanceSurpassed=function(e){this.isDistanceSurpassed=!0,this.tryStartDrag(e)},n.prototype.tryStartDrag=function(e){this.isDelayEnded&&this.isDistanceSurpassed&&(this.pointer.wasTouchScroll&&!this.touchScrollAllowed||(this.isDragging=!0,this.mirrorNeedsRevert=!1,this.autoScroller.start(e.pageX,e.pageY,this.containerEl),this.emitter.trigger("dragstart",e),!1===this.touchScrollAllowed&&this.pointer.cancelTouchScroll()))},n.prototype.tryStopDrag=function(e){this.mirror.stop(this.mirrorNeedsRevert,this.stopDrag.bind(this,e))},n.prototype.stopDrag=function(e){this.isDragging=!1,this.emitter.trigger("dragend",e)},n.prototype.setIgnoreMove=function(e){this.pointer.shouldIgnoreMove=e},n.prototype.setMirrorIsVisible=function(e){this.mirror.setIsVisible(e)},n.prototype.setMirrorNeedsRevert=function(e){this.mirrorNeedsRevert=e},n.prototype.setAutoScrollEnabled=function(e){this.autoScroller.isEnabled=e},n}(t.ElementDragging),E=function(){function e(e){this.origRect=t.computeRect(e),this.scrollCaches=t.getClippingParents(e).map((function(e){return new u(e,!0)}))}return e.prototype.destroy=function(){for(var e=0,t=this.scrollCaches;e<t.length;e++){t[e].destroy()}},e.prototype.computeLeft=function(){for(var e=this.origRect.left,t=0,n=this.scrollCaches;t<n.length;t++){var r=n[t];e+=r.origScrollLeft-r.getScrollLeft()}return e},e.prototype.computeTop=function(){for(var e=this.origRect.top,t=0,n=this.scrollCaches;t<n.length;t++){var r=n[t];e+=r.origScrollTop-r.getScrollTop()}return e},e.prototype.isWithinClipping=function(e,n){for(var r,i,o={left:e,top:n},a=0,l=this.scrollCaches;a<l.length;a++){var s=l[a];if(r=s.getEventTarget(),i=void 0,"HTML"!==(i=r.tagName)&&"BODY"!==i&&!t.pointInsideRect(o,s.clientRect))return!1}return!0},e}();var m=function(){function e(e,n){var r=this;this.useSubjectCenter=!1,this.requireInitial=!0,this.initialHit=null,this.movingHit=null,this.finalHit=null,this.handlePointerDown=function(e){var t=r.dragging;r.initialHit=null,r.movingHit=null,r.finalHit=null,r.prepareHits(),r.processFirstCoord(e),r.initialHit||!r.requireInitial?(t.setIgnoreMove(!1),r.emitter.trigger("pointerdown",e)):t.setIgnoreMove(!0)},this.handleDragStart=function(e){r.emitter.trigger("dragstart",e),r.handleMove(e,!0)},this.handleDragMove=function(e){r.emitter.trigger("dragmove",e),r.handleMove(e)},this.handlePointerUp=function(e){r.releaseHits(),r.emitter.trigger("pointerup",e)},this.handleDragEnd=function(e){r.movingHit&&r.emitter.trigger("hitupdate",null,!0,e),r.finalHit=r.movingHit,r.movingHit=null,r.emitter.trigger("dragend",e)},this.droppableStore=n,e.emitter.on("pointerdown",this.handlePointerDown),e.emitter.on("dragstart",this.handleDragStart),e.emitter.on("dragmove",this.handleDragMove),e.emitter.on("pointerup",this.handlePointerUp),e.emitter.on("dragend",this.handleDragEnd),this.dragging=e,this.emitter=new t.Emitter}return e.prototype.processFirstCoord=function(e){var n,r={left:e.pageX,top:e.pageY},i=r,o=e.subjectEl;o instanceof HTMLElement&&(n=t.computeRect(o),i=t.constrainPoint(i,n));var a=this.initialHit=this.queryHitForOffset(i.left,i.top);if(a){if(this.useSubjectCenter&&n){var l=t.intersectRects(n,a.rect);l&&(i=t.getRectCenter(l))}this.coordAdjust=t.diffPoints(i,r)}else this.coordAdjust={left:0,top:0}},e.prototype.handleMove=function(e,t){var n=this.queryHitForOffset(e.pageX+this.coordAdjust.left,e.pageY+this.coordAdjust.top);!t&&S(this.movingHit,n)||(this.movingHit=n,this.emitter.trigger("hitupdate",n,!1,e))},e.prototype.prepareHits=function(){this.offsetTrackers=t.mapHash(this.droppableStore,(function(e){return e.component.prepareHits(),new E(e.el)}))},e.prototype.releaseHits=function(){var e=this.offsetTrackers;for(var t in e)e[t].destroy();this.offsetTrackers={}},e.prototype.queryHitForOffset=function(e,n){var r=this.droppableStore,i=this.offsetTrackers,o=null;for(var a in r){var l=r[a].component,s=i[a];if(s&&s.isWithinClipping(e,n)){var c=s.computeLeft(),g=s.computeTop(),d=e-c,u=n-g,h=s.origRect,p=h.right-h.left,v=h.bottom-h.top;if(d>=0&&d<p&&u>=0&&u<v){var f=l.queryHit(d,u,p,v);f&&t.rangeContainsRange(f.dateProfile.activeRange,f.dateSpan.range)&&(!o||f.layer>o.layer)&&(f.componentId=a,f.context=l.context,f.rect.left+=c,f.rect.right+=c,f.rect.top+=g,f.rect.bottom+=g,o=f)}}}return o},e}();function S(e,n){return!e&&!n||Boolean(e)===Boolean(n)&&t.isDateSpansEqual(e.dateSpan,n.dateSpan)}function y(e,t){for(var n,r,o={},a=0,l=t.pluginHooks.datePointTransforms;a<l.length;a++){var s=l[a];i(o,s(e,t))}return i(o,(n=e,{date:(r=t.dateEnv).toDate(n.range.start),dateStr:r.formatIso(n.range.start,{omitTime:n.allDay}),allDay:n.allDay})),o}var D=function(e){function n(n){var r=e.call(this,n)||this;r.handlePointerDown=function(e){var t=r.dragging,n=e.origEvent.target;t.setIgnoreMove(!r.component.isValidDateDownEl(n))},r.handleDragEnd=function(e){var t=r.component;if(!r.dragging.pointer.wasTouchScroll){var n=r.hitDragging,o=n.initialHit,a=n.finalHit;if(o&&a&&S(o,a)){var l=t.context,s=i(i({},y(o.dateSpan,l)),{dayEl:o.dayEl,jsEvent:e.origEvent,view:l.viewApi||l.calendarApi.view});l.emitter.trigger("dateClick",s)}}},r.dragging=new f(n.el),r.dragging.autoScroller.isEnabled=!1;var o=r.hitDragging=new m(r.dragging,t.interactionSettingsToStore(n));return o.emitter.on("pointerdown",r.handlePointerDown),o.emitter.on("dragend",r.handleDragEnd),r}return r(n,e),n.prototype.destroy=function(){this.dragging.destroy()},n}(t.Interaction),w=function(e){function n(n){var r=e.call(this,n)||this;r.dragSelection=null,r.handlePointerDown=function(e){var t=r,n=t.component,i=t.dragging,o=n.context.options.selectable&&n.isValidDateDownEl(e.origEvent.target);i.setIgnoreMove(!o),i.delay=e.isTouch?function(e){var t=e.context.options,n=t.selectLongPressDelay;null==n&&(n=t.longPressDelay);return n}(n):null},r.handleDragStart=function(e){r.component.context.calendarApi.unselect(e)},r.handleHitUpdate=function(e,n){var o=r.component.context,a=null,l=!1;if(e){var s=r.hitDragging.initialHit;e.componentId===s.componentId&&r.isHitComboAllowed&&!r.isHitComboAllowed(s,e)||(a=function(e,n,r){var o=e.dateSpan,a=n.dateSpan,l=[o.range.start,o.range.end,a.range.start,a.range.end];l.sort(t.compareNumbers);for(var s={},c=0,g=r;c<g.length;c++){var d=(0,g[c])(e,n);if(!1===d)return null;d&&i(s,d)}return s.range={start:l[0],end:l[3]},s.allDay=o.allDay,s}(s,e,o.pluginHooks.dateSelectionTransformers)),a&&t.isDateSelectionValid(a,e.dateProfile,o)||(l=!0,a=null)}a?o.dispatch({type:"SELECT_DATES",selection:a}):n||o.dispatch({type:"UNSELECT_DATES"}),l?t.disableCursor():t.enableCursor(),n||(r.dragSelection=a)},r.handlePointerUp=function(e){r.dragSelection&&(t.triggerDateSelect(r.dragSelection,e,r.component.context),r.dragSelection=null)};var o=n.component.context.options,a=r.dragging=new f(n.el);a.touchScrollAllowed=!1,a.minDistance=o.selectMinDistance||0,a.autoScroller.isEnabled=o.dragScroll;var l=r.hitDragging=new m(r.dragging,t.interactionSettingsToStore(n));return l.emitter.on("pointerdown",r.handlePointerDown),l.emitter.on("dragstart",r.handleDragStart),l.emitter.on("hitupdate",r.handleHitUpdate),l.emitter.on("pointerup",r.handlePointerUp),r}return r(n,e),n.prototype.destroy=function(){this.dragging.destroy()},n}(t.Interaction);var T=function(e){function n(r){var o=e.call(this,r)||this;o.subjectEl=null,o.subjectSeg=null,o.isDragging=!1,o.eventRange=null,o.relevantEvents=null,o.receivingContext=null,o.validMutation=null,o.mutatedRelevantEvents=null,o.handlePointerDown=function(e){var n=e.origEvent.target,r=o,i=r.component,a=r.dragging,l=a.mirror,s=i.context.options,c=i.context;o.subjectEl=e.subjectEl;var g=o.subjectSeg=t.getElSeg(e.subjectEl),d=(o.eventRange=g.eventRange).instance.instanceId;o.relevantEvents=t.getRelevantEvents(c.getCurrentData().eventStore,d),a.minDistance=e.isTouch?0:s.eventDragMinDistance,a.delay=e.isTouch&&d!==i.props.eventSelection?function(e){var t=e.context.options,n=t.eventLongPressDelay;null==n&&(n=t.longPressDelay);return n}(i):null,s.fixedMirrorParent?l.parentNode=s.fixedMirrorParent:l.parentNode=t.elementClosest(n,".fc"),l.revertDuration=s.dragRevertDuration;var u=i.isValidSegDownEl(n)&&!t.elementClosest(n,".fc-event-resizer");a.setIgnoreMove(!u),o.isDragging=u&&e.subjectEl.classList.contains("fc-event-draggable")},o.handleDragStart=function(e){var n=o.component.context,r=o.eventRange,i=r.instance.instanceId;e.isTouch?i!==o.component.props.eventSelection&&n.dispatch({type:"SELECT_EVENT",eventInstanceId:i}):n.dispatch({type:"UNSELECT_EVENT"}),o.isDragging&&(n.calendarApi.unselect(e),n.emitter.trigger("eventDragStart",{el:o.subjectEl,event:new t.EventApi(n,r.def,r.instance),jsEvent:e.origEvent,view:n.viewApi}))},o.handleHitUpdate=function(e,n){if(o.isDragging){var r=o.relevantEvents,i=o.hitDragging.initialHit,a=o.component.context,l=null,s=null,c=null,g=!1,d={affectedEvents:r,mutatedEvents:t.createEmptyEventStore(),isEvent:!0};if(e){var u=(l=e.context).options;a===l||u.editable&&u.droppable?(s=function(e,n,r){var i=e.dateSpan,o=n.dateSpan,a=i.range.start,l=o.range.start,s={};i.allDay!==o.allDay&&(s.allDay=o.allDay,s.hasEnd=n.context.options.allDayMaintainDuration,o.allDay&&(a=t.startOfDay(a)));var c=t.diffDates(a,l,e.context.dateEnv,e.componentId===n.componentId?e.largeUnit:null);c.milliseconds&&(s.allDay=!1);for(var g={datesDelta:c,standardProps:s},d=0,u=r;d<u.length;d++){(0,u[d])(g,e,n)}return g}(i,e,l.getCurrentData().pluginHooks.eventDragMutationMassagers))&&(c=t.applyMutationToEventStore(r,l.getCurrentData().eventUiBases,s,l),d.mutatedEvents=c,t.isInteractionValid(d,e.dateProfile,l)||(g=!0,s=null,c=null,d.mutatedEvents=t.createEmptyEventStore())):l=null}o.displayDrag(l,d),g?t.disableCursor():t.enableCursor(),n||(a===l&&S(i,e)&&(s=null),o.dragging.setMirrorNeedsRevert(!s),o.dragging.setMirrorIsVisible(!e||!t.getElRoot(o.subjectEl).querySelector(".fc-event-mirror")),o.receivingContext=l,o.validMutation=s,o.mutatedRelevantEvents=c)}},o.handlePointerUp=function(){o.isDragging||o.cleanup()},o.handleDragEnd=function(e){if(o.isDragging){var n=o.component.context,r=n.viewApi,a=o,l=a.receivingContext,s=a.validMutation,c=o.eventRange.def,g=o.eventRange.instance,d=new t.EventApi(n,c,g),u=o.relevantEvents,h=o.mutatedRelevantEvents,p=o.hitDragging.finalHit;if(o.clearDrag(),n.emitter.trigger("eventDragStop",{el:o.subjectEl,event:d,jsEvent:e.origEvent,view:r}),s){if(l===n){var v=new t.EventApi(n,h.defs[c.defId],g?h.instances[g.instanceId]:null);n.dispatch({type:"MERGE_EVENTS",eventStore:h});for(var f={oldEvent:d,event:v,relatedEvents:t.buildEventApis(h,n,g),revert:function(){n.dispatch({type:"MERGE_EVENTS",eventStore:u})}},E={},m=0,S=n.getCurrentData().pluginHooks.eventDropTransformers;m<S.length;m++){var D=S[m];i(E,D(s,n))}n.emitter.trigger("eventDrop",i(i(i({},f),E),{el:e.subjectEl,delta:s.datesDelta,jsEvent:e.origEvent,view:r})),n.emitter.trigger("eventChange",f)}else if(l){var w={event:d,relatedEvents:t.buildEventApis(u,n,g),revert:function(){n.dispatch({type:"MERGE_EVENTS",eventStore:u})}};n.emitter.trigger("eventLeave",i(i({},w),{draggedEl:e.subjectEl,view:r})),n.dispatch({type:"REMOVE_EVENTS",eventStore:u}),n.emitter.trigger("eventRemove",w);var T=h.defs[c.defId],M=h.instances[g.instanceId],b=new t.EventApi(l,T,M);l.dispatch({type:"MERGE_EVENTS",eventStore:h});var C={event:b,relatedEvents:t.buildEventApis(h,l,M),revert:function(){l.dispatch({type:"REMOVE_EVENTS",eventStore:h})}};l.emitter.trigger("eventAdd",C),e.isTouch&&l.dispatch({type:"SELECT_EVENT",eventInstanceId:g.instanceId}),l.emitter.trigger("drop",i(i({},y(p.dateSpan,l)),{draggedEl:e.subjectEl,jsEvent:e.origEvent,view:p.context.viewApi})),l.emitter.trigger("eventReceive",i(i({},C),{draggedEl:e.subjectEl,view:p.context.viewApi}))}}else n.emitter.trigger("_noEventDrop")}o.cleanup()};var a=o.component.context.options,l=o.dragging=new f(r.el);l.pointer.selector=n.SELECTOR,l.touchScrollAllowed=!1,l.autoScroller.isEnabled=a.dragScroll;var s=o.hitDragging=new m(o.dragging,t.interactionSettingsStore);return s.useSubjectCenter=r.useEventCenter,s.emitter.on("pointerdown",o.handlePointerDown),s.emitter.on("dragstart",o.handleDragStart),s.emitter.on("hitupdate",o.handleHitUpdate),s.emitter.on("pointerup",o.handlePointerUp),s.emitter.on("dragend",o.handleDragEnd),o}return r(n,e),n.prototype.destroy=function(){this.dragging.destroy()},n.prototype.displayDrag=function(e,n){var r=this.component.context,i=this.receivingContext;i&&i!==e&&(i===r?i.dispatch({type:"SET_EVENT_DRAG",state:{affectedEvents:n.affectedEvents,mutatedEvents:t.createEmptyEventStore(),isEvent:!0}}):i.dispatch({type:"UNSET_EVENT_DRAG"})),e&&e.dispatch({type:"SET_EVENT_DRAG",state:n})},n.prototype.clearDrag=function(){var e=this.component.context,t=this.receivingContext;t&&t.dispatch({type:"UNSET_EVENT_DRAG"}),e!==t&&e.dispatch({type:"UNSET_EVENT_DRAG"})},n.prototype.cleanup=function(){this.subjectSeg=null,this.isDragging=!1,this.eventRange=null,this.relevantEvents=null,this.receivingContext=null,this.validMutation=null,this.mutatedRelevantEvents=null},n.SELECTOR=".fc-event-draggable, .fc-event-resizable",n}(t.Interaction);var M=function(e){function n(n){var r=e.call(this,n)||this;r.draggingSegEl=null,r.draggingSeg=null,r.eventRange=null,r.relevantEvents=null,r.validMutation=null,r.mutatedRelevantEvents=null,r.handlePointerDown=function(e){var n=r.component,i=r.querySegEl(e),o=t.getElSeg(i),a=r.eventRange=o.eventRange;r.dragging.minDistance=n.context.options.eventDragMinDistance,r.dragging.setIgnoreMove(!r.component.isValidSegDownEl(e.origEvent.target)||e.isTouch&&r.component.props.eventSelection!==a.instance.instanceId)},r.handleDragStart=function(e){var n=r.component.context,i=r.eventRange;r.relevantEvents=t.getRelevantEvents(n.getCurrentData().eventStore,r.eventRange.instance.instanceId);var o=r.querySegEl(e);r.draggingSegEl=o,r.draggingSeg=t.getElSeg(o),n.calendarApi.unselect(),n.emitter.trigger("eventResizeStart",{el:o,event:new t.EventApi(n,i.def,i.instance),jsEvent:e.origEvent,view:n.viewApi})},r.handleHitUpdate=function(e,n,i){var o=r.component.context,a=r.relevantEvents,l=r.hitDragging.initialHit,s=r.eventRange.instance,c=null,g=null,d=!1,u={affectedEvents:a,mutatedEvents:t.createEmptyEventStore(),isEvent:!0};e&&(e.componentId===l.componentId&&r.isHitComboAllowed&&!r.isHitComboAllowed(l,e)||(c=function(e,n,r,i){var o=e.context.dateEnv,a=e.dateSpan.range.start,l=n.dateSpan.range.start,s=t.diffDates(a,l,o,e.largeUnit);if(r){if(o.add(i.start,s)<i.end)return{startDelta:s}}else if(o.add(i.end,s)>i.start)return{endDelta:s};return null}(l,e,i.subjectEl.classList.contains("fc-event-resizer-start"),s.range)));c&&(g=t.applyMutationToEventStore(a,o.getCurrentData().eventUiBases,c,o),u.mutatedEvents=g,t.isInteractionValid(u,e.dateProfile,o)||(d=!0,c=null,g=null,u.mutatedEvents=null)),g?o.dispatch({type:"SET_EVENT_RESIZE",state:u}):o.dispatch({type:"UNSET_EVENT_RESIZE"}),d?t.disableCursor():t.enableCursor(),n||(c&&S(l,e)&&(c=null),r.validMutation=c,r.mutatedRelevantEvents=g)},r.handleDragEnd=function(e){var n=r.component.context,o=r.eventRange.def,a=r.eventRange.instance,l=new t.EventApi(n,o,a),s=r.relevantEvents,c=r.mutatedRelevantEvents;if(n.emitter.trigger("eventResizeStop",{el:r.draggingSegEl,event:l,jsEvent:e.origEvent,view:n.viewApi}),r.validMutation){var g=new t.EventApi(n,c.defs[o.defId],a?c.instances[a.instanceId]:null);n.dispatch({type:"MERGE_EVENTS",eventStore:c});var d={oldEvent:l,event:g,relatedEvents:t.buildEventApis(c,n,a),revert:function(){n.dispatch({type:"MERGE_EVENTS",eventStore:s})}};n.emitter.trigger("eventResize",i(i({},d),{el:r.draggingSegEl,startDelta:r.validMutation.startDelta||t.createDuration(0),endDelta:r.validMutation.endDelta||t.createDuration(0),jsEvent:e.origEvent,view:n.viewApi})),n.emitter.trigger("eventChange",d)}else n.emitter.trigger("_noEventResize");r.draggingSeg=null,r.relevantEvents=null,r.validMutation=null};var o=n.component,a=r.dragging=new f(n.el);a.pointer.selector=".fc-event-resizer",a.touchScrollAllowed=!1,a.autoScroller.isEnabled=o.context.options.dragScroll;var l=r.hitDragging=new m(r.dragging,t.interactionSettingsToStore(n));return l.emitter.on("pointerdown",r.handlePointerDown),l.emitter.on("dragstart",r.handleDragStart),l.emitter.on("hitupdate",r.handleHitUpdate),l.emitter.on("dragend",r.handleDragEnd),r}return r(n,e),n.prototype.destroy=function(){this.dragging.destroy()},n.prototype.querySegEl=function(e){return t.elementClosest(e.subjectEl,".fc-event")},n}(t.Interaction);var b=function(){function e(e){var n=this;this.context=e,this.isRecentPointerDateSelect=!1,this.matchesCancel=!1,this.matchesEvent=!1,this.onSelect=function(e){e.jsEvent&&(n.isRecentPointerDateSelect=!0)},this.onDocumentPointerDown=function(e){var r=n.context.options.unselectCancel,i=t.getEventTargetViaRoot(e.origEvent);n.matchesCancel=!!t.elementClosest(i,r),n.matchesEvent=!!t.elementClosest(i,T.SELECTOR)},this.onDocumentPointerUp=function(e){var t=n.context,r=n.documentPointer,i=t.getCurrentData();if(!r.wasTouchScroll){if(i.dateSelection&&!n.isRecentPointerDateSelect){var o=t.options.unselectAuto;!o||o&&n.matchesCancel||t.calendarApi.unselect(e)}i.eventSelection&&!n.matchesEvent&&t.dispatch({type:"UNSELECT_EVENT"})}n.isRecentPointerDateSelect=!1};var r=this.documentPointer=new s(document);r.shouldIgnoreMove=!0,r.shouldWatchScroll=!1,r.emitter.on("pointerdown",this.onDocumentPointerDown),r.emitter.on("pointerup",this.onDocumentPointerUp),e.emitter.on("select",this.onSelect)}return e.prototype.destroy=function(){this.context.emitter.off("select",this.onSelect),this.documentPointer.destroy()},e}(),C={fixedMirrorParent:t.identity},R={dateClick:t.identity,eventDragStart:t.identity,eventDragStop:t.identity,eventDrop:t.identity,eventResizeStart:t.identity,eventResizeStop:t.identity,eventResize:t.identity,drop:t.identity,eventReceive:t.identity,eventLeave:t.identity},I=function(){function e(e,n){var r=this;this.receivingContext=null,this.droppableEvent=null,this.suppliedDragMeta=null,this.dragMeta=null,this.handleDragStart=function(e){r.dragMeta=r.buildDragMeta(e.subjectEl)},this.handleHitUpdate=function(e,n,o){var a=r.hitDragging.dragging,l=null,s=null,c=!1,g={affectedEvents:t.createEmptyEventStore(),mutatedEvents:t.createEmptyEventStore(),isEvent:r.dragMeta.create};e&&(l=e.context,r.canDropElOnCalendar(o.subjectEl,l)&&(s=function(e,n,r){for(var o=i({},n.leftoverProps),a=0,l=r.pluginHooks.externalDefTransforms;a<l.length;a++){var s=l[a];i(o,s(e,n))}var c=t.refineEventDef(o,r),g=c.refined,d=c.extra,u=t.parseEventDef(g,d,n.sourceId,e.allDay,r.options.forceEventDuration||Boolean(n.duration),r),h=e.range.start;e.allDay&&n.startTime&&(h=r.dateEnv.add(h,n.startTime));var p=n.duration?r.dateEnv.add(h,n.duration):t.getDefaultEventEnd(e.allDay,h,r),v=t.createEventInstance(u.defId,{start:h,end:p});return{def:u,instance:v}}(e.dateSpan,r.dragMeta,l),g.mutatedEvents=t.eventTupleToStore(s),(c=!t.isInteractionValid(g,e.dateProfile,l))&&(g.mutatedEvents=t.createEmptyEventStore(),s=null))),r.displayDrag(l,g),a.setMirrorIsVisible(n||!s||!document.querySelector(".fc-event-mirror")),c?t.disableCursor():t.enableCursor(),n||(a.setMirrorNeedsRevert(!s),r.receivingContext=l,r.droppableEvent=s)},this.handleDragEnd=function(e){var n=r,o=n.receivingContext,a=n.droppableEvent;if(r.clearDrag(),o&&a){var l=r.hitDragging.finalHit,s=l.context.viewApi,c=r.dragMeta;if(o.emitter.trigger("drop",i(i({},y(l.dateSpan,o)),{draggedEl:e.subjectEl,jsEvent:e.origEvent,view:s})),c.create){var g=t.eventTupleToStore(a);o.dispatch({type:"MERGE_EVENTS",eventStore:g}),e.isTouch&&o.dispatch({type:"SELECT_EVENT",eventInstanceId:a.instance.instanceId}),o.emitter.trigger("eventReceive",{event:new t.EventApi(o,a.def,a.instance),relatedEvents:[],revert:function(){o.dispatch({type:"REMOVE_EVENTS",eventStore:g})},draggedEl:e.subjectEl,view:s})}}r.receivingContext=null,r.droppableEvent=null};var o=this.hitDragging=new m(e,t.interactionSettingsStore);o.requireInitial=!1,o.emitter.on("dragstart",this.handleDragStart),o.emitter.on("hitupdate",this.handleHitUpdate),o.emitter.on("dragend",this.handleDragEnd),this.suppliedDragMeta=n}return e.prototype.buildDragMeta=function(e){return"object"==typeof this.suppliedDragMeta?t.parseDragMeta(this.suppliedDragMeta):"function"==typeof this.suppliedDragMeta?t.parseDragMeta(this.suppliedDragMeta(e)):(n=function(e,n){var r=t.config.dataAttrPrefix,i=(r?r+"-":"")+n;return e.getAttribute("data-"+i)||""}(e,"event"),r=n?JSON.parse(n):{create:!1},t.parseDragMeta(r));var n,r},e.prototype.displayDrag=function(e,t){var n=this.receivingContext;n&&n!==e&&n.dispatch({type:"UNSET_EVENT_DRAG"}),e&&e.dispatch({type:"SET_EVENT_DRAG",state:t})},e.prototype.clearDrag=function(){this.receivingContext&&this.receivingContext.dispatch({type:"UNSET_EVENT_DRAG"})},e.prototype.canDropElOnCalendar=function(e,n){var r=n.options.dropAccept;return"function"==typeof r?r.call(n.calendarApi,e):"string"!=typeof r||!r||Boolean(t.elementMatches(e,r))},e}();t.config.dataAttrPrefix="";var P=function(){function e(e,n){var r=this;void 0===n&&(n={}),this.handlePointerDown=function(e){var n=r.dragging,i=r.settings,o=i.minDistance,a=i.longPressDelay;n.minDistance=null!=o?o:e.isTouch?0:t.BASE_OPTION_DEFAULTS.eventDragMinDistance,n.delay=e.isTouch?null!=a?a:t.BASE_OPTION_DEFAULTS.longPressDelay:0},this.handleDragStart=function(e){e.isTouch&&r.dragging.delay&&e.subjectEl.classList.contains("fc-event")&&r.dragging.mirror.getMirrorEl().classList.add("fc-event-selected")},this.settings=n;var i=this.dragging=new f(e);i.touchScrollAllowed=!1,null!=n.itemSelector&&(i.pointer.selector=n.itemSelector),null!=n.appendTo&&(i.mirror.parentNode=n.appendTo),i.emitter.on("pointerdown",this.handlePointerDown),i.emitter.on("dragstart",this.handleDragStart),new I(i,n.eventData)}return e.prototype.destroy=function(){this.dragging.destroy()},e}(),A=function(e){function t(t){var n=e.call(this,t)||this;n.shouldIgnoreMove=!1,n.mirrorSelector="",n.currentMirrorEl=null,n.handlePointerDown=function(e){n.emitter.trigger("pointerdown",e),n.shouldIgnoreMove||n.emitter.trigger("dragstart",e)},n.handlePointerMove=function(e){n.shouldIgnoreMove||n.emitter.trigger("dragmove",e)},n.handlePointerUp=function(e){n.emitter.trigger("pointerup",e),n.shouldIgnoreMove||n.emitter.trigger("dragend",e)};var r=n.pointer=new s(t);return r.emitter.on("pointerdown",n.handlePointerDown),r.emitter.on("pointermove",n.handlePointerMove),r.emitter.on("pointerup",n.handlePointerUp),n}return r(t,e),t.prototype.destroy=function(){this.pointer.destroy()},t.prototype.setIgnoreMove=function(e){this.shouldIgnoreMove=e},t.prototype.setMirrorIsVisible=function(e){if(e)this.currentMirrorEl&&(this.currentMirrorEl.style.visibility="",this.currentMirrorEl=null);else{var t=this.mirrorSelector?document.querySelector(this.mirrorSelector):null;t&&(this.currentMirrorEl=t,t.style.visibility="hidden")}},t}(t.ElementDragging),L=function(){function e(e,t){var n=document;e===document||e instanceof Element?(n=e,t=t||{}):t=e||{};var r=this.dragging=new A(n);"string"==typeof t.itemSelector?r.pointer.selector=t.itemSelector:n===document&&(r.pointer.selector="[data-event]"),"string"==typeof t.mirrorSelector&&(r.mirrorSelector=t.mirrorSelector),new I(r,t.eventData)}return e.prototype.destroy=function(){this.dragging.destroy()},e}(),x=t.createPlugin({componentInteractions:[D,w,T,M],calendarInteractions:[b],elementDraggingImpl:f,optionRefiners:C,listenerRefiners:R});return t.globalPlugins.push(x),e.Draggable=P,e.FeaturefulElementDragging=f,e.PointerDragging=s,e.ThirdPartyDraggable=L,e.default=x,Object.defineProperty(e,"__esModule",{value:!0}),e}({},FullCalendar);