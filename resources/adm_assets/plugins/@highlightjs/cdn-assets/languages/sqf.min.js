/*! `sqf` grammar compiled for Highlight.js 11.4.0 */
(()=>{var e=(()=>{"use strict";return e=>{const t={className:"string",
variants:[{begin:'"',end:'"',contains:[{begin:'""',relevance:0}]},{begin:"'",
end:"'",contains:[{begin:"''",relevance:0}]}]},a={className:"meta",
begin:/#\s*[a-z]+\b/,end:/$/,keywords:{
keyword:"define undef ifdef ifndef else endif include"},contains:[{begin:/\\\n/,
relevance:0},e.inherit(t,{className:"string"}),{className:"string",
begin:/<[^\n>]*>/,end:/$/,illegal:"\\n"
},e.C_LINE_COMMENT_MODE,e.C_BLOCK_COMMENT_MODE]};return{name:"SQF",
case_insensitive:!0,keywords:{
keyword:["case","catch","default","do","else","exit","exitWith","for","forEach","from","if","private","switch","then","throw","to","try","waitUntil","while","with"],
built_in:["abs","accTime","acos","action","actionIDs","actionKeys","actionKeysImages","actionKeysNames","actionKeysNamesArray","actionName","actionParams","activateAddons","activatedAddons","activateKey","add3DENConnection","add3DENEventHandler","add3DENLayer","addAction","addBackpack","addBackpackCargo","addBackpackCargoGlobal","addBackpackGlobal","addBinocularItem","addCamShake","addCuratorAddons","addCuratorCameraArea","addCuratorEditableObjects","addCuratorEditingArea","addCuratorPoints","addEditorObject","addEventHandler","addForce","addForceGeneratorRTD","addGoggles","addGroupIcon","addHandgunItem","addHeadgear","addItem","addItemCargo","addItemCargoGlobal","addItemPool","addItemToBackpack","addItemToUniform","addItemToVest","addLiveStats","addMagazine","addMagazineAmmoCargo","addMagazineCargo","addMagazineCargoGlobal","addMagazineGlobal","addMagazinePool","addMagazines","addMagazineTurret","addMenu","addMenuItem","addMissionEventHandler","addMPEventHandler","addMusicEventHandler","addonFiles","addOwnedMine","addPlayerScores","addPrimaryWeaponItem","addPublicVariableEventHandler","addRating","addResources","addScore","addScoreSide","addSecondaryWeaponItem","addSwitchableUnit","addTeamMember","addToRemainsCollector","addTorque","addUniform","addVehicle","addVest","addWaypoint","addWeapon","addWeaponCargo","addWeaponCargoGlobal","addWeaponGlobal","addWeaponItem","addWeaponPool","addWeaponTurret","addWeaponWithAttachmentsCargo","addWeaponWithAttachmentsCargoGlobal","admin","agent","agents","AGLToASL","aimedAtTarget","aimPos","airDensityCurveRTD","airDensityRTD","airplaneThrottle","airportSide","AISFinishHeal","alive","all3DENEntities","allActiveTitleEffects","allAddonsInfo","allAirports","allControls","allCurators","allCutLayers","allDead","allDeadMen","allDiarySubjects","allDisplays","allGroups","allMapMarkers","allMines","allMissionObjects","allow3DMode","allowCrewInImmobile","allowCuratorLogicIgnoreAreas","allowDamage","allowDammage","allowFileOperations","allowFleeing","allowGetIn","allowSprint","allPlayers","allSimpleObjects","allSites","allTurrets","allUnits","allUnitsUAV","allVariables","ammo","ammoOnPylon","and","animate","animateBay","animateDoor","animatePylon","animateSource","animationNames","animationPhase","animationSourcePhase","animationState","apertureParams","append","apply","armoryPoints","arrayIntersect","asin","ASLToAGL","ASLToATL","assert","assignAsCargo","assignAsCargoIndex","assignAsCommander","assignAsDriver","assignAsGunner","assignAsTurret","assignCurator","assignedCargo","assignedCommander","assignedDriver","assignedGunner","assignedItems","assignedTarget","assignedTeam","assignedVehicle","assignedVehicleRole","assignItem","assignTeam","assignToAirport","atan","atan2","atg","ATLToASL","attachedObject","attachedObjects","attachedTo","attachObject","attachTo","attackEnabled","backpack","backpackCargo","backpackContainer","backpackItems","backpackMagazines","backpackSpaceFor","batteryChargeRTD","behaviour","benchmark","bezierInterpolation","binocular","binocularItems","binocularMagazine","boundingBox","boundingBoxReal","boundingCenter","break","breakOut","breakTo","breakWith","briefingName","buildingExit","buildingPos","buldozer_EnableRoadDiag","buldozer_IsEnabledRoadDiag","buldozer_LoadNewRoads","buldozer_reloadOperMap","buttonAction","buttonSetAction","cadetMode","calculatePath","calculatePlayerVisibilityByFriendly","call","callExtension","camCommand","camCommit","camCommitPrepared","camCommitted","camConstuctionSetParams","camCreate","camDestroy","cameraEffect","cameraEffectEnableHUD","cameraInterest","cameraOn","cameraView","campaignConfigFile","camPreload","camPreloaded","camPrepareBank","camPrepareDir","camPrepareDive","camPrepareFocus","camPrepareFov","camPrepareFovRange","camPreparePos","camPrepareRelPos","camPrepareTarget","camSetBank","camSetDir","camSetDive","camSetFocus","camSetFov","camSetFovRange","camSetPos","camSetRelPos","camSetTarget","camTarget","camUseNVG","canAdd","canAddItemToBackpack","canAddItemToUniform","canAddItemToVest","cancelSimpleTaskDestination","canFire","canMove","canSlingLoad","canStand","canSuspend","canTriggerDynamicSimulation","canUnloadInCombat","canVehicleCargo","captive","captiveNum","cbChecked","cbSetChecked","ceil","channelEnabled","cheatsEnabled","checkAIFeature","checkVisibility","className","clear3DENAttribute","clear3DENInventory","clearAllItemsFromBackpack","clearBackpackCargo","clearBackpackCargoGlobal","clearForcesRTD","clearGroupIcons","clearItemCargo","clearItemCargoGlobal","clearItemPool","clearMagazineCargo","clearMagazineCargoGlobal","clearMagazinePool","clearOverlay","clearRadio","clearVehicleInit","clearWeaponCargo","clearWeaponCargoGlobal","clearWeaponPool","clientOwner","closeDialog","closeDisplay","closeOverlay","collapseObjectTree","collect3DENHistory","collectiveRTD","combatBehaviour","combatMode","commandArtilleryFire","commandChat","commander","commandFire","commandFollow","commandFSM","commandGetOut","commandingMenu","commandMove","commandRadio","commandStop","commandSuppressiveFire","commandTarget","commandWatch","comment","commitOverlay","compile","compileFinal","compileScript","completedFSM","composeText","configClasses","configFile","configHierarchy","configName","configOf","configProperties","configSourceAddonList","configSourceMod","configSourceModList","confirmSensorTarget","connectTerminalToUAV","connectToServer","continue","continueWith","controlsGroupCtrl","copyFromClipboard","copyToClipboard","copyWaypoints","cos","count","countEnemy","countFriendly","countSide","countType","countUnknown","create3DENComposition","create3DENEntity","createAgent","createCenter","createDialog","createDiaryLink","createDiaryRecord","createDiarySubject","createDisplay","createGearDialog","createGroup","createGuardedPoint","createHashMap","createHashMapFromArray","createLocation","createMarker","createMarkerLocal","createMenu","createMine","createMissionDisplay","createMPCampaignDisplay","createSimpleObject","createSimpleTask","createSite","createSoundSource","createTarget","createTask","createTeam","createTrigger","createUnit","createVehicle","createVehicleCrew","createVehicleLocal","crew","ctAddHeader","ctAddRow","ctClear","ctCurSel","ctData","ctFindHeaderRows","ctFindRowHeader","ctHeaderControls","ctHeaderCount","ctRemoveHeaders","ctRemoveRows","ctrlActivate","ctrlAddEventHandler","ctrlAngle","ctrlAnimateModel","ctrlAnimationPhaseModel","ctrlAutoScrollDelay","ctrlAutoScrollRewind","ctrlAutoScrollSpeed","ctrlChecked","ctrlClassName","ctrlCommit","ctrlCommitted","ctrlCreate","ctrlDelete","ctrlEnable","ctrlEnabled","ctrlFade","ctrlFontHeight","ctrlHTMLLoaded","ctrlIDC","ctrlIDD","ctrlMapAnimAdd","ctrlMapAnimClear","ctrlMapAnimCommit","ctrlMapAnimDone","ctrlMapCursor","ctrlMapMouseOver","ctrlMapScale","ctrlMapScreenToWorld","ctrlMapWorldToScreen","ctrlModel","ctrlModelDirAndUp","ctrlModelScale","ctrlMousePosition","ctrlParent","ctrlParentControlsGroup","ctrlPosition","ctrlRemoveAllEventHandlers","ctrlRemoveEventHandler","ctrlScale","ctrlScrollValues","ctrlSetActiveColor","ctrlSetAngle","ctrlSetAutoScrollDelay","ctrlSetAutoScrollRewind","ctrlSetAutoScrollSpeed","ctrlSetBackgroundColor","ctrlSetChecked","ctrlSetDisabledColor","ctrlSetEventHandler","ctrlSetFade","ctrlSetFocus","ctrlSetFont","ctrlSetFontH1","ctrlSetFontH1B","ctrlSetFontH2","ctrlSetFontH2B","ctrlSetFontH3","ctrlSetFontH3B","ctrlSetFontH4","ctrlSetFontH4B","ctrlSetFontH5","ctrlSetFontH5B","ctrlSetFontH6","ctrlSetFontH6B","ctrlSetFontHeight","ctrlSetFontHeightH1","ctrlSetFontHeightH2","ctrlSetFontHeightH3","ctrlSetFontHeightH4","ctrlSetFontHeightH5","ctrlSetFontHeightH6","ctrlSetFontHeightSecondary","ctrlSetFontP","ctrlSetFontPB","ctrlSetFontSecondary","ctrlSetForegroundColor","ctrlSetModel","ctrlSetModelDirAndUp","ctrlSetModelScale","ctrlSetMousePosition","ctrlSetPixelPrecision","ctrlSetPosition","ctrlSetPositionH","ctrlSetPositionW","ctrlSetPositionX","ctrlSetPositionY","ctrlSetScale","ctrlSetScrollValues","ctrlSetStructuredText","ctrlSetText","ctrlSetTextColor","ctrlSetTextColorSecondary","ctrlSetTextSecondary","ctrlSetTextSelection","ctrlSetTooltip","ctrlSetTooltipColorBox","ctrlSetTooltipColorShade","ctrlSetTooltipColorText","ctrlSetURL","ctrlShow","ctrlShown","ctrlStyle","ctrlText","ctrlTextColor","ctrlTextHeight","ctrlTextSecondary","ctrlTextSelection","ctrlTextWidth","ctrlTooltip","ctrlType","ctrlURL","ctrlVisible","ctRowControls","ctRowCount","ctSetCurSel","ctSetData","ctSetHeaderTemplate","ctSetRowTemplate","ctSetValue","ctValue","curatorAddons","curatorCamera","curatorCameraArea","curatorCameraAreaCeiling","curatorCoef","curatorEditableObjects","curatorEditingArea","curatorEditingAreaType","curatorMouseOver","curatorPoints","curatorRegisteredObjects","curatorSelected","curatorWaypointCost","current3DENOperation","currentChannel","currentCommand","currentMagazine","currentMagazineDetail","currentMagazineDetailTurret","currentMagazineTurret","currentMuzzle","currentNamespace","currentPilot","currentTask","currentTasks","currentThrowable","currentVisionMode","currentWaypoint","currentWeapon","currentWeaponMode","currentWeaponTurret","currentZeroing","cursorObject","cursorTarget","customChat","customRadio","customWaypointPosition","cutFadeOut","cutObj","cutRsc","cutText","damage","date","dateToNumber","daytime","deActivateKey","debriefingText","debugFSM","debugLog","decayGraphValues","deg","delete3DENEntities","deleteAt","deleteCenter","deleteCollection","deleteEditorObject","deleteGroup","deleteGroupWhenEmpty","deleteIdentity","deleteLocation","deleteMarker","deleteMarkerLocal","deleteRange","deleteResources","deleteSite","deleteStatus","deleteTarget","deleteTeam","deleteVehicle","deleteVehicleCrew","deleteWaypoint","detach","detectedMines","diag_activeMissionFSMs","diag_activeScripts","diag_activeSQSScripts","diag_captureFrameToFile","diag_captureSlowFrame","diag_deltaTime","diag_drawMode","diag_enable","diag_enabled","diag_fps","diag_fpsMin","diag_frameNo","diag_list","diag_mergeConfigFile","diag_scope","diag_activeSQFScripts","diag_allMissionEventHandlers","diag_captureFrame","diag_codePerformance","diag_dumpCalltraceToLog","diag_dumpTerrainSynth","diag_dynamicSimulationEnd","diag_exportConfig","diag_exportTerrainSVG","diag_lightNewLoad","diag_localized","diag_log","diag_logSlowFrame","diag_recordTurretLimits","diag_resetShapes","diag_setLightNew","diag_tickTime","diag_toggle","dialog","diaryRecordNull","diarySubjectExists","didJIP","didJIPOwner","difficulty","difficultyEnabled","difficultyEnabledRTD","difficultyOption","direction","directSay","disableAI","disableCollisionWith","disableConversation","disableDebriefingStats","disableMapIndicators","disableNVGEquipment","disableRemoteSensors","disableSerialization","disableTIEquipment","disableUAVConnectability","disableUserInput","displayAddEventHandler","displayCtrl","displayParent","displayRemoveAllEventHandlers","displayRemoveEventHandler","displaySetEventHandler","dissolveTeam","distance","distance2D","distanceSqr","distributionRegion","do3DENAction","doArtilleryFire","doFire","doFollow","doFSM","doGetOut","doMove","doorPhase","doStop","doSuppressiveFire","doTarget","doWatch","drawArrow","drawEllipse","drawIcon","drawIcon3D","drawLine","drawLine3D","drawLink","drawLocation","drawPolygon","drawRectangle","drawTriangle","driver","drop","dynamicSimulationDistance","dynamicSimulationDistanceCoef","dynamicSimulationEnabled","dynamicSimulationSystemEnabled","echo","edit3DENMissionAttributes","editObject","editorSetEventHandler","effectiveCommander","elevatePeriscope","emptyPositions","enableAI","enableAIFeature","enableAimPrecision","enableAttack","enableAudioFeature","enableAutoStartUpRTD","enableAutoTrimRTD","enableCamShake","enableCaustics","enableChannel","enableCollisionWith","enableCopilot","enableDebriefingStats","enableDiagLegend","enableDynamicSimulation","enableDynamicSimulationSystem","enableEndDialog","enableEngineArtillery","enableEnvironment","enableFatigue","enableGunLights","enableInfoPanelComponent","enableIRLasers","enableMimics","enablePersonTurret","enableRadio","enableReload","enableRopeAttach","enableSatNormalOnDetail","enableSaving","enableSentences","enableSimulation","enableSimulationGlobal","enableStamina","enableStressDamage","enableTeamSwitch","enableTraffic","enableUAVConnectability","enableUAVWaypoints","enableVehicleCargo","enableVehicleSensor","enableWeaponDisassembly","endLoadingScreen","endMission","enemy","engineOn","enginesIsOnRTD","enginesPowerRTD","enginesRpmRTD","enginesTorqueRTD","entities","environmentEnabled","environmentVolume","estimatedEndServerTime","estimatedTimeLeft","evalObjectArgument","everyBackpack","everyContainer","exec","execEditorScript","execFSM","execVM","exp","expectedDestination","exportJIPMessages","exportLandscapeXYZ","eyeDirection","eyePos","face","faction","fadeEnvironment","fadeMusic","fadeRadio","fadeSound","fadeSpeech","failMission","fileExists","fillWeaponsFromPool","find","findCover","findDisplay","findEditorObject","findEmptyPosition","findEmptyPositionReady","findIf","findNearestEnemy","finishMissionInit","finite","fire","fireAtTarget","firstBackpack","flag","flagAnimationPhase","flagOwner","flagSide","flagTexture","flatten","fleeing","floor","flyInHeight","flyInHeightASL","focusedCtrl","fog","fogForecast","fogParams","forceAddUniform","forceAtPositionRTD","forceCadetDifficulty","forcedMap","forceEnd","forceFlagTexture","forceFollowRoad","forceGeneratorRTD","forceMap","forceRespawn","forceSpeed","forceUnicode","forceWalk","forceWeaponFire","forceWeatherChange","forEachMember","forEachMemberAgent","forEachMemberTeam","forgetTarget","format","formation","formationDirection","formationLeader","formationMembers","formationPosition","formationTask","formatText","formLeader","freeLook","friendly","fromEditor","fuel","fullCrew","gearIDCAmmoCount","gearSlotAmmoCount","gearSlotData","get","get3DENActionState","get3DENAttribute","get3DENCamera","get3DENConnections","get3DENEntity","get3DENEntityID","get3DENGrid","get3DENIconsVisible","get3DENLayerEntities","get3DENLinesVisible","get3DENMissionAttribute","get3DENMouseOver","get3DENSelected","getAimingCoef","getAllEnvSoundControllers","getAllHitPointsDamage","getAllOwnedMines","getAllPylonsInfo","getAllSoundControllers","getAllUnitTraits","getAmmoCargo","getAnimAimPrecision","getAnimSpeedCoef","getArray","getArtilleryAmmo","getArtilleryComputerSettings","getArtilleryETA","getAssetDLCInfo","getAssignedCuratorLogic","getAssignedCuratorUnit","getAttackTarget","getAudioOptionVolumes","getBackpackCargo","getBleedingRemaining","getBurningValue","getCalculatePlayerVisibilityByFriendly","getCameraViewDirection","getCargoIndex","getCenterOfMass","getClientState","getClientStateNumber","getCompatiblePylonMagazines","getConnectedUAV","getContainerMaxLoad","getCursorObjectParams","getCustomAimCoef","getCustomSoundController","getCustomSoundControllerCount","getDammage","getDescription","getDir","getDirVisual","getDiverState","getDLCAssetsUsage","getDLCAssetsUsageByName","getDLCs","getDLCUsageTime","getEditorCamera","getEditorMode","getEditorObjectScope","getElevationOffset","getEnvSoundController","getFatigue","getFieldManualStartPage","getForcedFlagTexture","getFriend","getFSMVariable","getFuelCargo","getGraphValues","getGroupIcon","getGroupIconParams","getGroupIcons","getHideFrom","getHit","getHitIndex","getHitPointDamage","getItemCargo","getLighting","getLightingAt","getLoadedModsInfo","getMagazineCargo","getMarkerColor","getMarkerPos","getMarkerSize","getMarkerType","getMass","getMissionConfig","getMissionConfigValue","getMissionDLCs","getMissionLayerEntities","getMissionLayers","getMissionPath","getModelInfo","getMousePosition","getMusicPlayedTime","getNumber","getObjectArgument","getObjectChildren","getObjectDLC","getObjectFOV","getObjectMaterials","getObjectProxy","getObjectScale","getObjectTextures","getObjectType","getObjectViewDistance","getOrDefault","getOxygenRemaining","getPersonUsedDLCs","getPilotCameraDirection","getPilotCameraPosition","getPilotCameraRotation","getPilotCameraTarget","getPlateNumber","getPlayerChannel","getPlayerID","getPlayerScores","getPlayerUID","getPlayerUIDOld","getPlayerVoNVolume","getPos","getPosASL","getPosASLVisual","getPosASLW","getPosATL","getPosATLVisual","getPosVisual","getPosWorld","getPosWorldVisual","getPylonMagazines","getRelDir","getRelPos","getRemoteSensorsDisabled","getRepairCargo","getResolution","getRoadInfo","getRotorBrakeRTD","getShadowDistance","getShotParents","getSlingLoad","getSoundController","getSoundControllerResult","getSpeed","getStamina","getStatValue","getSteamFriendsServers","getSubtitleOptions","getSuppression","getTerrainGrid","getTerrainHeightASL","getText","getTextRaw","getTextWidth","getTotalDLCUsageTime","getTrimOffsetRTD","getUnitLoadout","getUnitTrait","getUserMFDText","getUserMFDValue","getVariable","getVehicleCargo","getVehicleTIPars","getWeaponCargo","getWeaponSway","getWingsOrientationRTD","getWingsPositionRTD","getWorld","getWPPos","glanceAt","globalChat","globalRadio","goggles","goto","group","groupChat","groupFromNetId","groupIconSelectable","groupIconsVisible","groupId","groupOwner","groupRadio","groupSelectedUnits","groupSelectUnit","gunner","gusts","halt","handgunItems","handgunMagazine","handgunWeapon","handsHit","hasInterface","hasPilotCamera","hasWeapon","hcAllGroups","hcGroupParams","hcLeader","hcRemoveAllGroups","hcRemoveGroup","hcSelected","hcSelectGroup","hcSetGroup","hcShowBar","hcShownBar","headgear","hideBehindScripted","hideBody","hideObject","hideObjectGlobal","hideSelection","hierarchyObjectsCount","hint","hintC","hintCadet","hintSilent","hmd","hostMission","htmlLoad","HUDMovementLevels","humidity","image","importAllGroups","importance","in","inArea","inAreaArray","incapacitatedState","inflame","inflamed","infoPanel","infoPanelComponentEnabled","infoPanelComponents","infoPanels","inGameUISetEventHandler","inheritsFrom","initAmbientLife","inPolygon","inputAction","inRangeOfArtillery","insert","insertEditorObject","intersect","is3DEN","is3DENMultiplayer","is3DENPreview","isAbleToBreathe","isActionMenuVisible","isAgent","isAimPrecisionEnabled","isArray","isAutoHoverOn","isAutonomous","isAutoStartUpEnabledRTD","isAutotest","isAutoTrimOnRTD","isBleeding","isBurning","isClass","isCollisionLightOn","isCopilotEnabled","isDamageAllowed","isDedicated","isDLCAvailable","isEngineOn","isEqualTo","isEqualType","isEqualTypeAll","isEqualTypeAny","isEqualTypeArray","isEqualTypeParams","isFilePatchingEnabled","isFinal","isFlashlightOn","isFlatEmpty","isForcedWalk","isFormationLeader","isGameFocused","isGamePaused","isGroupDeletedWhenEmpty","isHidden","isHideBehindScripted","isInRemainsCollector","isInstructorFigureEnabled","isIRLaserOn","isKeyActive","isKindOf","isLaserOn","isLightOn","isLocalized","isManualFire","isMarkedForCollection","isMultiplayer","isMultiplayerSolo","isNil","isNotEqualTo","isNull","isNumber","isObjectHidden","isObjectRTD","isOnRoad","isPiPEnabled","isPlayer","isRealTime","isRemoteExecuted","isRemoteExecutedJIP","isSensorTargetConfirmed","isServer","isShowing3DIcons","isSimpleObject","isSprintAllowed","isStaminaEnabled","isSteamMission","isStreamFriendlyUIEnabled","isStressDamageEnabled","isText","isTouchingGround","isTurnedOut","isTutHintsEnabled","isUAVConnectable","isUAVConnected","isUIContext","isUniformAllowed","isVehicleCargo","isVehicleRadarOn","isVehicleSensorEnabled","isWalking","isWeaponDeployed","isWeaponRested","itemCargo","items","itemsWithMagazines","join","joinAs","joinAsSilent","joinSilent","joinString","kbAddDatabase","kbAddDatabaseTargets","kbAddTopic","kbHasTopic","kbReact","kbRemoveTopic","kbTell","kbWasSaid","keyImage","keyName","keys","knowsAbout","land","landAt","landResult","language","laserTarget","lbAdd","lbClear","lbColor","lbColorRight","lbCurSel","lbData","lbDelete","lbIsSelected","lbPicture","lbPictureRight","lbSelection","lbSetColor","lbSetColorRight","lbSetCurSel","lbSetData","lbSetPicture","lbSetPictureColor","lbSetPictureColorDisabled","lbSetPictureColorSelected","lbSetPictureRight","lbSetPictureRightColor","lbSetPictureRightColorDisabled","lbSetPictureRightColorSelected","lbSetSelectColor","lbSetSelectColorRight","lbSetSelected","lbSetText","lbSetTextRight","lbSetTooltip","lbSetValue","lbSize","lbSort","lbSortByValue","lbText","lbTextRight","lbValue","leader","leaderboardDeInit","leaderboardGetRows","leaderboardInit","leaderboardRequestRowsFriends","leaderboardRequestRowsGlobal","leaderboardRequestRowsGlobalAroundUser","leaderboardsRequestUploadScore","leaderboardsRequestUploadScoreKeepBest","leaderboardState","leaveVehicle","libraryCredits","libraryDisclaimers","lifeState","lightAttachObject","lightDetachObject","lightIsOn","lightnings","limitSpeed","linearConversion","lineIntersects","lineIntersectsObjs","lineIntersectsSurfaces","lineIntersectsWith","linkItem","list","listObjects","listRemoteTargets","listVehicleSensors","ln","lnbAddArray","lnbAddColumn","lnbAddRow","lnbClear","lnbColor","lnbColorRight","lnbCurSelRow","lnbData","lnbDeleteColumn","lnbDeleteRow","lnbGetColumnsPosition","lnbPicture","lnbPictureRight","lnbSetColor","lnbSetColorRight","lnbSetColumnsPos","lnbSetCurSelRow","lnbSetData","lnbSetPicture","lnbSetPictureColor","lnbSetPictureColorRight","lnbSetPictureColorSelected","lnbSetPictureColorSelectedRight","lnbSetPictureRight","lnbSetText","lnbSetTextRight","lnbSetTooltip","lnbSetValue","lnbSize","lnbSort","lnbSortByValue","lnbText","lnbTextRight","lnbValue","load","loadAbs","loadBackpack","loadFile","loadGame","loadIdentity","loadMagazine","loadOverlay","loadStatus","loadUniform","loadVest","local","localize","localNamespace","locationPosition","lock","lockCameraTo","lockCargo","lockDriver","locked","lockedCargo","lockedDriver","lockedInventory","lockedTurret","lockIdentity","lockInventory","lockTurret","lockWP","log","logEntities","logNetwork","logNetworkTerminate","lookAt","lookAtPos","magazineCargo","magazines","magazinesAllTurrets","magazinesAmmo","magazinesAmmoCargo","magazinesAmmoFull","magazinesDetail","magazinesDetailBackpack","magazinesDetailUniform","magazinesDetailVest","magazinesTurret","magazineTurretAmmo","mapAnimAdd","mapAnimClear","mapAnimCommit","mapAnimDone","mapCenterOnCamera","mapGridPosition","markAsFinishedOnSteam","markerAlpha","markerBrush","markerChannel","markerColor","markerDir","markerPolyline","markerPos","markerShadow","markerShape","markerSize","markerText","markerType","matrixMultiply","matrixTranspose","max","members","menuAction","menuAdd","menuChecked","menuClear","menuCollapse","menuData","menuDelete","menuEnable","menuEnabled","menuExpand","menuHover","menuPicture","menuSetAction","menuSetCheck","menuSetData","menuSetPicture","menuSetShortcut","menuSetText","menuSetURL","menuSetValue","menuShortcut","menuShortcutText","menuSize","menuSort","menuText","menuURL","menuValue","merge","min","mineActive","mineDetectedBy","missileTarget","missileTargetPos","missionConfigFile","missionDifficulty","missionName","missionNameSource","missionNamespace","missionStart","missionVersion","mod","modelToWorld","modelToWorldVisual","modelToWorldVisualWorld","modelToWorldWorld","modParams","moonIntensity","moonPhase","morale","move","move3DENCamera","moveInAny","moveInCargo","moveInCommander","moveInDriver","moveInGunner","moveInTurret","moveObjectToEnd","moveOut","moveTarget","moveTime","moveTo","moveToCompleted","moveToFailed","musicVolume","name","namedProperties","nameSound","nearEntities","nearestBuilding","nearestLocation","nearestLocations","nearestLocationWithDubbing","nearestObject","nearestObjects","nearestTerrainObjects","nearObjects","nearObjectsReady","nearRoads","nearSupplies","nearTargets","needReload","netId","netObjNull","newOverlay","nextMenuItemIndex","nextWeatherChange","nMenuItems","not","numberOfEnginesRTD","numberToDate","object","objectCurators","objectFromNetId","objectParent","objStatus","onBriefingGear","onBriefingGroup","onBriefingNotes","onBriefingPlan","onBriefingTeamSwitch","onCommandModeChanged","onDoubleClick","onEachFrame","onGroupIconClick","onGroupIconOverEnter","onGroupIconOverLeave","onHCGroupSelectionChanged","onMapSingleClick","onPlayerConnected","onPlayerDisconnected","onPreloadFinished","onPreloadStarted","onShowNewObject","onTeamSwitch","openCuratorInterface","openDLCPage","openDSInterface","openGPS","openMap","openSteamApp","openYoutubeVideo","or","orderGetIn","overcast","overcastForecast","owner","param","params","parseNumber","parseSimpleArray","parseText","parsingNamespace","particlesQuality","periscopeElevation","pickWeaponPool","pitch","pixelGrid","pixelGridBase","pixelGridNoUIScale","pixelH","pixelW","playableSlotsNumber","playableUnits","playAction","playActionNow","player","playerRespawnTime","playerSide","playersNumber","playGesture","playMission","playMove","playMoveNow","playMusic","playScriptedMission","playSound","playSound3D","position","positionCameraToWorld","posScreenToWorld","posWorldToScreen","ppEffectAdjust","ppEffectCommit","ppEffectCommitted","ppEffectCreate","ppEffectDestroy","ppEffectEnable","ppEffectEnabled","ppEffectForceInNVG","precision","preloadCamera","preloadObject","preloadSound","preloadTitleObj","preloadTitleRsc","preprocessFile","preprocessFileLineNumbers","primaryWeapon","primaryWeaponItems","primaryWeaponMagazine","priority","processDiaryLink","processInitCommands","productVersion","profileName","profileNamespace","profileNameSteam","progressLoadingScreen","progressPosition","progressSetPosition","publicVariable","publicVariableClient","publicVariableServer","pushBack","pushBackUnique","putWeaponPool","queryItemsPool","queryMagazinePool","queryWeaponPool","rad","radioChannelAdd","radioChannelCreate","radioChannelInfo","radioChannelRemove","radioChannelSetCallSign","radioChannelSetLabel","radioVolume","rain","rainbow","random","rank","rankId","rating","rectangular","registeredTasks","registerTask","reload","reloadEnabled","remoteControl","remoteExec","remoteExecCall","remoteExecutedOwner","remove3DENConnection","remove3DENEventHandler","remove3DENLayer","removeAction","removeAll3DENEventHandlers","removeAllActions","removeAllAssignedItems","removeAllBinocularItems","removeAllContainers","removeAllCuratorAddons","removeAllCuratorCameraAreas","removeAllCuratorEditingAreas","removeAllEventHandlers","removeAllHandgunItems","removeAllItems","removeAllItemsWithMagazines","removeAllMissionEventHandlers","removeAllMPEventHandlers","removeAllMusicEventHandlers","removeAllOwnedMines","removeAllPrimaryWeaponItems","removeAllSecondaryWeaponItems","removeAllWeapons","removeBackpack","removeBackpackGlobal","removeBinocularItem","removeClothing","removeCuratorAddons","removeCuratorCameraArea","removeCuratorEditableObjects","removeCuratorEditingArea","removeDiaryRecord","removeDiarySubject","removeDrawIcon","removeDrawLinks","removeEventHandler","removeFromRemainsCollector","removeGoggles","removeGroupIcon","removeHandgunItem","removeHeadgear","removeItem","removeItemFromBackpack","removeItemFromUniform","removeItemFromVest","removeItems","removeMagazine","removeMagazineGlobal","removeMagazines","removeMagazinesTurret","removeMagazineTurret","removeMenuItem","removeMissionEventHandler","removeMPEventHandler","removeMusicEventHandler","removeOwnedMine","removePrimaryWeaponItem","removeSecondaryWeaponItem","removeSimpleTask","removeSwitchableUnit","removeTeamMember","removeUniform","removeVest","removeWeapon","removeWeaponAttachmentCargo","removeWeaponCargo","removeWeaponGlobal","removeWeaponTurret","reportRemoteTarget","requiredVersion","resetCamShake","resetSubgroupDirection","resize","resources","respawnVehicle","restartEditorCamera","reveal","revealMine","reverse","reversedMouseY","roadAt","roadsConnectedTo","roleDescription","ropeAttachedObjects","ropeAttachedTo","ropeAttachEnabled","ropeAttachTo","ropeCreate","ropeCut","ropeDestroy","ropeDetach","ropeEndPosition","ropeLength","ropes","ropeSegments","ropeSetCargoMass","ropeUnwind","ropeUnwound","rotorsForcesRTD","rotorsRpmRTD","round","runInitScript","safeZoneH","safeZoneW","safeZoneWAbs","safeZoneX","safeZoneXAbs","safeZoneY","save3DENInventory","saveGame","saveIdentity","saveJoysticks","saveOverlay","saveProfileNamespace","saveStatus","saveVar","savingEnabled","say","say2D","say3D","scopeName","score","scoreSide","screenshot","screenToWorld","scriptDone","scriptName","scudState","secondaryWeapon","secondaryWeaponItems","secondaryWeaponMagazine","select","selectBestPlaces","selectDiarySubject","selectedEditorObjects","selectEditorObject","selectionNames","selectionPosition","selectLeader","selectMax","selectMin","selectNoPlayer","selectPlayer","selectRandom","selectRandomWeighted","selectWeapon","selectWeaponTurret","sendAUMessage","sendSimpleCommand","sendTask","sendTaskResult","sendUDPMessage","serverCommand","serverCommandAvailable","serverCommandExecutable","serverName","serverTime","set","set3DENAttribute","set3DENAttributes","set3DENGrid","set3DENIconsVisible","set3DENLayer","set3DENLinesVisible","set3DENLogicType","set3DENMissionAttribute","set3DENMissionAttributes","set3DENModelsVisible","set3DENObjectType","set3DENSelected","setAccTime","setActualCollectiveRTD","setAirplaneThrottle","setAirportSide","setAmmo","setAmmoCargo","setAmmoOnPylon","setAnimSpeedCoef","setAperture","setApertureNew","setAPURTD","setArmoryPoints","setAttributes","setAutonomous","setBatteryChargeRTD","setBatteryRTD","setBehaviour","setBehaviourStrong","setBleedingRemaining","setBrakesRTD","setCameraEffect","setCameraInterest","setCamShakeDefParams","setCamShakeParams","setCamUseTI","setCaptive","setCenterOfMass","setCollisionLight","setCombatBehaviour","setCombatMode","setCompassOscillation","setConvoySeparation","setCuratorCameraAreaCeiling","setCuratorCoef","setCuratorEditingAreaType","setCuratorWaypointCost","setCurrentChannel","setCurrentTask","setCurrentWaypoint","setCustomAimCoef","setCustomMissionData","setCustomSoundController","setCustomWeightRTD","setDamage","setDammage","setDate","setDebriefingText","setDefaultCamera","setDestination","setDetailMapBlendPars","setDiaryRecordText","setDiarySubjectPicture","setDir","setDirection","setDrawIcon","setDriveOnPath","setDropInterval","setDynamicSimulationDistance","setDynamicSimulationDistanceCoef","setEditorMode","setEditorObjectScope","setEffectCondition","setEffectiveCommander","setEngineRPMRTD","setEngineRpmRTD","setFace","setFaceAnimation","setFatigue","setFeatureType","setFlagAnimationPhase","setFlagOwner","setFlagSide","setFlagTexture","setFog","setForceGeneratorRTD","setFormation","setFormationTask","setFormDir","setFriend","setFromEditor","setFSMVariable","setFuel","setFuelCargo","setGroupIcon","setGroupIconParams","setGroupIconsSelectable","setGroupIconsVisible","setGroupId","setGroupIdGlobal","setGroupOwner","setGusts","setHideBehind","setHit","setHitIndex","setHitPointDamage","setHorizonParallaxCoef","setHUDMovementLevels","setIdentity","setImportance","setInfoPanel","setLeader","setLightAmbient","setLightAttenuation","setLightBrightness","setLightColor","setLightDayLight","setLightFlareMaxDistance","setLightFlareSize","setLightIntensity","setLightnings","setLightUseFlare","setLocalWindParams","setMagazineTurretAmmo","setMarkerAlpha","setMarkerAlphaLocal","setMarkerBrush","setMarkerBrushLocal","setMarkerColor","setMarkerColorLocal","setMarkerDir","setMarkerDirLocal","setMarkerPolyline","setMarkerPolylineLocal","setMarkerPos","setMarkerPosLocal","setMarkerShadow","setMarkerShadowLocal","setMarkerShape","setMarkerShapeLocal","setMarkerSize","setMarkerSizeLocal","setMarkerText","setMarkerTextLocal","setMarkerType","setMarkerTypeLocal","setMass","setMimic","setMissileTarget","setMissileTargetPos","setMousePosition","setMusicEffect","setMusicEventHandler","setName","setNameSound","setObjectArguments","setObjectMaterial","setObjectMaterialGlobal","setObjectProxy","setObjectScale","setObjectTexture","setObjectTextureGlobal","setObjectViewDistance","setOvercast","setOwner","setOxygenRemaining","setParticleCircle","setParticleClass","setParticleFire","setParticleParams","setParticleRandom","setPilotCameraDirection","setPilotCameraRotation","setPilotCameraTarget","setPilotLight","setPiPEffect","setPitch","setPlateNumber","setPlayable","setPlayerRespawnTime","setPlayerVoNVolume","setPos","setPosASL","setPosASL2","setPosASLW","setPosATL","setPosition","setPosWorld","setPylonLoadout","setPylonsPriority","setRadioMsg","setRain","setRainbow","setRandomLip","setRank","setRectangular","setRepairCargo","setRotorBrakeRTD","setShadowDistance","setShotParents","setSide","setSimpleTaskAlwaysVisible","setSimpleTaskCustomData","setSimpleTaskDescription","setSimpleTaskDestination","setSimpleTaskTarget","setSimpleTaskType","setSimulWeatherLayers","setSize","setSkill","setSlingLoad","setSoundEffect","setSpeaker","setSpeech","setSpeedMode","setStamina","setStaminaScheme","setStarterRTD","setStatValue","setSuppression","setSystemOfUnits","setTargetAge","setTaskMarkerOffset","setTaskResult","setTaskState","setTerrainGrid","setText","setThrottleRTD","setTimeMultiplier","setTitleEffect","setToneMapping","setToneMappingParams","setTrafficDensity","setTrafficDistance","setTrafficGap","setTrafficSpeed","setTriggerActivation","setTriggerArea","setTriggerInterval","setTriggerStatements","setTriggerText","setTriggerTimeout","setTriggerType","setType","setUnconscious","setUnitAbility","setUnitCombatMode","setUnitLoadout","setUnitPos","setUnitPosWeak","setUnitRank","setUnitRecoilCoefficient","setUnitTrait","setUnloadInCombat","setUserActionText","setUserMFDText","setUserMFDValue","setVariable","setVectorDir","setVectorDirAndUp","setVectorUp","setVehicleAmmo","setVehicleAmmoDef","setVehicleArmor","setVehicleCargo","setVehicleId","setVehicleInit","setVehicleLock","setVehiclePosition","setVehicleRadar","setVehicleReceiveRemoteTargets","setVehicleReportOwnPosition","setVehicleReportRemoteTargets","setVehicleTIPars","setVehicleVarName","setVelocity","setVelocityModelSpace","setVelocityTransformation","setViewDistance","setVisibleIfTreeCollapsed","setWantedRPMRTD","setWaves","setWaypointBehaviour","setWaypointCombatMode","setWaypointCompletionRadius","setWaypointDescription","setWaypointForceBehaviour","setWaypointFormation","setWaypointHousePosition","setWaypointLoiterAltitude","setWaypointLoiterRadius","setWaypointLoiterType","setWaypointName","setWaypointPosition","setWaypointScript","setWaypointSpeed","setWaypointStatements","setWaypointTimeout","setWaypointType","setWaypointVisible","setWeaponReloadingTime","setWeaponZeroing","setWind","setWindDir","setWindForce","setWindStr","setWingForceScaleRTD","setWPPos","show3DIcons","showChat","showCinemaBorder","showCommandingMenu","showCompass","showCuratorCompass","showGPS","showHUD","showLegend","showMap","shownArtilleryComputer","shownChat","shownCompass","shownCuratorCompass","showNewEditorObject","shownGPS","shownHUD","shownMap","shownPad","shownRadio","shownScoretable","shownUAVFeed","shownWarrant","shownWatch","showPad","showRadio","showScoretable","showSubtitles","showUAVFeed","showWarrant","showWatch","showWaypoint","showWaypoints","side","sideChat","sideEmpty","sideEnemy","sideFriendly","sideRadio","simpleTasks","simulationEnabled","simulCloudDensity","simulCloudOcclusion","simulInClouds","simulSetHumidity","simulWeatherSync","sin","size","sizeOf","skill","skillFinal","skipTime","sleep","sliderPosition","sliderRange","sliderSetPosition","sliderSetRange","sliderSetSpeed","sliderSpeed","slingLoadAssistantShown","soldierMagazines","someAmmo","sort","soundVolume","spawn","speaker","speechVolume","speed","speedMode","splitString","sqrt","squadParams","stance","startLoadingScreen","step","stop","stopEngineRTD","stopped","str","sunOrMoon","supportInfo","suppressFor","surfaceIsWater","surfaceNormal","surfaceTexture","surfaceType","swimInDepth","switchableUnits","switchAction","switchCamera","switchGesture","switchLight","switchMove","synchronizedObjects","synchronizedTriggers","synchronizedWaypoints","synchronizeObjectsAdd","synchronizeObjectsRemove","synchronizeTrigger","synchronizeWaypoint","systemChat","systemOfUnits","systemTime","systemTimeUTC","tan","targetKnowledge","targets","targetsAggregate","targetsQuery","taskAlwaysVisible","taskChildren","taskCompleted","taskCustomData","taskDescription","taskDestination","taskHint","taskMarkerOffset","taskName","taskParent","taskResult","taskState","taskType","teamMember","teamName","teams","teamSwitch","teamSwitchEnabled","teamType","terminate","terrainIntersect","terrainIntersectASL","terrainIntersectAtASL","text","textLog","textLogFormat","tg","throttleRTD","time","timeMultiplier","titleCut","titleFadeOut","titleObj","titleRsc","titleText","toArray","toFixed","toLower","toLowerANSI","toString","toUpper","toUpperANSI","triggerActivated","triggerActivation","triggerAmmo","triggerArea","triggerAttachedVehicle","triggerAttachObject","triggerAttachVehicle","triggerDynamicSimulation","triggerInterval","triggerStatements","triggerText","triggerTimeout","triggerTimeoutCurrent","triggerType","trim","turretLocal","turretOwner","turretUnit","tvAdd","tvClear","tvCollapse","tvCollapseAll","tvCount","tvCurSel","tvData","tvDelete","tvExpand","tvExpandAll","tvIsSelected","tvPicture","tvPictureRight","tvSelection","tvSetColor","tvSetCurSel","tvSetData","tvSetPicture","tvSetPictureColor","tvSetPictureColorDisabled","tvSetPictureColorSelected","tvSetPictureRight","tvSetPictureRightColor","tvSetPictureRightColorDisabled","tvSetPictureRightColorSelected","tvSetSelectColor","tvSetSelected","tvSetText","tvSetTooltip","tvSetValue","tvSort","tvSortAll","tvSortByValue","tvSortByValueAll","tvText","tvTooltip","tvValue","type","typeName","typeOf","UAVControl","uiNamespace","uiSleep","unassignCurator","unassignItem","unassignTeam","unassignVehicle","underwater","uniform","uniformContainer","uniformItems","uniformMagazines","unitAddons","unitAimPosition","unitAimPositionVisual","unitBackpack","unitCombatMode","unitIsUAV","unitPos","unitReady","unitRecoilCoefficient","units","unitsBelowHeight","unitTurret","unlinkItem","unlockAchievement","unregisterTask","updateDrawIcon","updateMenuItem","updateObjectTree","useAIOperMapObstructionTest","useAISteeringComponent","useAudioTimeForMoves","userInputDisabled","vectorAdd","vectorCos","vectorCrossProduct","vectorDiff","vectorDir","vectorDirVisual","vectorDistance","vectorDistanceSqr","vectorDotProduct","vectorFromTo","vectorLinearConversion","vectorMagnitude","vectorMagnitudeSqr","vectorModelToWorld","vectorModelToWorldVisual","vectorMultiply","vectorNormalized","vectorUp","vectorUpVisual","vectorWorldToModel","vectorWorldToModelVisual","vehicle","vehicleCargoEnabled","vehicleChat","vehicleMoveInfo","vehicleRadio","vehicleReceiveRemoteTargets","vehicleReportOwnPosition","vehicleReportRemoteTargets","vehicles","vehicleVarName","velocity","velocityModelSpace","verifySignature","vest","vestContainer","vestItems","vestMagazines","viewDistance","visibleCompass","visibleGPS","visibleMap","visiblePosition","visiblePositionASL","visibleScoretable","visibleWatch","waves","waypointAttachedObject","waypointAttachedVehicle","waypointAttachObject","waypointAttachVehicle","waypointBehaviour","waypointCombatMode","waypointCompletionRadius","waypointDescription","waypointForceBehaviour","waypointFormation","waypointHousePosition","waypointLoiterAltitude","waypointLoiterRadius","waypointLoiterType","waypointName","waypointPosition","waypoints","waypointScript","waypointsEnabledUAV","waypointShow","waypointSpeed","waypointStatements","waypointTimeout","waypointTimeoutCurrent","waypointType","waypointVisible","weaponAccessories","weaponAccessoriesCargo","weaponCargo","weaponDirection","weaponInertia","weaponLowered","weapons","weaponsItems","weaponsItemsCargo","weaponState","weaponsTurret","weightRTD","WFSideText","wind","windDir","windRTD","windStr","wingsForcesRTD","worldName","worldSize","worldToModel","worldToModelVisual","worldToScreen"],
literal:["blufor","civilian","configNull","controlNull","displayNull","east","endl","false","grpNull","independent","lineBreak","locationNull","nil","objNull","opfor","pi","resistance","scriptNull","sideAmbientLife","sideEmpty","sideLogic","sideUnknown","taskNull","teamMemberNull","true","west"]
},contains:[e.C_LINE_COMMENT_MODE,e.C_BLOCK_COMMENT_MODE,e.NUMBER_MODE,{
className:"variable",begin:/\b_+[a-zA-Z]\w*/},{className:"title",
begin:/[a-zA-Z]\w+_fnc_\w+/},t,a],illegal:/#|^\$ /}}})()
;hljs.registerLanguage("sqf",e)})();