/*! `leaf` grammar compiled for Highlight.js 11.4.0 */
(()=>{var e=(()=>{"use strict";return e=>({name:"Leaf",contains:[{
className:"function",begin:"#+[A-Za-z_0-9]*\\(",end:/ \{/,returnBegin:!0,
excludeEnd:!0,contains:[{className:"keyword",begin:"#+"},{className:"title",
begin:"[A-Za-z_][A-Za-z_0-9]*"},{className:"params",begin:"\\(",end:"\\)",
endsParent:!0,contains:[{className:"string",begin:'"',end:'"'},{
className:"variable",begin:"[A-Za-z_][A-Za-z_0-9]*"}]}]}]})})()
;hljs.registerLanguage("leaf",e)})();