/*! `livescript` grammar compiled for Highlight.js 11.4.0 */
(()=>{var e=(()=>{"use strict"
;const e=["as","in","of","if","for","while","finally","var","new","function","do","return","void","else","break","catch","instanceof","with","throw","case","default","try","switch","continue","typeof","delete","let","yield","const","class","debugger","async","await","static","import","from","export","extends"],n=["true","false","null","undefined","NaN","Infinity"],a=[].concat(["setInterval","setTimeout","clearInterval","clearTimeout","require","exports","eval","isFinite","isNaN","parseFloat","parseInt","decodeURI","decodeURIComponent","encodeURI","encodeURIComponent","escape","unescape"],["Object","Function","Boolean","Symbol","Math","Date","Number","BigInt","String","RegExp","Array","Float32Array","Float64Array","Int8Array","Uint8Array","Uint8ClampedArray","Int16Array","Int32Array","Uint16Array","Uint32Array","BigInt64Array","BigUint64Array","Set","Map","WeakSet","WeakMap","ArrayBuffer","SharedArrayBuffer","Atomics","DataView","JSON","Promise","Generator","GeneratorFunction","AsyncFunction","Reflect","Proxy","Intl","WebAssembly"],["Error","EvalError","InternalError","RangeError","ReferenceError","SyntaxError","TypeError","URIError"])
;return t=>{const r={
keyword:e.concat(["then","unless","until","loop","of","by","when","and","or","is","isnt","not","it","that","otherwise","from","to","til","fallthrough","case","enum","native","list","map","__hasProp","__extends","__slice","__bind","__indexOf"]),
literal:n.concat(["yes","no","on","off","it","that","void"]),
built_in:a.concat(["npm","print"])
},s="[A-Za-z$_](?:-[0-9A-Za-z$_]|[0-9A-Za-z$_])*",i=t.inherit(t.TITLE_MODE,{
begin:s}),o={className:"subst",begin:/#\{/,end:/\}/,keywords:r},c={
className:"subst",begin:/#[A-Za-z$_]/,end:/(?:-[0-9A-Za-z$_]|[0-9A-Za-z$_])*/,
keywords:r},l=[t.BINARY_NUMBER_MODE,{className:"number",
begin:"(\\b0[xX][a-fA-F0-9_]+)|(\\b\\d(\\d|_\\d)*(\\.(\\d(\\d|_\\d)*)?)?(_*[eE]([-+]\\d(_\\d|\\d)*)?)?[_a-z]*)",
relevance:0,starts:{end:"(\\s*/)?",relevance:0}},{className:"string",variants:[{
begin:/'''/,end:/'''/,contains:[t.BACKSLASH_ESCAPE]},{begin:/'/,end:/'/,
contains:[t.BACKSLASH_ESCAPE]},{begin:/"""/,end:/"""/,
contains:[t.BACKSLASH_ESCAPE,o,c]},{begin:/"/,end:/"/,
contains:[t.BACKSLASH_ESCAPE,o,c]},{begin:/\\/,end:/(\s|$)/,excludeEnd:!0}]},{
className:"regexp",variants:[{begin:"//",end:"//[gim]*",
contains:[o,t.HASH_COMMENT_MODE]},{
begin:/\/(?![ *])(\\.|[^\\\n])*?\/[gim]*(?=\W)/}]},{begin:"@"+s},{begin:"``",
end:"``",excludeBegin:!0,excludeEnd:!0,subLanguage:"javascript"}];o.contains=l
;const d={className:"params",begin:"\\(",returnBegin:!0,contains:[{begin:/\(/,
end:/\)/,keywords:r,contains:["self"].concat(l)}]},g={variants:[{
match:[/class\s+/,s,/\s+extends\s+/,s]},{match:[/class\s+/,s]}],scope:{
2:"title.class",4:"title.class.inherited"},keywords:r};return{name:"LiveScript",
aliases:["ls"],keywords:r,illegal:/\/\*/,
contains:l.concat([t.COMMENT("\\/\\*","\\*\\/"),t.HASH_COMMENT_MODE,{
begin:"(#=>|=>|\\|>>|-?->|!->)"},{className:"function",contains:[i,d],
returnBegin:!0,variants:[{
begin:"("+s+"\\s*(?:=|:=)\\s*)?(\\(.*\\)\\s*)?\\B->\\*?",end:"->\\*?"},{
begin:"("+s+"\\s*(?:=|:=)\\s*)?!?(\\(.*\\)\\s*)?\\B[-~]{1,2}>\\*?",
end:"[-~]{1,2}>\\*?"},{
begin:"("+s+"\\s*(?:=|:=)\\s*)?(\\(.*\\)\\s*)?\\B!?[-~]{1,2}>\\*?",
end:"!?[-~]{1,2}>\\*?"}]},g,{begin:s+":",end:":",returnBegin:!0,returnEnd:!0,
relevance:0}])}}})();hljs.registerLanguage("livescript",e)})();