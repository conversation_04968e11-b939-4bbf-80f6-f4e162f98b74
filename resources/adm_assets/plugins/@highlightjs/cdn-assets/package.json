{"name": "@highlightjs/cdn-assets", "description": "Syntax highlighting with language autodetection. (pre-compiled CDN assets)", "keywords": ["highlight", "syntax"], "homepage": "https://highlightjs.org/", "version": "11.4.0", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "contributors": ["<PERSON> <<EMAIL>>", "<PERSON><PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON><PERSON> <<EMAIL>>", "<PERSON><PERSON><PERSON> <<EMAIL>>", "<PERSON> <****************>", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>"], "bugs": {"url": "https://github.com/highlightjs/highlight.js/issues"}, "license": "BSD-3-<PERSON><PERSON>", "repository": {"type": "git", "url": "git://github.com/highlightjs/highlight.js.git"}, "scripts": {"mocha": "mocha", "lint": "eslint src/*.js src/lib/*.js demo/*.js tools/**/*.js --ignore-pattern vendor", "lint-languages": "eslint --no-eslintrc -c .eslintrc.lang.js src/languages/**/*.js", "build_and_test": "npm run build && npm run test", "build_and_test_browser": "npm run build-browser && npm run test-browser", "build": "node ./tools/build.js -t node", "build-cdn": "node ./tools/build.js -t cdn", "build-browser": "node ./tools/build.js -t browser :common", "test": "mocha test", "test-markup": "mocha test/markup", "test-detect": "mocha test/detect", "test-browser": "mocha test/browser", "test-parser": "mocha test/parser"}, "engines": {"node": ">=12.0.0"}, "devDependencies": {"@rollup/plugin-commonjs": "^21.0.0", "@rollup/plugin-json": "^4.1.0", "@rollup/plugin-node-resolve": "^13.0.0", "@types/mocha": "^9.0.0", "@typescript-eslint/eslint-plugin": "^4.23.0", "@typescript-eslint/parser": "^4.23.0", "clean-css": "^5.0.1", "cli-table": "^0.3.1", "colors": "^1.1.2", "commander": "8.2", "css": "^3.0.0", "css-color-names": "^1.0.1", "deep-freeze-es6": "^1.4.1", "del": "^6.0.0", "dependency-resolver": "^2.0.1", "eslint": "^7.26.0", "eslint-config-standard": "^16.0.1", "eslint-plugin-import": "^2.25.2", "eslint-plugin-node": "^11.1.0", "eslint-plugin-promise": "^5.1.0", "glob": "^7.1.7", "glob-promise": "^4.2.1", "handlebars": "^4.7.6", "jsdom": "^18.0.0", "lodash": "^4.17.20", "mocha": "^9.1.3", "refa": "^0.4.1", "rollup": "^2.47.0", "should": "^13.2.3", "terser": "^5.7.0", "tiny-worker": "^2.3.0", "typescript": "^4.4.4", "wcag-contrast": "^3.0.0"}}