/*!
  Theme: Horizon Light
  Author: <PERSON><PERSON><PERSON> (http://github.com/mi<PERSON><PERSON>-ball/)
  License: ~ MIT (or more permissive) [via base16-schemes-source]
  Maintainer: @highlightjs/core-team
  Version: 2021.09.0
*/pre code.hljs{display:block;overflow-x:auto;padding:1em}code.hljs{padding:3px 5px}.hljs{color:#403c3d;background:#fdf0ed}.hljs ::selection,.hljs::selection{background-color:#f9cbbe;color:#403c3d}.hljs-comment{color:#bdb3b1}.hljs-tag{color:#948c8a}.hljs-operator,.hljs-punctuation,.hljs-subst{color:#403c3d}.hljs-operator{opacity:.7}.hljs-bullet,.hljs-deletion,.hljs-name,.hljs-selector-tag,.hljs-template-variable,.hljs-variable{color:#e95678}.hljs-attr,.hljs-link,.hljs-literal,.hljs-number,.hljs-symbol,.hljs-variable.constant_{color:#f9cec3}.hljs-class .hljs-title,.hljs-title,.hljs-title.class_{color:#fadad1}.hljs-strong{font-weight:700;color:#fadad1}.hljs-addition,.hljs-code,.hljs-string,.hljs-title.class_.inherited__{color:#29d398}.hljs-built_in,.hljs-doctag,.hljs-keyword.hljs-atrule,.hljs-quote,.hljs-regexp{color:#59e1e3}.hljs-attribute,.hljs-function .hljs-title,.hljs-section,.hljs-title.function_,.ruby .hljs-property{color:#26bbd9}.diff .hljs-meta,.hljs-keyword,.hljs-template-tag,.hljs-type{color:#ee64ac}.hljs-emphasis{color:#ee64ac;font-style:italic}.hljs-meta,.hljs-meta .hljs-keyword,.hljs-meta .hljs-string{color:#f9cbbe}.hljs-meta .hljs-keyword,.hljs-meta-keyword{font-weight:700}